import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X } from 'lucide-react';
import { motion } from 'framer-motion';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navLinks = [
    { name: 'Features', path: '/features' },
    { name: 'AI Agents', path: '/agents' },
    { name: 'Advantages', path: '/advantages' },
    { name: 'Pricing', path: '/pricing' },
    { name: 'About', path: '/about' },
    { name: 'Contact Us', path: '/contact' },
    { name: 'Blog', path: '/blog' }
  ];

  return (
    <motion.nav
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      className={`fixed w-full z-50 transition-all duration-300 ${isScrolled ? 'bg-[var(--background)]/95 backdrop-blur-sm shadow-lg' : ''
        }`}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-20">
          <Link to="/" className="text-2xl font-bold gradient-text">
            <div className="flex flex-row items-center">
              <img src="../favicon-agent.svg" alt="Logo" className="mr-2 h-12 w-12 flex items-center animate-bounce" />
              siliconagent.ai
            </div>
          </Link>

          <div className="hidden md:flex items-center space-x-8">
            {navLinks.map((link) => (
              <Link
                key={link.name}
                to={link.path}
                className={`text-sm font-medium transition-colors hover:text-[var(--primary)] ${location.pathname === link.path ? 'text-[var(--primary)]' : 'text-gray-300'
                  }`}
              >
                {link.name}
              </Link>
            ))}
            {/* <Link to="/signin" className="text-sm font-medium text-gray-300 hover:text-white">
              Sign In
            </Link>
            <Link to="/signup" className="btn-primary">
              Get Started
            </Link> */}
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden text-gray-300 hover:text-white"
            onClick={() => setIsOpen(!isOpen)}
          >
            {isOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="md:hidden pb-6"
          >
            {navLinks.map((link) => (
              <Link
                key={link.name}
                to={link.path}
                className={`block py-2 text-sm font-medium ${location.pathname === link.path ? 'text-[var(--primary)]' : 'text-gray-300'
                  }`}
                onClick={() => setIsOpen(false)}
              >
                {link.name}
              </Link>
            ))}
            {/* <div className="mt-4 space-y-2">
              <Link
                to="/signin"
                className="block w-full text-center py-2 text-sm font-medium text-gray-300"
              >
                Sign In
              </Link>
              <Link to="/signup" className="block w-full btn-primary">
                Get Started
              </Link>
            </div> */}
          </motion.div>
        )}
      </div>
    </motion.nav>
  );
};

export default Navbar;