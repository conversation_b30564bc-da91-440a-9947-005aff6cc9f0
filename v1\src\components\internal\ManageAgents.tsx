import React from 'react';
    import { agentsData } from './mockData/manageAgentsData';
    import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Loader2, Al<PERSON><PERSON>riangle } from 'lucide-react';

    const ManageAgents: React.FC = () => {
      return (
        <div className="p-6 bg-gray-900 text-white rounded-lg shadow-md">
          <h1 className="text-3xl font-bold mb-6">Manage AI Agents</h1>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-700">
              <thead className="bg-gray-800">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">ID</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Name</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Type</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Last Active</th>
                </tr>
              </thead>
              <tbody className="bg-gray-900 divide-y divide-gray-700">
                {agentsData.map((agent) => (
                  <tr key={agent.id} className="hover:bg-gray-800">
                    <td className="px-4 py-4 whitespace-nowrap">{agent.id}</td>
                    <td className="px-4 py-4 whitespace-nowrap">{agent.name}</td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      {agent.status === 'active' && <CheckCircle className="text-green-500 inline-block mr-1" />}
                      {agent.status === 'idle' && <Loader2 className="text-yellow-500 inline-block mr-1 animate-spin" />}
                      {agent.status === 'error' && <XCircle className="text-red-500 inline-block mr-1" />}
                      {agent.status}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">{agent.type}</td>
                    <td className="px-4 py-4 whitespace-nowrap">{agent.lastActive}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      );
    };

    export default ManageAgents;
