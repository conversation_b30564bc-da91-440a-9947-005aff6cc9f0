# Full-Stack Application: Express.js + React TypeScript

This project demonstrates a complete full-stack application with an Express.js backend and React TypeScript frontend, featuring seamless integration between both parts.

## Project Structure

```
v6/
├── backend/          # Express.js + TypeScript API server
│   ├── src/
│   │   ├── app.ts           # Express app configuration
│   │   ├── server.ts        # Server entry point
│   │   └── routes/          # API route handlers
│   │       ├── userRoutes.ts
│   │       └── taskRoutes.ts
│   ├── package.json
│   ├── tsconfig.json
│   └── .env
└── frontend/         # React + TypeScript + Vite
    ├── src/
    │   ├── components/      # React components
    │   │   ├── Dashboard.tsx
    │   │   ├── Users.tsx
    │   │   └── Tasks.tsx
    │   ├── services/        # API integration
    │   │   └── api.ts
    │   ├── App.tsx
    │   └── App.css
    ├── package.json
    └── .env
```

## Features

### Backend (Express.js + TypeScript)
- **RESTful API** with full CRUD operations
- **CORS configuration** for frontend integration
- **TypeScript** for type safety
- **Environment configuration** with dotenv
- **Error handling middleware**
- **Sample data** for users and tasks

### Frontend (React + TypeScript + Vite)
- **Modern React** with hooks and functional components
- **TypeScript** for type safety
- **Axios** for API communication
- **Responsive design** with CSS Grid and Flexbox
- **Real-time API status** monitoring
- **Complete CRUD interface** for users and tasks

### Integration Features
- **API service layer** with interceptors
- **Environment-based configuration**
- **Error handling** and loading states
- **Real-time data synchronization**

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### 1. Start the Backend Server

```bash
# Navigate to backend directory
cd backend

# Install dependencies
npm install

# Start development server
npm run dev
```

The backend server will start on `http://localhost:3001`

### 2. Start the Frontend Application

```bash
# Navigate to frontend directory (in a new terminal)
cd frontend

# Install dependencies
npm install

# Start development server
npm run dev
```

The frontend application will start on `http://localhost:5173`

## API Endpoints

### Users
- `GET /api/users` - Get all users
- `GET /api/users/:id` - Get user by ID
- `POST /api/users` - Create new user
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user

### Tasks
- `GET /api/tasks` - Get all tasks (with optional filters)
- `GET /api/tasks/:id` - Get task by ID
- `POST /api/tasks` - Create new task
- `PUT /api/tasks/:id` - Update task
- `DELETE /api/tasks/:id` - Delete task

### Query Parameters for Tasks
- `userId` - Filter tasks by user ID
- `completed` - Filter by completion status (true/false)

## Environment Variables

### Backend (.env)
```
PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:5173
```

### Frontend (.env)
```
VITE_API_URL=http://localhost:3001
```

## Available Scripts

### Backend
- `npm run dev` - Start development server with hot reload
- `npm run build` - Build TypeScript to JavaScript
- `npm start` - Start production server

### Frontend
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build

## Testing the Integration

1. **Start both servers** (backend on :3001, frontend on :5173)
2. **Open the frontend** in your browser
3. **Check the Dashboard** to verify API connectivity
4. **Test Users management** - create, edit, delete users
5. **Test Tasks management** - create, assign, complete tasks
6. **Use filters** to test query parameters

## Technologies Used

### Backend
- Express.js - Web framework
- TypeScript - Type safety
- CORS - Cross-origin resource sharing
- dotenv - Environment configuration

### Frontend
- React 18 - UI library
- TypeScript - Type safety
- Vite - Build tool and dev server
- Axios - HTTP client

## Next Steps

This foundation can be extended with:
- Database integration (PostgreSQL, MongoDB)
- Authentication and authorization
- Input validation and sanitization
- Unit and integration tests
- Docker containerization
- Deployment configuration

## Troubleshooting

### Common Issues

1. **CORS errors**: Make sure the backend FRONTEND_URL matches your frontend URL
2. **Connection refused**: Ensure the backend server is running on port 3001
3. **Module not found**: Run `npm install` in both directories
4. **Port conflicts**: Change ports in the .env files if needed

### Checking API Status
The Dashboard component includes an API health check that will show connection status and help diagnose issues.
