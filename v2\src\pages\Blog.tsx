import { motion } from 'framer-motion';

const blogPosts = [
  {
    id: 1,
    title: 'How AI Agents Are Revolutionizing Software Development',
    excerpt: 'Discover how AI-powered automation is transforming the way developers work and increasing productivity...',
    author: '<PERSON>',
    readTime: '4 min read',
    image: 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?auto=format&fit=crop&q=80&w=800',
    category: 'Development'
  },
  {
    id: 2,
    title: 'The Business Impact of AI Automation in 2025',
    excerpt: 'An in-depth analysis of how AI automation is driving business transformation and ROI...',
    author: '<PERSON>',
    readTime: '6 min read',
    image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?auto=format&fit=crop&q=80&w=800',
    category: 'Business'
  },
  {
    id: 3,
    title: 'Building Smart AI Agents for Enterprise Solutions',
    excerpt: 'Learn how to create and deploy intelligent AI agents that scale with your enterprise needs...',
    author: '<PERSON>',
    readTime: '5 min read',
    image: 'https://images.unsplash.com/photo-1488229297570-58520851e868?auto=format&fit=crop&q=80&w=800',
    category: 'Enterprise'
  }
];

const Blog = () => {
  return (
    <main className="py-24">
      <div className="container">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Discover How AI is Changing the<br />
            <span className="gradient-text">Future of Automation</span>
          </h1>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            Explore the latest insights, trends, and success stories in AI automation
          </p>
        </motion.div>

        {/* Filter Section */}
        <div className="flex flex-wrap gap-4 mb-12">
          <button className="px-4 py-2 rounded-full bg-[var(--primary)] text-white">All</button>
          <button className="px-4 py-2 rounded-full bg-[var(--card)] text-gray-400 hover:text-white">Business</button>
          <button className="px-4 py-2 rounded-full bg-[var(--card)] text-gray-400 hover:text-white">Automation</button>
          <button className="px-4 py-2 rounded-full bg-[var(--card)] text-gray-400 hover:text-white">AI Agents</button>
        </div>

        {/* Blog Posts Grid */}
        <div className="grid md:grid-cols-3 gap-8">
          {blogPosts.map((post, index) => (
            <motion.article
              key={post.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="card card-hover overflow-hidden"
            >
              <img
                src={post.image}
                alt={post.title}
                className="w-full h-48 object-cover"
              />
              <div className="p-6">
                <div className="flex items-center gap-2 mb-4">
                  <span className="text-sm text-[var(--primary)]">{post.category}</span>
                  <span className="text-gray-400">•</span>
                  <span className="text-sm text-gray-400">{post.readTime}</span>
                </div>
                <h2 className="text-xl font-semibold mb-4">{post.title}</h2>
                <p className="text-gray-400 mb-6">{post.excerpt}</p>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-400">{post.author}</span>
                  <button className="text-[var(--primary)] hover:text-[var(--secondary)]">
                    Read More →
                  </button>
                </div>
              </div>
            </motion.article>
          ))}
        </div>

        {/* Load More Button */}
        <div className="text-center mt-12">
          <button className="btn-primary">Load More Articles</button>
        </div>
      </div>
    </main>
  );
};

export default Blog;