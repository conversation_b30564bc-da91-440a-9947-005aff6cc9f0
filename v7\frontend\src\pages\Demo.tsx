import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "../components/ui/Button";
import {
  FiCode,
  FiPlay,
  FiCopy,
  FiDownload,
  FiRefreshCw,
  FiChevronDown,
  FiSearch,
  FiX,
  FiCheck,
  FiZap,
  FiArrowRight,
  FiAlertCircle,
  FiTrash2,
} from "react-icons/fi";

const TASK_OPTIONS = [
  {
    value: "Struts → Spring",
    label: "Struts → Spring",
    category: "Framework Migration",
    inputLang: "java",
    outputLang: "java",
  },
  {
    value: "AngularJS (v1) → React",
    label: "AngularJS (v1) → React",
    category: "Framework Migration",
    inputLang: "javascript",
    outputLang: "jsx",
  },
  {
    value: "Angular (2+) → React",
    label: "Angular (2+) → React",
    category: "Framework Migration",
    inputLang: "typescript",
    outputLang: "jsx",
  },
  {
    value: "React → Angular",
    label: "React → Angular",
    category: "Framework Migration",
    inputLang: "jsx",
    outputLang: "typescript",
  },
  {
    value: "Java → Python",
    label: "Java → Python",
    category: "Language Translation",
    inputLang: "java",
    outputLang: "python",
  },
  {
    value: "Python → Java",
    label: "Python → Java",
    category: "Language Translation",
    inputLang: "python",
    outputLang: "java",
  },
  {
    value: "Java → TypeScript",
    label: "Java → TypeScript",
    category: "Language Translation",
    inputLang: "java",
    outputLang: "typescript",
  },
  {
    value: "TypeScript → Java",
    label: "TypeScript → Java",
    category: "Language Translation",
    inputLang: "typescript",
    outputLang: "java",
  },
  {
    value: "JavaScript → Python",
    label: "JavaScript → Python",
    category: "Language Translation",
    inputLang: "javascript",
    outputLang: "python",
  },
  {
    value: "Python → JavaScript",
    label: "Python → JavaScript",
    category: "Language Translation",
    inputLang: "python",
    outputLang: "javascript",
  },
  {
    value: "Java 8 → Java 11",
    label: "Java 8 → Java 11",
    category: "Version Upgrade",
    inputLang: "java",
    outputLang: "java",
  },
  {
    value: "Java 11 → Java 17",
    label: "Java 11 → Java 17",
    category: "Version Upgrade",
    inputLang: "java",
    outputLang: "java",
  },
  {
    value: "Java 17 → Java 21",
    label: "Java 17 → Java 21",
    category: "Version Upgrade",
    inputLang: "java",
    outputLang: "java",
  },
  {
    value: "Flask → FastAPI",
    label: "Flask → FastAPI",
    category: "Framework Migration",
    inputLang: "python",
    outputLang: "python",
  },
  {
    value: "Express.js → NestJS",
    label: "Express.js → NestJS",
    category: "Framework Migration",
    inputLang: "javascript",
    outputLang: "typescript",
  },
  {
    value: "Spring MVC → Spring Boot",
    label: "Spring MVC → Spring Boot",
    category: "Framework Migration",
    inputLang: "java",
    outputLang: "java",
  },
];

// Monaco Editor Component
const MonacoEditor = ({
  value,
  onChange,
  language,
  readOnly = false,
  placeholder = "",
}: {
  value: string;
  onChange?: (value: string) => void;
  language: string;
  readOnly?: boolean;
  placeholder?: string;
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const monacoRef = useRef<any>(null);
  const editorInstanceRef = useRef<any>(null);

  useEffect(() => {
    // Load Monaco Editor
    const script = document.createElement("script");
    script.src =
      "https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.44.0/min/vs/loader.min.js";
    script.onload = () => {
      (window as any).require.config({
        paths: {
          vs: "https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.44.0/min/vs",
        },
      });

      (window as any).require(["vs/editor/editor.main"], () => {
        if (editorRef.current && !editorInstanceRef.current) {
          monacoRef.current = (window as any).monaco;

          // Map language names to Monaco language IDs
          const languageMap: Record<string, string> = {
            java: "java",
            python: "python",
            javascript: "javascript",
            typescript: "typescript",
            jsx: "javascript",
          };

          const monacoLanguage = languageMap[language] || "plaintext";

          editorInstanceRef.current = monacoRef.current.editor.create(
            editorRef.current,
            {
              value: value || placeholder,
              language: monacoLanguage,
              theme: "vs-dark",
              readOnly,
              fontSize: 14,
              fontFamily:
                '"Fira Code", "JetBrains Mono", Monaco, Consolas, monospace',
              lineNumbers: "on",
              minimap: { enabled: false },
              scrollBeyondLastLine: false,
              automaticLayout: true,
              wordWrap: "on",
              bracketPairColorization: { enabled: true },
              renderWhitespace: "selection",
              smoothScrolling: true,
              contextmenu: true,
              selectOnLineNumbers: true,
              lineNumbersMinChars: 3,
              padding: { top: 12, bottom: 12 },
              scrollbar: {
                vertical: "auto",
                horizontal: "auto",
                verticalScrollbarSize: 8,
                horizontalScrollbarSize: 8,
              },
            }
          );

          // Handle value changes
          if (onChange && !readOnly) {
            editorInstanceRef.current.onDidChangeModelContent(() => {
              const newValue = editorInstanceRef.current.getValue();
              onChange(newValue);
            });
          }
        }
      });
    };

    if (!(window as any).require) {
      document.head.appendChild(script);
    } else {
      script.onload?.({} as any);
    }

    return () => {
      if (editorInstanceRef.current) {
        editorInstanceRef.current.dispose();
        editorInstanceRef.current = null;
      }
    };
  }, []);

  // Update editor value when prop changes
  useEffect(() => {
    if (
      editorInstanceRef.current &&
      value !== editorInstanceRef.current.getValue()
    ) {
      editorInstanceRef.current.setValue(value || placeholder);
    }
  }, [value, placeholder]);

  // Update language when prop changes
  useEffect(() => {
    if (editorInstanceRef.current && monacoRef.current) {
      const languageMap: Record<string, string> = {
        java: "java",
        python: "python",
        javascript: "javascript",
        typescript: "typescript",
        jsx: "javascript",
      };
      const monacoLanguage = languageMap[language] || "plaintext";
      monacoRef.current.editor.setModelLanguage(
        editorInstanceRef.current.getModel(),
        monacoLanguage
      );
    }
  }, [language]);

  return (
    <div className="relative flex-1 rounded-xl overflow-hidden border border-gray-700">
      {/* Editor Header */}
      <div className="h-8 bg-gray-800 dark:bg-gray-950 flex items-center px-4 border-b border-gray-700">
        <div className="flex space-x-2">
          <div className="w-3 h-3 rounded-full bg-red-500"></div>
          <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
          <div className="w-3 h-3 rounded-full bg-green-500"></div>
        </div>
        <div className="ml-4 text-xs text-gray-400">
          {readOnly ? "output" : "main"}.
          {language === "python" ? "py" : language === "java" ? "java" : "js"}
        </div>
      </div>

      {/* Monaco Editor Container */}
      <div
        ref={editorRef}
        className="w-full h-[calc(100%-32px)] bg-gray-900 dark:bg-gray-950"
        style={{ minHeight: "300px" }}
      />
    </div>
  );
};

// Toast notification component - Bottom right with slide left animation
const Toast = ({
  message,
  type,
  onClose,
}: {
  message: string;
  type: "error" | "success";
  onClose: () => void;
}) => (
  <motion.div
    initial={{ opacity: 0, x: 300, scale: 0.9 }}
    animate={{ opacity: 1, x: 0, scale: 1 }}
    exit={{ opacity: 0, x: 300, scale: 0.9 }}
    className={`fixed bottom-4 right-4 z-50 flex items-center px-4 py-3 rounded-xl shadow-lg border transition-all ${
      type === "error"
        ? "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-700 dark:text-red-300"
        : "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-700 dark:text-green-300"
    }`}
  >
    <FiAlertCircle className="w-5 h-5 mr-2" />
    <span className="font-medium">{message}</span>
    <button
      onClick={onClose}
      className="ml-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
    >
      <FiX className="w-4 h-4" />
    </button>
  </motion.div>
);

// Advanced code formatting functions
const formatCode = (code: string, language: string): string => {
  if (!code.trim()) return code;

  switch (language) {
    case "java":
      return code
        .replace(/\s*{\s*/g, " {\n")
        .replace(/;\s*/g, ";\n")
        .replace(/}\s*/g, "\n}\n")
        .replace(/,\s*/g, ",\n    ")
        .replace(/\n\s*\n\s*\n/g, "\n\n")
        .split("\n")
        .map((line, index, arr) => {
          let indent = 0;
          for (let i = 0; i < index; i++) {
            if (arr[i].includes("{")) indent++;
            if (arr[i].includes("}")) indent--;
          }
          if (line.includes("}")) indent--;
          return "    ".repeat(Math.max(0, indent)) + line.trim();
        })
        .filter((line) => line.trim().length > 0)
        .join("\n");

    case "python":
      return code
        .replace(/:\s*/g, ":\n")
        .replace(/,\s*/g, ",\n")
        .split("\n")
        .map((line) => line.trim())
        .filter((line) => line.length > 0)
        .join("\n");

    case "javascript":
    case "typescript":
    case "jsx":
      return code
        .replace(/\s*{\s*/g, " {\n")
        .replace(/;\s*/g, ";\n")
        .replace(/}\s*/g, "\n}\n")
        .replace(/,\s*(?![^()]*\))/g, ",\n    ")
        .replace(/\n\s*\n\s*\n/g, "\n\n")
        .split("\n")
        .map((line, index, arr) => {
          let indent = 0;
          for (let i = 0; i < index; i++) {
            if (arr[i].includes("{")) indent++;
            if (arr[i].includes("}")) indent--;
          }
          if (line.includes("}")) indent--;
          return "    ".repeat(Math.max(0, indent)) + line.trim();
        })
        .filter((line) => line.trim().length > 0)
        .join("\n");

    default:
      return code;
  }
};

// Simplified validation - just check if code exists
// const validateCodeForLanguage = (code: string, language: string): boolean => {
//   return code.trim().length > 0;
// };

export default function Demo() {
  const [taskType, setTaskType] = useState("Python → Java");
  const [code, setCode] = useState(`print("Hello, I'm Silicon Agent!")`);
  const [transformedCode, setTransformedCode] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [copied, setCopied] = useState(false);
  const [toast, setToast] = useState<{
    message: string;
    type: "error" | "success";
  } | null>(null);

  // Get current task option
  const currentTask =
    TASK_OPTIONS.find((option) => option.value === taskType) || TASK_OPTIONS[0];

  // Filter options based on search
  const filteredOptions = TASK_OPTIONS.filter(
    (option) =>
      option.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
      option.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Group options by category
  const groupedOptions = filteredOptions.reduce((acc, option) => {
    const category = option.category;
    if (!acc[category]) acc[category] = [];
    acc[category].push(option);
    return acc;
  }, {} as Record<string, typeof TASK_OPTIONS>);

  const showToast = (message: string, type: "error" | "success") => {
    setToast({ message, type });
    setTimeout(() => setToast(null), 4000);
  };

  const getSystemMessage = (task: string) => {
    return [
      "You are a highly reliable code transformation assistant.",
      "",
      `Your task is to convert the user's input code according to the following transformation: ${task}`,
      "",
      "--------------------------",
      "💡 Output Format Rules:",
      "--------------------------",
      "1. Only return the transformed code, without any markdown syntax (no triple backticks or headings).",
      '2. Return code as raw text. Do NOT prepend it with "Here is the code", "Here\'s your result", or similar.',
      "3. If necessary, append a plain-text 'Notes:' section (only if there are:",
      "   - Migration caveats,",
      "   - Manual steps,",
      "   - Compatibility considerations,",
      "   - Library import requirements, etc.)",
      "",
      "4. Use only one class/component/file unless the transformation logically demands multiple files.",
      "5. Ensure all output code is valid, idiomatic, and minimal for the target environment or language.",
      "",
      "--------------------------",
      "⛔ Do NOT Include:",
      "--------------------------",
      "- Markdown formatting like triple backticks (```)",
      "- Internal thoughts, explanation, or reasoning (e.g., <think> blocks)",
      "- Any repetition of the input code unless required",
      '- Summaries like "converted from X to Y"',
      "- Import statements unless required for code to compile or run",
      "",
      "--------------------------",
      "🛑 Invalid Input Handling:",
      "--------------------------",
      "If the input is empty, meaningless, or too minimal to transform, reply with:",
      '"The code is empty or invalid. Please provide valid code to transform."',
    ].join("\n");
  };

  const handleTransform = async () => {
    setError("");
    setTransformedCode("");

    if (!code.trim()) {
      setError("Please enter some code to transform.");
      showToast("Please enter some code to transform.", "error");
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(
        "https://api.groq.com/openai/v1/chat/completions",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${import.meta.env.VITE_GROQ_API_KEY}`,
          },
          body: JSON.stringify({
            model: "deepseek-r1-distill-llama-70b",
            messages: [
              {
                role: "system",
                content: getSystemMessage(taskType),
              },
              {
                role: "user",
                content: code,
              },
            ],
            temperature: 0.7,
          }),
        }
      );

      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);

      const data = await response.json();
      const cleanedContent = data.choices?.[0]?.message?.content
        ?.replace(/<think>[\s\S]*?<\/think>/g, "")
        .trim();
      setTransformedCode(cleanedContent || "No output.");
      showToast("Code transformed successfully!", "success");
    } catch (error) {
      console.error("Error transforming code:", error);
      const errorMessage = "Error transforming code. Please try again.";
      setError(errorMessage);
      showToast(errorMessage, "error");
    } finally {
      setIsLoading(false);
    }
  };

  const handleFormatCode = () => {
    const formatted = formatCode(code, currentTask.inputLang);
    setCode(formatted);
    showToast("Code formatted successfully!", "success");
  };

  const handleClearCode = () => {
    setCode("");
    setError("");
    showToast("Code cleared successfully!", "success");
  };

  const handleCopyCode = async () => {
    if (transformedCode) {
      await navigator.clipboard.writeText(transformedCode);
      setCopied(true);
      showToast("Code copied to clipboard!", "success");
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const handleDownload = () => {
    if (transformedCode) {
      const extension =
        currentTask.outputLang === "python"
          ? "py"
          : currentTask.outputLang === "java"
          ? "java"
          : currentTask.outputLang.includes("script")
          ? "ts"
          : "txt";
      const blob = new Blob([transformedCode], { type: "text/plain" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `transformed_code.${extension}`;
      a.click();
      URL.revokeObjectURL(url);
      showToast("Code downloaded successfully!", "success");
    }
  };

  return (
    <motion.div
      className="bg-white dark:bg-black transition-all"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      {/* Toast notifications - Bottom right */}
      <AnimatePresence>
        {toast && (
          <Toast
            message={toast.message}
            type={toast.type}
            onClose={() => setToast(null)}
          />
        )}
      </AnimatePresence>

      {/* Main Container with proper spacing */}
      <div className="container mx-auto px-4 sm:px-6 max-w-7xl py-8 pb-16">
        {/* Compact Header Section */}
        <motion.div
          className="text-center mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <motion.div
            className="inline-flex items-center gap-2 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 px-4 py-2 rounded-full text-sm font-medium mb-3 transition-all"
            whileHover={{ scale: 1.05 }}
          >
            <FiZap className="w-4 h-4" />
            AI-Powered Code Transformation
          </motion.div>

          <h1 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 dark:from-indigo-400 dark:to-purple-400 bg-clip-text text-transparent mb-2">
            Code Transformation Demo
          </h1>

          <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto transition-colors">
            Transform your code between languages and frameworks with AI
            precision.
          </p>
        </motion.div>

        {/* Mobile-responsive grid layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Input Section */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="flex flex-col"
          >
            <div className="bg-gray-50 dark:bg-gray-900/50 rounded-2xl p-4 border border-gray-200/50 dark:border-gray-700/50 transition-all">
              {/* Task Selection Dropdown */}
              <div className="mb-4">
                <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 transition-colors">
                  <FiRefreshCw className="inline w-4 h-4 mr-2" />
                  Transformation Type
                </label>

                <div className="relative">
                  <motion.button
                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                    className="w-full flex items-center justify-between px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm hover:border-indigo-500 dark:hover:border-indigo-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all text-sm"
                    whileHover={{ scale: 1.01 }}
                    whileTap={{ scale: 0.99 }}
                  >
                    <div className="flex items-center min-w-0 flex-1">
                      <div className="w-2 h-2 rounded-full bg-indigo-500 mr-2 flex-shrink-0"></div>
                      <span className="text-gray-900 dark:text-gray-100 font-medium transition-colors truncate">
                        {currentTask.label}
                      </span>
                      <span className="ml-2 px-2 py-1 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 text-xs rounded-lg transition-colors hidden sm:inline-block">
                        {currentTask.category}
                      </span>
                    </div>
                    <motion.div
                      animate={{ rotate: isDropdownOpen ? 180 : 0 }}
                      transition={{ duration: 0.2 }}
                      className="flex-shrink-0 ml-2"
                    >
                      <FiChevronDown className="w-4 h-4 text-gray-400" />
                    </motion.div>
                  </motion.button>

                  <AnimatePresence>
                    {isDropdownOpen && (
                      <>
                        {/* Backdrop */}
                        <motion.div
                          className="fixed inset-0 z-10"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          onClick={() => setIsDropdownOpen(false)}
                        />

                        {/* Dropdown Menu */}
                        <motion.div
                          className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-xl z-20 transition-all"
                          initial={{ opacity: 0, y: -10, scale: 0.95 }}
                          animate={{ opacity: 1, y: 0, scale: 1 }}
                          exit={{ opacity: 0, y: -10, scale: 0.95 }}
                          transition={{ duration: 0.2 }}
                        >
                          {/* Search */}
                          <div className="p-3 border-b border-gray-200 dark:border-gray-700">
                            <div className="relative">
                              <FiSearch className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                              <input
                                type="text"
                                placeholder="Search transformations..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="w-full pl-10 pr-4 py-2 bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all"
                              />
                              {searchQuery && (
                                <button
                                  onClick={() => setSearchQuery("")}
                                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                                >
                                  <FiX className="w-4 h-4" />
                                </button>
                              )}
                            </div>
                          </div>

                          {/* Options */}
                          <div className="max-h-64 overflow-y-auto">
                            {Object.entries(groupedOptions).map(
                              ([category, options]) => (
                                <div key={category} className="p-2">
                                  <div className="px-3 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                    {category}
                                  </div>
                                  {options.map((option) => (
                                    <motion.button
                                      key={option.value}
                                      onClick={() => {
                                        setTaskType(option.value);
                                        setIsDropdownOpen(false);
                                        setSearchQuery("");
                                      }}
                                      className={`w-full flex items-center px-3 py-2 rounded-lg text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-all text-sm ${
                                        taskType === option.value
                                          ? "bg-indigo-50 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400"
                                          : "text-gray-900 dark:text-gray-100"
                                      }`}
                                      whileHover={{ scale: 1.01 }}
                                      whileTap={{ scale: 0.99 }}
                                    >
                                      <div
                                        className={`w-2 h-2 rounded-full mr-3 flex-shrink-0 ${
                                          taskType === option.value
                                            ? "bg-indigo-500"
                                            : "bg-gray-300 dark:bg-gray-600"
                                        }`}
                                      ></div>
                                      <span className="font-medium truncate">
                                        {option.label}
                                      </span>
                                      {taskType === option.value && (
                                        <FiCheck className="w-4 h-4 ml-auto text-indigo-500 flex-shrink-0" />
                                      )}
                                    </motion.button>
                                  ))}
                                </div>
                              )
                            )}
                          </div>
                        </motion.div>
                      </>
                    )}
                  </AnimatePresence>
                </div>
              </div>

              {/* Code Input Area */}
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <label className="text-sm font-semibold text-gray-700 dark:text-gray-300 transition-colors">
                    <FiCode className="inline w-4 h-4 mr-2" />
                    Input Code ({currentTask.inputLang})
                  </label>
                  <div className="flex items-center space-x-2">
                    <motion.button
                      onClick={handleFormatCode}
                      className="text-xs text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300 font-medium transition-colors"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      Format
                    </motion.button>
                    <motion.button
                      onClick={handleClearCode}
                      className="text-xs text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 font-medium transition-colors flex items-center"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <FiTrash2 className="w-3 h-3 mr-1" />
                      Clear
                    </motion.button>
                  </div>
                </div>

                {/* Monaco Code Editor with fixed height */}
                <div className="h-80 sm:h-96">
                  <MonacoEditor
                    value={code}
                    onChange={setCode}
                    language={currentTask.inputLang}
                    // placeholder={``}
                  />
                </div>

                {error && (
                  <motion.p
                    className="text-red-500 dark:text-red-400 text-sm mt-2 flex items-center transition-colors"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    <FiX className="w-4 h-4 mr-1" />
                    {error}
                  </motion.p>
                )}
              </div>

              {/* Transform Button */}
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  onClick={handleTransform}
                  disabled={isLoading || !code.trim()}
                  className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <div className="w-5 h-5 mr-2">
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      </div>
                      Transforming...
                    </div>
                  ) : (
                    <div className="flex items-center justify-center">
                      <FiPlay className="w-5 h-5 mr-2" />
                      Transform Code
                      <FiArrowRight className="w-4 h-4 ml-2" />
                    </div>
                  )}
                </Button>
              </motion.div>
            </div>
          </motion.div>

          {/* Output Section */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="flex flex-col"
          >
            <div className="bg-gray-50 dark:bg-gray-900/50 rounded-2xl p-4 border border-gray-200/50 dark:border-gray-700/50 transition-all">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center transition-colors">
                  <FiCode className="w-5 h-5 mr-2" />
                  Output ({currentTask.outputLang})
                </h3>

                {transformedCode && (
                  <div className="flex items-center space-x-2">
                    <motion.button
                      onClick={handleCopyCode}
                      className="p-2 text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 rounded-lg transition-all"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      title="Copy code"
                    >
                      {copied ? (
                        <FiCheck className="w-4 h-4" />
                      ) : (
                        <FiCopy className="w-4 h-4" />
                      )}
                    </motion.button>

                    <motion.button
                      onClick={handleDownload}
                      className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-all"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      title="Download code"
                    >
                      <FiDownload className="w-4 h-4" />
                    </motion.button>
                  </div>
                )}
              </div>

              {/* Output Area with fixed height */}
              <div className="h-80 sm:h-96">
                <AnimatePresence mode="wait">
                  {transformedCode ? (
                    <motion.div
                      key="output"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.3 }}
                      className="h-full"
                    >
                      <MonacoEditor
                        value={transformedCode}
                        language={currentTask.outputLang}
                        readOnly={true}
                      />
                    </motion.div>
                  ) : (
                    <motion.div
                      key="placeholder"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="h-full flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl transition-all"
                    >
                      <div className="text-center">
                        <FiCode className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
                        <p className="text-gray-500 dark:text-gray-400 transition-colors">
                          Transformed code will appear here
                        </p>
                        <p className="text-sm text-gray-400 dark:text-gray-500 mt-2 transition-colors">
                          Paste your code and click Transform to get started
                        </p>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
}
