import { motion } from 'framer-motion';
import { Mail, Phone, Linkedin, Twitter, Facebook } from 'lucide-react';

const ContactUs = () => {
    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.5 }}
            className="container mx-auto p-4 py-24"
        >
            <h1 className="text-4xl md:text-6xl font-bold text-center mb-12">Contact Us</h1>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <h2 className="text-2xl font-bold mb-4">Get in Touch</h2>
                    <form className="space-y-4">
                        <div>
                            <label className="block text-sm font-medium mb-1">Name</label>
                            <input type="text" className="w-full p-2 rounded bg-gray-800 text-gray-300" />
                        </div>
                        <div>
                            <label className="block text-sm font-medium mb-1">Email</label>
                            <input type="email" className="w-full p-2 rounded bg-gray-800 text-gray-300" />
                        </div>
                        <div>
                            <label className="block text-sm font-medium mb-1">Message</label>
                            <textarea className="w-full p-2 rounded bg-gray-800 text-gray-300" rows={4}></textarea>
                        </div>
                        <button type="submit" className="btn-primary">Send Message</button>
                    </form>
                </div>
                <div>
                    <h2 className="text-2xl font-bold mb-4">Business Inquiries</h2>
                    <p className="mb-4">For partnership opportunities and business-related questions, please contact us at:</p>
                    <div className="flex items-center mb-4">
                        <Mail className="w-6 h-6 text-[var(--primary)] mr-2" />
                        <a href="mailto:<EMAIL>" className="text-[var(--primary)]"><EMAIL></a>
                    </div>
                    <div className="flex items-center mb-4">
                        <Phone className="w-6 h-6 text-[var(--primary)] mr-2" />
                        <span className="text-[var(--primary)]">+91 6301704900</span>
                    </div>
                    <h2 className="text-2xl font-bold mb-4">Follow Us</h2>
                    <div className="flex space-x-4">
                        <a href="#" className="text-[var(--primary)]"><Linkedin className="w-6 h-6" /></a>
                        <a href="#" className="text-[var(--primary)]"><Twitter className="w-6 h-6" /></a>
                        <a href="#" className="text-[var(--primary)]"><Facebook className="w-6 h-6" /></a>
                    </div>
                </div>
            </div>
        </motion.div>
    );
};

export default ContactUs;
