import { Layout } from "~/components/Layout";
import { Button } from "~/components/ui/Button";
import { FiArrowLeft } from "react-icons/fi";
import { Link } from "@remix-run/react";

export default function JavaUpgrades() {
  return (
    <Layout>
      <div className="bg-white dark:bg-gray-900 py-24">
        <div className="container mx-auto px-6">
          <div className="mb-8">
            <Link
              to="/"
              className="inline-flex items-center text-indigo-600 hover:text-indigo-800"
            >
              <FiArrowLeft className="mr-2" />
              Back to Transform
            </Link>
          </div>

          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
            Learn More About Java Upgrades
          </h1>

          <div className="prose dark:prose-invert max-w-3xl">
            <p>
              Java Upgrades are a critical part of modernizing legacy systems.
              Here, you'll find detailed information about the benefits,
              process, and technologies we use to ensure a smooth and efficient
              upgrade.
            </p>

            <h2>Benefits of Java Upgrades</h2>
            <ul>
              <li>
                <strong>Performance Improvements:</strong> Newer Java versions
                offer significant performance enhancements.
              </li>
              <li>
                <strong>Security Patches:</strong> Stay protected with the
                latest security updates.
              </li>
              <li>
                <strong>Modern Features:</strong> Take advantage of new language
                features and APIs.
              </li>
              <li>
                <strong>Reduced Costs:</strong> Lower operational costs with
                improved efficiency.
              </li>
            </ul>

            <h2>Our Java Upgrade Process</h2>
            <ol>
              <li>
                <strong>Analysis:</strong> AI-powered code analysis identifies
                upgrade opportunities.
              </li>
              <li>
                <strong>Planning:</strong> Detailed transformation plan with
                risk assessment.
              </li>
              <li>
                <strong>Transformation:</strong> Automated code transformation
                with human oversight.
              </li>
              <li>
                <strong>Testing:</strong> Comprehensive testing ensures
                functional equivalence.
              </li>
              <li>
                <strong>Deployment:</strong> Zero-downtime deployment with
                rollback capability.
              </li>
            </ol>

            <h2>Technologies We Use</h2>
            <ul>
              <li>
                <strong>AI-Powered Analysis Tools:</strong> Identify deprecated
                APIs and language features.
              </li>
              <li>
                <strong>Automated Code Transformation:</strong> Intelligent
                refactoring to use modern Java language features.
              </li>
              <li>
                <strong>Comprehensive Testing Frameworks:</strong> Ensure
                functional equivalence.
              </li>
            </ul>

            <Button className="bg-indigo-600 hover:bg-indigo-700 text-white mt-8">
              <Link to="/contact">Try yourself</Link>
            </Button>
          </div>
        </div>
      </div>
    </Layout>
  );
}
