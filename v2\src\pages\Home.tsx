import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

const Home = () => {
  return (
    <motion.main
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      {/* Hero Section */}
      <section className="min-h-screen relative overflow-hidden flex items-center">
        <div
          className="absolute inset-0 bg-gradient-to-b from-blue-500/10 to-purple-500/10"
          style={{
            backgroundImage: "url('https://images.unsplash.com/photo-1639762681485-074b7f938ba0?auto=format&fit=crop&q=80&w=2832')",
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundBlendMode: 'overlay',
            opacity: 0.2
          }}
        />
        <div className="container relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="max-w-6xl mx-auto text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold py mb-6">
              <p className='mb-3'>Build, Deploy, and Automate</p>
              <span className="gradient-text">with AI Agents</span>
            </h1>
            <p className="text-lg md:text-xl text-gray-300 mb-8">
              Empower your workflow with our intelligent AI agents. Automate tasks, optimize processes, and integrate cutting-edge AI solutions seamlessly into your projects.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/agents" className="btn-primary">
                Explore AI Agents
              </Link>
              <Link to="/pricing" className="btn-secondary">
                Get Started
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-[var(--card)]/50">
        <div className="container">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="p-6"
            >
              <h3 className="text-3xl md:text-4xl font-bold gradient-text mb-2">100+</h3>
              <p className="text-gray-400">Available Agents</p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 }}
              className="p-6"
            >
              <h3 className="text-3xl md:text-4xl font-bold gradient-text mb-2">&lt;1s</h3>
              <p className="text-gray-400">Response Time</p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2 }}
              className="p-6"
            >
              <h3 className="text-3xl md:text-4xl font-bold gradient-text mb-2">10x</h3>
              <p className="text-gray-400">Faster Automation</p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.3 }}
              className="p-6"
            >
              <h3 className="text-3xl md:text-4xl font-bold gradient-text mb-2">24/7</h3>
              <p className="text-gray-400">Availability</p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-[var(--card)]/50">
        <div className="container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Transform Your Workflow?
            </h2>
            <p className="text-gray-400 mb-8">
              Join thousands of developers and teams who are already using siliconagent.ai to supercharge their productivity.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/signup" className="btn-primary">
                Get Started Now
              </Link>
              <Link to="/contact" className="btn-secondary">
                Schedule Demo
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </motion.main>
  );
};

export default Home;