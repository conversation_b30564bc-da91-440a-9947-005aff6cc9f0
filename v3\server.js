import express from "express";
import mysql from "mysql2";
import bodyParser from "body-parser";
import cors from "cors";

const app = express();
const port = 5000;

app.use(
  cors({
    origin: "http://localhost:5173", // Adjust if frontend runs on a different port (3000 for CRA)
    methods: "GET, POST, PUT, DELETE",
    allowedHeaders: "Content-Type, Authorization",
    credentials: true,
  })
);

app.use(bodyParser.json());

app.use((req, res, next) => {
  res.header("Access-Control-Allow-Origin", "http://localhost:5173");
  res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  res.header("Access-Control-Allow-Headers", "Content-Type, Authorization");
  next();
});

app.options("*", (req, res) => {
  res.sendStatus(200);
});

// Create a connection to the database
const db = mysql.createConnection({
  host: "127.0.0.1",
  port: 3306,
  user: "root",
  password: "password",
  database: "siliconagent",
});

// Connect to the database
db.connect((err) => {
  if (err) {
    console.error("Error connecting to the database:", err);
    return;
  }
  console.log("Connected to the database");
});

// Endpoint to fetch blog posts
app.get("/api/posts", (req, res) => {
  const query = "SELECT id, title, excerpt, author, readTime, image, category FROM posts"; // Exclude content for list view
  db.query(query, (err, results) => {
    if (err) {
      console.error("Error fetching posts:", err);
      res.status(500).send("Server error");
      return;
    }
    res.json(results);
  });
});

// Endpoint to fetch a single blog post by ID
app.get("/api/posts/:id", (req, res) => {
  const { id } = req.params;
  // console.log("Fetching post with ID:", id);
  const query = "SELECT * FROM posts WHERE id = ?";
  db.query(query, [id], (err, results) => {
    if (err) {
      console.error("Error fetching post:", err);
      res.status(500).send("Server error");
      return;
    }
    console.log("Query results:", results);
    if (results.length === 0) {
      console.warn("Post not found for ID:", id);
      res.status(404).send("Post not found");
      return;
    }
    res.json(results[0]);
  });
});

// Endpoint to create a new blog post
app.post("/api/posts", (req, res) => {
  const { title, excerpt, author, readTime, image, category, content } = req.body;
  const query =
    "INSERT INTO posts (title, excerpt, author, readTime, image, category, content) VALUES (?, ?, ?, ?, ?, ?, ?)";
  db.query(
    query,
    [title, excerpt, author, readTime, image, category, content],
    (err, results) => {
      if (err) {
        console.error("Error creating post:", err);
        res.status(500).send("Server error");
        return;
      }
      res.status(201).send("Post created");
    }
  );
});

// Endpoint to fetch top 8 categories based on the number of posts
app.get("/api/categories", (req, res) => {
  const query = `
    SELECT category, COUNT(*) AS postCount 
    FROM posts 
    GROUP BY category 
    ORDER BY postCount DESC 
    LIMIT 8
  `;
  db.query(query, (err, results) => {
    if (err) {
      console.error("Error fetching categories:", err);
      res.status(500).send("Server error");
      return;
    }
    res.json(results.map((row) => row.category)); // Return an array of top 8 categories
  });
});

app.listen(port, () => {
  console.log(`Server running at http://localhost:${port}`);
});
