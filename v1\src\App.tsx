import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Navbar from './components/Navbar';
import Home from './components/Home';
import Features from './components/Features';
import HowItWorks from './components/HowItWorks';
import Marketplace from './components/Marketplace';
import Advantages from './components/Advantages';
import Pricing from './components/Pricing';
import About from './components/About';
import Contact from './components/Contact';
import Blog from './components/Blog';

// Internal Pages
import Dashboard from './components/internal/Dashboard';
import ManageAgents from './components/internal/ManageAgents';
import ManageApplications from './components/internal/ManageApplications';
import ManageAgentChains from './components/internal/ManageAgentChains';
import ExecutionMonitor from './components/internal/ExecutionMonitor';
import ApiManagement from './components/internal/ApiManagement';
import Analytics from './components/internal/Analytics';
import Settings from './components/internal/Settings';
import Notifications from './components/internal/Notifications';

function App() {
  return (
    <Router>
      <Navbar />
      <Routes>
        {/* External Pages */}
        <Route path="/" element={<Home />} />
        <Route path="/features" element={<Features />} />
        <Route path="/how-it-works" element={<HowItWorks />} />
        <Route path="/marketplace" element={<Marketplace />} />
        <Route path="/advantages" element={<Advantages />} />
        <Route path="/pricing" element={<Pricing />} />
        <Route path="/about" element={<About />} />
        <Route path="/contact" element={<Contact />} />
        <Route path="/blog" element={<Blog />} />

        {/* Internal Pages */}
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/manage-agents" element={<ManageAgents />} />
        <Route path="/manage-applications" element={<ManageApplications />} />
        <Route path="/manage-agent-chains" element={<ManageAgentChains />} />
        <Route path="/execution-monitor" element={<ExecutionMonitor />} />
        <Route path="/api-management" element={<ApiManagement />} />
        <Route path="/analytics" element={<Analytics />} />
        <Route path="/settings" element={<Settings />} />
        <Route path="/notifications" element={<Notifications />} />
      </Routes>
    </Router>
  );
}

export default App;
