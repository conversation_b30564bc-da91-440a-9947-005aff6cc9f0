@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --primary: #4A6CF7;
    --secondary: #00D1FF;
    --background: #0B1120;
    --card: #151C31;
  }

  body {
    @apply bg-[var(--background)] text-white font-sans antialiased;
  }
}

@layer components {
  .container {
    @apply max-w-[1240px] mx-auto px-4;
  }

  .btn-primary {
    @apply bg-[var(--primary)] text-white px-6 py-3 rounded-lg 
    transition-all duration-300 inline-flex items-center justify-center
    hover:opacity-90;
  }

  .btn-secondary {
    @apply bg-[var(--secondary)] text-white px-6 py-3 rounded-lg 
    transition-all duration-300 inline-flex items-center justify-center
    hover:opacity-90;
  }

  .card {
    @apply bg-[var(--card)] rounded-2xl p-6 transition-all duration-300;
  }

  .card-hover {
    @apply hover:scale-[1.02] hover:shadow-lg;
  }

  .gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-[var(--primary)] to-[var(--secondary)];
  }
}

/* Animations */
.flip-card {
  perspective: 1000px;
  transform-style: preserve-3d;
}

.flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.6s;
  transform-style: preserve-3d;
}

.flip-card.flipped .flip-card-inner {
  transform: rotateY(180deg);
}

.flip-card-front,
.flip-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
}

.flip-card-back {
  transform: rotateY(180deg);
}