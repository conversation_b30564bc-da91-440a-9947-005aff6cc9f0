import React from 'react';
    import { notificationsData } from './mockData/notificationsData';
    import { Bell, CheckCircle, XCircle, Info } from 'lucide-react';

    const Notifications: React.FC = () => {
      return (
        <div className="p-6 bg-gray-900 text-white rounded-lg shadow-md">
          <h1 className="text-3xl font-bold mb-6 flex items-center"><Bell className="mr-2" /> Notifications &amp; Alerts</h1>
          <ul>
            {notificationsData.map((notification) => (
              <li key={notification.id} className={`mb-4 p-4 rounded-lg shadow ${
                notification.type === 'success' ? 'bg-green-700 bg-opacity-50' :
                notification.type === 'error' ? 'bg-red-700 bg-opacity-50' : 'bg-blue-700 bg-opacity-50'
              }`}>
                <div className="flex items-center">
                  {notification.type === 'success' && <CheckCircle className="text-green-500 mr-2" />}
                  {notification.type === 'error' && <XCircle className="text-red-500 mr-2" />}
                  {notification.type === 'info' && <Info className="text-blue-500 mr-2" />}
                  <span className="font-medium">{notification.time}</span> - {notification.message}
                </div>
              </li>
            ))}
          </ul>
        </div>
      );
    };

    export default Notifications;
