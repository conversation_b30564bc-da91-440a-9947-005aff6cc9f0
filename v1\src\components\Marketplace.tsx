import React from 'react';

    const Marketplace: React.FC = () => {
      // Placeholder data for AI agents
      const agents = [
        { id: 1, name: 'Email Automation Agent', category: 'Productivity', description: 'Automates email sending and responses.' },
        { id: 2, name: 'Data Extraction Agent', category: 'Data Processing', description: 'Extracts data from various sources.' },
        { id: 3, name: 'SEO Optimization Agent', category: 'Marketing', description: 'Optimizes website content for search engines.' },
        { id: 4, name: 'API Connector Agent', category: 'Integration', description: 'Connects to various APIs for data exchange.' },
        { id: 5, name: 'AI Chatbot Agent', category: 'Customer Support', description: 'Provides automated customer support.' },
        { id: 6, name: 'Invoice Processing Agent', category: 'Finance', description: 'Automates invoice processing and management.' },
      ];

      return (
        <div className="bg-gray-900 text-white min-h-screen p-8">
          <div className="container mx-auto">
            <h1 className="text-4xl font-bold text-center mb-12">AI Agents Marketplace</h1>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {agents.map((agent) => (
                <div key={agent.id} className="bg-gray-800 p-6 rounded-lg shadow-lg">
                  <h2 className="text-2xl font-bold mb-2">{agent.name}</h2>
                  <p className="text-gray-400 mb-2">{agent.category}</p>
                  <p className="text-gray-300 mb-4">{agent.description}</p>
                  <a href={`/agent/${agent.id}`} className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                    Explore More
                  </a>
                </div>
              ))}
            </div>
          </div>
        </div>
      );
    };

    export default Marketplace;
