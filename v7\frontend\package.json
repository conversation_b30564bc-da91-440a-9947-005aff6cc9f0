{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@types/express": "^4.17.23", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "express": "^4.21.2", "framer-motion": "^11.0.0", "postcss": "^8.5.5", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^6.28.0", "tailwindcss": "^3.4.17"}, "devDependencies": {"@eslint/js": "^9.25.0", "@radix-ui/react-tabs": "^1.1.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "class-variance-authority": "^0.7.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "highlight.js": "^11.11.1", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.0.2", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}