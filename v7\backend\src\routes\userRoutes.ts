import express from 'express';

const router = express.Router();

// Sample user data (in a real app, this would come from a database)
interface User {
  id: number;
  name: string;
  email: string;
  createdAt: string;
}

let users: User[] = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    createdAt: new Date().toISOString()
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
    createdAt: new Date().toISOString()
  }
];

// GET /api/users - Get all users
router.get('/', (req, res) => {
  res.json({
    success: true,
    data: users,
    count: users.length
  });
});

// GET /api/users/:id - Get user by ID
router.get('/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const user = users.find(u => u.id === id);

  if (!user) {
    res.status(404).json({
      success: false,
      error: 'User not found'
    });
    return;
  }

  res.json({
    success: true,
    data: user
  });
});

// POST /api/users - Create new user
router.post('/', (req, res) => {
  const { name, email } = req.body;

  if (!name || !email) {
    res.status(400).json({
      success: false,
      error: 'Name and email are required'
    });
    return;
  }

  const newUser: User = {
    id: users.length + 1,
    name,
    email,
    createdAt: new Date().toISOString()
  };

  users.push(newUser);

  res.status(201).json({
    success: true,
    data: newUser,
    message: 'User created successfully'
  });
});

// PUT /api/users/:id - Update user
router.put('/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const { name, email } = req.body;

  const userIndex = users.findIndex(u => u.id === id);

  if (userIndex === -1) {
    res.status(404).json({
      success: false,
      error: 'User not found'
    });
    return;
  }

  if (name) users[userIndex].name = name;
  if (email) users[userIndex].email = email;

  res.json({
    success: true,
    data: users[userIndex],
    message: 'User updated successfully'
  });
});

// DELETE /api/users/:id - Delete user
router.delete('/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const userIndex = users.findIndex(u => u.id === id);

  if (userIndex === -1) {
    res.status(404).json({
      success: false,
      error: 'User not found'
    });
    return;
  }

  const deletedUser = users.splice(userIndex, 1)[0];

  res.json({
    success: true,
    data: deletedUser,
    message: 'User deleted successfully'
  });
});

export { router as userRoutes };
