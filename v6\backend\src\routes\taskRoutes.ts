import express from 'express';

const router = express.Router();

// Sample task data
interface Task {
  id: number;
  title: string;
  description: string;
  completed: boolean;
  userId: number;
  createdAt: string;
  updatedAt: string;
}

let tasks: Task[] = [
  {
    id: 1,
    title: 'Complete project setup',
    description: 'Set up the backend and frontend projects with proper integration',
    completed: false,
    userId: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 2,
    title: 'Write API documentation',
    description: 'Document all the API endpoints for the frontend team',
    completed: true,
    userId: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 3,
    title: 'Implement user authentication',
    description: 'Add JWT-based authentication system',
    completed: false,
    userId: 2,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// GET /api/tasks - Get all tasks
router.get('/', (req, res) => {
  const { userId, completed } = req.query;
  
  let filteredTasks = tasks;
  
  if (userId) {
    filteredTasks = filteredTasks.filter(task => task.userId === parseInt(userId as string));
  }
  
  if (completed !== undefined) {
    filteredTasks = filteredTasks.filter(task => task.completed === (completed === 'true'));
  }
  
  res.json({
    success: true,
    data: filteredTasks,
    count: filteredTasks.length
  });
});

// GET /api/tasks/:id - Get task by ID
router.get('/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const task = tasks.find(t => t.id === id);
  
  if (!task) {
    res.status(404).json({
      success: false,
      error: 'Task not found'
    });
    return;
  }
  
  res.json({
    success: true,
    data: task
  });
});

// POST /api/tasks - Create new task
router.post('/', (req, res) => {
  const { title, description, userId } = req.body;
  
  if (!title || !userId) {
    res.status(400).json({
      success: false,
      error: 'Title and userId are required'
    });
    return;
  }
  
  const newTask: Task = {
    id: tasks.length + 1,
    title,
    description: description || '',
    completed: false,
    userId: parseInt(userId),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  tasks.push(newTask);
  
  res.status(201).json({
    success: true,
    data: newTask,
    message: 'Task created successfully'
  });
});

// PUT /api/tasks/:id - Update task
router.put('/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const { title, description, completed } = req.body;
  
  const taskIndex = tasks.findIndex(t => t.id === id);
  
  if (taskIndex === -1) {
    res.status(404).json({
      success: false,
      error: 'Task not found'
    });
    return;
  }
  
  if (title) tasks[taskIndex].title = title;
  if (description !== undefined) tasks[taskIndex].description = description;
  if (completed !== undefined) tasks[taskIndex].completed = completed;
  tasks[taskIndex].updatedAt = new Date().toISOString();
  
  res.json({
    success: true,
    data: tasks[taskIndex],
    message: 'Task updated successfully'
  });
});

// DELETE /api/tasks/:id - Delete task
router.delete('/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const taskIndex = tasks.findIndex(t => t.id === id);
  
  if (taskIndex === -1) {
    res.status(404).json({
      success: false,
      error: 'Task not found'
    });
    return;
  }
  
  const deletedTask = tasks.splice(taskIndex, 1)[0];
  
  res.json({
    success: true,
    data: deletedTask,
    message: 'Task deleted successfully'
  });
});

export { router as taskRoutes };
