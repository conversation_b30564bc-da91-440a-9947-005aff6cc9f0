@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global smooth theme transitions */
*,
*::before,
*::after {
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter,
    backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

html {
  transition: background-color 300ms ease, color 300ms ease;
}

html,
body {
  @apply bg-white dark:bg-black;

  @media (prefers-color-scheme: dark) {
    color-scheme: dark;
  }
}

html.dark {
  background-color: #000000;
  color: white;
}

/* Prevent flash during theme transitions */
html.dark,
html.dark body,
html.dark * {
  transition: background-color 300ms ease, color 300ms ease,
    border-color 300ms ease !important;
}

/* Custom scrollbar for dark mode */
html.dark ::-webkit-scrollbar {
  width: 8px;
}

html.dark ::-webkit-scrollbar-track {
  background: #1a1a1a;
  transition: background-color 300ms ease;
}

html.dark ::-webkit-scrollbar-thumb {
  background: #4f46e5;
  border-radius: 4px;
  transition: background-color 300ms ease;
}

html.dark ::-webkit-scrollbar-thumb:hover {
  background: #6366f1;
}

/* Light mode scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  transition: background-color 300ms ease;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  transition: background-color 300ms ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Enhanced focus styles */
.focus-visible\:ring-2:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px theme("colors.indigo.500");
}

/* Custom gradient text utility */
.gradient-text {
  @apply bg-gradient-to-r from-indigo-600 to-purple-600 dark:from-indigo-400 dark:to-purple-400 bg-clip-text text-transparent;
  transition: background-image 300ms ease;
}

/* Enhanced card hover effects */
.card-hover {
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.dark .card-hover:hover {
  box-shadow: 0 20px 25px -5px rgba(79, 70, 229, 0.25),
    0 10px 10px -5px rgba(79, 70, 229, 0.1);
}

/* Glass morphism effect with smooth transitions */
.glass {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: background-color 300ms ease, border-color 300ms ease,
    backdrop-filter 300ms ease;
}

.dark .glass {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Improved button hover effects */
.btn-primary {
  @apply bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-semibold px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-secondary {
  @apply border-2 border-indigo-600 text-indigo-600 hover:bg-indigo-600 hover:text-white dark:border-indigo-400 dark:text-indigo-400 dark:hover:bg-indigo-400 dark:hover:text-black font-semibold px-6 py-3 rounded-xl;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Header specific smooth transitions */
.header-bg-transition {
  transition: background-color 300ms ease, backdrop-filter 300ms ease,
    border-color 300ms ease, box-shadow 300ms ease;
}

/* Loading animation */
.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Floating animation */
.float {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Glow effect */
.glow {
  box-shadow: 0 0 20px rgba(79, 70, 229, 0.3);
  transition: box-shadow 300ms ease;
}

.dark .glow {
  box-shadow: 0 0 30px rgba(99, 102, 241, 0.4);
}

/* Text shimmer effect */
.shimmer {
  background: linear-gradient(110deg, #e2e8f0 45%, #f1f5f9 55%, #e2e8f0);
  background-size: 200% 100%;
  animation: shimmer 2s linear infinite;
}

.dark .shimmer {
  background: linear-gradient(110deg, #374151 45%, #4b5563 55%, #374151);
  background-size: 200% 100%;
}

@keyframes shimmer {
  to {
    background-position: -200% 0%;
  }
}

/* Prevent white flash during theme switch */
.theme-transition * {
  transition: background-color 300ms ease, color 300ms ease,
    border-color 300ms ease !important;
}

/* Smooth backdrop transitions */
.backdrop-transition {
  transition: backdrop-filter 300ms ease, background-color 300ms ease;
}
