import type { MetaFunction } from "@remix-run/node";
import { useState, useEffect } from "react";
import { Layout } from "~/components/Layout";
import { Button } from "~/components/ui/Button";
import ReactMarkdown from "react-markdown";
import rehypeHighlight from "rehype-highlight";
import remarkGfm from "remark-gfm";

export const meta: MetaFunction = () => {
  return [{ title: "SiliconAgent.ai - Demo" }];
};

const TASK_OPTIONS = [
  "Struts → Spring",
  "AngularJS (v1) → React",
  "Angular (2+) → React",
  "React → Angular",
  "Java → Python",
  "Python → Java",
  "Java → TypeScript",
  "TypeScript → Java",
  "JavaScript → Python",
  "Python → JavaScript",
  "Java 8 → Java 11",
  "Java 11 → Java 17",
  "Java 17 → Java 21",
  "Flask → FastAPI",
  "Express.js → NestJS",
  "Spring MVC → Spring Boot",
];

export default function Demo() {
  const [taskType, setTaskType] = useState("Java → Python");
  const [code, setCode] = useState("");
  const [transformedCode, setTransformedCode] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    const html = document.documentElement;
    html.classList.toggle(
      "dark",
      window.matchMedia("(prefers-color-scheme: dark)").matches
    );
  }, []);

  const getSystemMessage = (task: string) => {
    return [
      "You are a highly reliable code transformation assistant.",
      "",
      `Your task is to convert the user's input code according to the following transformation: ${task}`,
      "",
      "--------------------------",
      "💡 Output Format Rules:",
      "--------------------------",
      "1. Only return the transformed code, without any markdown syntax (no triple backticks or headings).",
      "2. Return code as raw text. Do NOT prepend it with “Here is the code”, “Here’s your result”, or similar.",
      "3. If necessary, append a plain-text 'Notes:' section (only if there are:",
      "   - Migration caveats,",
      "   - Manual steps,",
      "   - Compatibility considerations,",
      "   - Library import requirements, etc.)",
      "",
      "4. Use only one class/component/file unless the transformation logically demands multiple files.",
      "5. Ensure all output code is valid, idiomatic, and minimal for the target environment or language.",
      "",
      "--------------------------",
      "⛔ Do NOT Include:",
      "--------------------------",
      "- Markdown formatting like triple backticks (```)",
      "- Internal thoughts, explanation, or reasoning (e.g., <think> blocks)",
      "- Any repetition of the input code unless required",
      "- Summaries like “converted from X to Y”",
      "- Import statements unless required for code to compile or run",
      "",
      "--------------------------",
      "🛑 Invalid Input Handling:",
      "--------------------------",
      "If the input is empty, meaningless, or too minimal to transform, reply with:",
      "“The code is empty or invalid. Please provide valid code to transform.”",
    ].join("\n");
  };

  const handleTransform = async () => {
    setError("");
    setTransformedCode("");

    if (!code.trim()) {
      setError("Please enter some code to transform.");
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(
        "https://api.groq.com/openai/v1/chat/completions",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ********************************************************`,
          },
          body: JSON.stringify({
            model: "deepseek-r1-distill-llama-70b",
            messages: [
              {
                role: "system",
                content: getSystemMessage(taskType),
              },
              {
                role: "user",
                content: code,
              },
            ],
            temperature: 0.7,
          }),
        }
      );

      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);

      const data = await response.json();
      const cleanedContent = data.choices?.[0]?.message?.content
        ?.replace(/<think>[\s\S]*?<\/think>/g, "")
        .trim();
      const formattedContent = "```ts\n" + cleanedContent + "\n```";
      setTransformedCode(formattedContent || "No output.");
    } catch (error) {
      console.error("Error transforming code:", error);
      setError("Error transforming code. Please check the console.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Layout>
      <div className="bg-white dark:bg-gray-900 py-24">
        <div className="container mx-auto px-6">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100">
              Code Transformation Demo
            </h1>
            <Button
              onClick={() => {
                document.documentElement.classList.toggle("dark");
              }}
              className="bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
            >
              Toggle Theme
            </Button>
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Select Conversion Task:
            </label>
            <input
              list="task-options"
              value={taskType}
              onChange={(e) => setTaskType(e.target.value)}
              className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white dark:bg-gray-800 dark:text-gray-100 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
            <datalist id="task-options">
              {TASK_OPTIONS.map((option) => (
                <option key={option} value={option} />
              ))}
            </datalist>
          </div>

          <div className="mb-6">
            <label
              htmlFor="code"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              Enter{" "}
              {taskType.includes("→")
                ? taskType.split("→")[0].trim()
                : "source"}{" "}
              Code:
            </label>
            <textarea
              id="code"
              rows={10}
              className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300"
              value={code}
              onChange={(e) => setCode(e.target.value)}
            />
            {error && <p className="text-red-500 text-sm mt-2">{error}</p>}
          </div>

          <Button
            onClick={handleTransform}
            className="bg-indigo-600 hover:bg-indigo-700 text-white"
            disabled={isLoading}
          >
            {isLoading ? "Transforming..." : `Run: ${taskType}`}
          </Button>

          {transformedCode && (
            <div className="mt-8">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                Output
              </h2>
              <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md overflow-x-auto text-sm text-gray-900 dark:text-gray-300">
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  rehypePlugins={[rehypeHighlight]}
                >
                  {transformedCode}
                </ReactMarkdown>
              </div>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
}
