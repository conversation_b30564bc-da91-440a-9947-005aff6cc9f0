import React from "react";
import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { FiMenu, FiX, FiArrowUpRight } from "react-icons/fi";
import { ThemeToggle } from "./ui/ThemeToggle";

export function Header() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const navItems = [
    { name: "Demo", href: "/demo", icon: FiArrowUpRight },
    { name: "Contact Us", href: "/contact", icon: null },
  ];

  const handleNavClick = (href: string, e: React.MouseEvent) => {
    e.preventDefault();
    navigate(href);
  };

  const handleLogoClick = (e: React.MouseEvent) => {
    e.preventDefault();
    navigate("/");
  };

  return (
    <>
      <motion.header
        className={`fixed top-0 left-0 right-0 z-50 header-bg-transition ${
          isScrolled
            ? "bg-white/95 dark:bg-black/95 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-800/50 shadow-lg shadow-gray-200/20 dark:shadow-gray-900/20"
            : "bg-white/0 dark:bg-black/0 border-b border-transparent"
        }`}
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        {/* Animated background gradient with proper theme transitions */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-indigo-500/5 via-purple-500/5 to-pink-500/5 dark:from-indigo-400/5 dark:via-purple-400/5 dark:to-pink-400/5 opacity-0 theme-transition"
          animate={{
            opacity: isScrolled ? [0, 0.5, 0] : 0,
          }}
          transition={{
            duration: 3,
            repeat: isScrolled ? Infinity : 0,
            ease: "easeInOut",
          }}
        />

        <div className="container mx-auto relative z-10">
          <div className="flex items-center justify-between px-4 sm:px-6">
            {/* Logo with enhanced animations and smooth theme transitions */}
            <motion.div
              className="py-4"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <button onClick={handleLogoClick} className="block">
                <motion.div
                  className="relative"
                  whileHover={{ y: -2 }}
                  transition={{ duration: 0.2 }}
                >
                  <h1 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 dark:from-indigo-400 dark:to-purple-400 bg-clip-text text-transparent theme-transition">
                    SiliconAgent.ai
                  </h1>
                  {/* Subtle glow effect with theme transitions */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-indigo-600/20 to-purple-600/20 dark:from-indigo-400/20 dark:to-purple-400/20 rounded-lg blur-lg opacity-0 theme-transition"
                    whileHover={{ opacity: 1 }}
                    transition={{ duration: 0.3 }}
                  />
                </motion.div>
              </button>
            </motion.div>

            {/* Desktop Navigation with smooth theme transitions */}
            <div className="hidden md:flex items-center space-x-8">
              <nav className="flex items-center space-x-6">
                {navItems.map((item, index) => (
                  <motion.div
                    key={item.name}
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <button
                      onClick={(e) => handleNavClick(item.href, e)}
                      className="group relative px-3 py-2 text-gray-700 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 font-medium theme-transition cursor-pointer"
                    >
                      <span className="flex items-center space-x-1">
                        <span>{item.name}</span>
                        {item.icon && (
                          <motion.div
                            className="opacity-0 group-hover:opacity-100 theme-transition"
                            whileHover={{ rotate: 45 }}
                          >
                            <item.icon className="w-3 h-3" />
                          </motion.div>
                        )}
                      </span>

                      {/* Animated underline with theme support */}
                      <motion.div
                        className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-indigo-600 to-purple-600 dark:from-indigo-400 dark:to-purple-400 origin-left theme-transition"
                        initial={{ scaleX: 0 }}
                        whileHover={{ scaleX: 1 }}
                        transition={{ duration: 0.3 }}
                      />
                    </button>
                  </motion.div>
                ))}
              </nav>

              {/* Theme Toggle with enhanced styling and smooth transitions */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, delay: 0.3 }}
                className="relative"
              >
                <div className="p-1 rounded-full bg-gray-100/50 dark:bg-gray-800/50 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 theme-transition backdrop-transition">
                  <ThemeToggle />
                </div>
              </motion.div>
            </div>

            {/* Mobile Menu Button with smooth theme transitions */}
            <div className="md:hidden flex items-center space-x-4">
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4 }}
              >
                <div className="p-1 rounded-full bg-gray-100/50 dark:bg-gray-800/50 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 theme-transition">
                  <ThemeToggle />
                </div>
              </motion.div>

              <motion.button
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300 theme-transition"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                aria-label="Toggle mobile menu"
              >
                <motion.div
                  animate={{ rotate: isMobileMenuOpen ? 180 : 0 }}
                  transition={{ duration: 0.2 }}
                >
                  {isMobileMenuOpen ? (
                    <FiX className="w-5 h-5" />
                  ) : (
                    <FiMenu className="w-5 h-5" />
                  )}
                </motion.div>
              </motion.button>
            </div>
          </div>
        </div>
      </motion.header>

      {/* Mobile Menu Overlay with smooth theme transitions */}
      <motion.div
        className={`fixed inset-0 z-40 md:hidden ${
          isMobileMenuOpen ? "pointer-events-auto" : "pointer-events-none"
        }`}
        initial={{ opacity: 0 }}
        animate={{ opacity: isMobileMenuOpen ? 1 : 0 }}
        transition={{ duration: 0.3 }}
      >
        {/* Backdrop with theme transitions */}
        <motion.div
          className="absolute inset-0 bg-black/50 dark:bg-black/70 backdrop-blur-sm theme-transition backdrop-transition"
          initial={{ opacity: 0 }}
          animate={{ opacity: isMobileMenuOpen ? 1 : 0 }}
          transition={{ duration: 0.3 }}
          onClick={() => setIsMobileMenuOpen(false)}
        />

        {/* Mobile Menu Panel with smooth theme transitions */}
        <motion.div
          className="absolute top-0 right-0 h-full w-72 bg-white/95 dark:bg-black/95 backdrop-blur-xl border-l border-gray-200/50 dark:border-gray-800/50 shadow-2xl theme-transition backdrop-transition"
          initial={{ x: "100%" }}
          animate={{ x: isMobileMenuOpen ? 0 : "100%" }}
          transition={{ type: "spring", damping: 20, stiffness: 300 }}
        >
          <div className="pt-20 px-6">
            <nav className="space-y-4">
              {navItems.map((item, index) => (
                <motion.div
                  key={item.name}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{
                    opacity: isMobileMenuOpen ? 1 : 0,
                    x: isMobileMenuOpen ? 0 : 20,
                  }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <button
                    onClick={(e) => {
                      handleNavClick(item.href, e);
                      setIsMobileMenuOpen(false);
                    }}
                    className="w-full flex items-center justify-between p-4 rounded-xl bg-gray-50 dark:bg-gray-900/50 border border-gray-200/50 dark:border-gray-700/50 text-gray-700 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 theme-transition cursor-pointer"
                  >
                    <span className="font-medium">{item.name}</span>
                    {item.icon && <item.icon className="w-4 h-4 opacity-60" />}
                  </button>
                </motion.div>
              ))}
            </nav>

            {/* Mobile menu footer with theme transitions */}
            <motion.div
              className="mt-8 pt-6 border-t border-gray-200/50 dark:border-gray-700/50 theme-transition"
              initial={{ opacity: 0, y: 20 }}
              animate={{
                opacity: isMobileMenuOpen ? 1 : 0,
                y: isMobileMenuOpen ? 0 : 20,
              }}
              transition={{ duration: 0.3, delay: 0.3 }}
            >
              <p className="text-sm text-gray-500 dark:text-gray-400 text-center theme-transition">
                Transform your legacy systems with AI precision
              </p>
            </motion.div>
          </div>
        </motion.div>
      </motion.div>

      {/* Spacer to prevent content jump with theme transitions */}
      <div className="h-16 sm:h-20 bg-white dark:bg-black theme-transition" />
    </>
  );
}
