import React, { useState, useEffect } from 'react';
import { apiService } from '../services/api';

const Dashboard: React.FC = () => {
  const [apiStatus, setApiStatus] = useState<{
    isConnected: boolean;
    message?: string;
    timestamp?: string;
    version?: string;
    error?: string;
  }>({ isConnected: false });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkApiHealth();
  }, []);

  const checkApiHealth = async () => {
    try {
      setLoading(true);
      const response = await apiService.healthCheck();
      setApiStatus({
        isConnected: true,
        message: response.message,
        timestamp: response.timestamp,
        version: response.version,
      });
    } catch (err: any) {
      setApiStatus({
        isConnected: false,
        error: err.message || 'Failed to connect to API',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="dashboard">
      <h2>Dashboard</h2>
      
      <div className="api-status-card">
        <h3>API Status</h3>
        {loading ? (
          <p>Checking API connection...</p>
        ) : (
          <div className={`status ${apiStatus.isConnected ? 'connected' : 'disconnected'}`}>
            <div className="status-indicator">
              <span className={`dot ${apiStatus.isConnected ? 'green' : 'red'}`}></span>
              <span>{apiStatus.isConnected ? 'Connected' : 'Disconnected'}</span>
            </div>
            
            {apiStatus.isConnected ? (
              <div className="api-info">
                <p><strong>Message:</strong> {apiStatus.message}</p>
                <p><strong>Version:</strong> {apiStatus.version}</p>
                <p><strong>Last Check:</strong> {apiStatus.timestamp ? new Date(apiStatus.timestamp).toLocaleString() : 'N/A'}</p>
              </div>
            ) : (
              <div className="api-error">
                <p><strong>Error:</strong> {apiStatus.error}</p>
                <p>Make sure the backend server is running on port 3000</p>
              </div>
            )}
            
            <button onClick={checkApiHealth} className="refresh-btn">
              Refresh Status
            </button>
          </div>
        )}
      </div>

      <div className="quick-stats">
        <h3>Quick Start Guide</h3>
        <div className="guide-steps">
          <div className="step">
            <h4>1. Start the Backend</h4>
            <p>Navigate to the backend directory and run:</p>
            <code>npm run dev</code>
          </div>
          <div className="step">
            <h4>2. API Endpoints Available</h4>
            <ul>
              <li><code>GET /api/users</code> - Get all users</li>
              <li><code>POST /api/users</code> - Create a user</li>
              <li><code>GET /api/tasks</code> - Get all tasks</li>
              <li><code>POST /api/tasks</code> - Create a task</li>
            </ul>
          </div>
          <div className="step">
            <h4>3. Test the Integration</h4>
            <p>Use the Users and Tasks tabs to test the full-stack integration.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
