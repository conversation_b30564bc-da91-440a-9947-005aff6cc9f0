import React from 'react';
    import { dashboardData } from './mockData/dashboardData';
    import { Sparkles } from 'lucide-react';

    const Dashboard: React.FC = () => {
      return (
        <div className="p-6 bg-gray-900 text-white rounded-lg shadow-md">
          <h1 className="text-3xl font-bold mb-6 flex items-center">
            <Sparkles className="mr-2 h-6 w-6 text-purple-500 animate-spin" />
            Dashboard
          </h1>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Key Metrics Cards */}
            <div className="bg-gray-800 p-4 rounded-lg shadow">
              <h2 className="text-lg font-semibold">Active Agents</h2>
              <p className="text-3xl font-bold text-purple-400">{dashboardData.activeAgents}</p>
            </div>
            <div className="bg-gray-800 p-4 rounded-lg shadow">
              <h2 className="text-lg font-semibold">Running Executions</h2>
              <p className="text-3xl font-bold text-purple-400">{dashboardData.runningExecutions}</p>
            </div>
            <div className="bg-gray-800 p-4 rounded-lg shadow">
              <h2 className="text-lg font-semibold">Completed Executions</h2>
              <p className="text-3xl font-bold text-purple-400">{dashboardData.completedExecutions}</p>
            </div>
            <div className="bg-gray-800 p-4 rounded-lg shadow">
              <h2 className="text-lg font-semibold">Total Applications</h2>
              <p className="text-3xl font-bold text-purple-400">{dashboardData.totalApplications}</p>
            </div>

            {/* Recent Activity */}
            <div className="bg-gray-800 p-4 rounded-lg shadow col-span-full lg:col-span-3">
              <h2 className="text-lg font-semibold mb-4">Recent Activity</h2>
              <ul>
                {dashboardData.recentActivity.map((activity, index) => (
                  <li key={index} className={`mb-2 p-2 rounded ${
                    activity.type === 'success' ? 'bg-green-700 bg-opacity-50' :
                    activity.type === 'error' ? 'bg-red-700 bg-opacity-50' : 'bg-blue-700 bg-opacity-50'
                  }`}>
                    <span className="font-medium">{activity.time}</span> - {activity.event}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      );
    };

    export default Dashboard;
