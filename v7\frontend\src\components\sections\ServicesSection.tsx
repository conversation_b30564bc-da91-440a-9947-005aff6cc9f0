import { useState, useEffect, useRef } from "react";
import { motion, useScroll, useTransform } from "framer-motion";
import { FiServer } from "react-icons/fi";
import { AnimatedSection } from "../animations/index";
import { servicesData } from "../../data/transformServices";
import { ServiceItem } from "./ServiceItem";

export function ServicesSection() {
  const [activeService, setActiveService] = useState(0);
  const servicesRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: servicesRef,
    offset: ["start end", "end start"],
  });

  // Transform scroll progress to service index
  const serviceProgress = useTransform(
    scrollYProgress,
    [0.1, 0.9],
    [0, servicesData.length - 1]
  );

  useEffect(() => {
    const unsubscribe = serviceProgress.onChange((latest) => {
      const newIndex = Math.round(latest);
      if (
        newIndex !== activeService &&
        newIndex >= 0 &&
        newIndex < servicesData.length
      ) {
        setActiveService(newIndex);
      }
    });

    return unsubscribe;
  }, [serviceProgress, activeService]);

  return (
    <section className="services mb-16" ref={servicesRef}>
      <AnimatedSection>
        <motion.div
          className="flex items-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 p-3 mr-4"
            whileHover={{ scale: 1.1, rotate: 5 }}
          >
            <FiServer className="text-white w-6 h-6" />
          </motion.div>
          <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
            Transformation Services
          </h2>
        </motion.div>
      </AnimatedSection>

      {/* Sequential Service Sections */}
      <div className="space-y-32">
        {servicesData.map((service, index) => (
          <ServiceItem
            key={service.id}
            service={service}
            index={index}
            servicesData={servicesData}
          />
        ))}
      </div>
    </section>
  );
}
