import React from 'react';
    import { agentChainsData } from './mockData/manageAgentChainsData';
    import { CheckCircle, Loader2 } from 'lucide-react';

    const ManageAgentChains: React.FC = () => {
      return (
        <div className="p-6 bg-gray-900 text-white rounded-lg shadow-md">
          <h1 className="text-3xl font-bold mb-6">Manage Agent Chains</h1>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {agentChainsData.map((chain) => (
              <div key={chain.id} className="bg-gray-800 p-4 rounded-lg shadow">
                <h2 className="text-lg font-semibold flex items-center">
                  {chain.name}
                  {chain.status === 'active' && <CheckCircle className="text-green-500 ml-2" size={16} />}
                  {chain.status === 'idle' && <Loader2 className="text-yellow-500 ml-2 animate-spin" size={16} />}
                </h2>
                <p className="text-gray-400 text-sm mb-2">Status: {chain.status}</p>
                <p className="text-gray-400 text-sm mb-2">Agents: {chain.agents.join(', ')}</p>
                <p className="text-gray-400 text-sm">Last Execution: {chain.lastExecution}</p>
              </div>
            ))}
          </div>
        </div>
      );
    };

    export default ManageAgentChains;
