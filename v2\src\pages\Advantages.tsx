import { motion } from 'framer-motion';
import { Code, Shield, Zap, BarChart2, Bot, Database } from 'lucide-react';

const advantages = [
    {
        icon: <Code className="w-8 h-8 text-[var(--primary)]" />,
        title: 'AI-Driven Automation',
        description: 'Boost efficiency with intelligent automation.'
    },
    {
        icon: <Zap className="w-8 h-8 text-[var(--secondary)]" />,
        title: 'No Coding Required',
        description: 'Build and deploy agents without writing code.'
    },
    {
        icon: <BarChart2 className="w-8 h-8 text-purple-500" />,
        title: 'Multi-LLM Support',
        description: 'Leverage multiple AI models for diverse tasks.'
    },
    {
        icon: <Bot className="w-8 h-8 text-green-500" />,
        title: 'Customizable & Scalable',
        description: 'Tailor agents to your specific needs and scale effortlessly.'
    },
    {
        icon: <Shield className="w-8 h-8 text-blue-500" />,
        title: 'Business-Focused',
        description: 'Designed for real-world business applications.'
    },
    {
        icon: <Database className="w-8 h-8 text-orange-500" />,
        title: 'Enterprise-Ready Security',
        description: 'Ensure data protection and compliance.'
    }
];

const Advantages = () => {
    return (
        <main className="py-24">
            <div className="container">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-center mb-16"
                >
                    <h1 className="text-4xl md:text-5xl font-bold mb-6">
                        Why Choose <span className="gradient-text">siliconagent.ai?</span>
                    </h1>
                </motion.div>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {advantages.map((advantage, index) => (
                        <motion.div
                            key={index}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.5, delay: index * 0.1 }}
                            className="card flex p-6 rounded-lg shadow-lg"
                        >
                            <div className="flex items-center justify-center w-1/4">
                                {advantage.icon}
                            </div>
                            <div className="w-3/4 pl-4">
                                <h3 className="text-xl font-semibold mb-2">{advantage.title}</h3>
                                <p className="text-gray-400">{advantage.description}</p>
                            </div>
                        </motion.div>
                    ))}
                </div>
            </div>
        </main>
    );
};

export default Advantages;
