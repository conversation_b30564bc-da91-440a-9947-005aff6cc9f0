import React from 'react';
    import { CheckCircle, Code, Monitor, Settings, Zap } from 'lucide-react';

    const HowItWorks: React.FC = () => {
      return (
        <div className="bg-gray-900 text-white min-h-screen p-8">
          <div className="container mx-auto">
            <h1 className="text-4xl font-bold text-center mb-12">How It Works</h1>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Step 1 */}
              <div className="flex items-center">
                <div className="mr-4">
                  <Code className="h-12 w-12 text-purple-500" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold mb-2">Create an AI Agent</h2>
                  <p className="text-gray-300">Use predefined templates or define custom logic.</p>
                </div>
              </div>

              {/* Step 2 */}
              <div className="flex items-center">
                <div className="mr-4">
                  <Settings className="h-12 w-12 text-purple-500" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold mb-2">Configure & Train</h2>
                  <p className="text-gray-300">Provide datasets or fine-tune AI behavior.</p>
                </div>
              </div>

              {/* Step 3 */}
              <div className="flex items-center">
                <div className="mr-4">
                  <Zap className="h-12 w-12 text-purple-500" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold mb-2">Deploy & Execute</h2>
                  <p className="text-gray-300">Run AI agents for different tasks.</p>
                </div>
              </div>

              {/* Step 4 */}
              <div className="flex items-center">
                <div className="mr-4">
                  <Monitor className="h-12 w-12 text-purple-500" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold mb-2">Monitor & Optimize</h2>
                  <p className="text-gray-300">Improve performance with AI insights.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    };

    export default HowItWorks;
