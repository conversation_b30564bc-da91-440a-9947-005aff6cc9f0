import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Bot, X, MailCheck, FileHeartIcon, SearchCheck, Waypoints, ReceiptText } from 'lucide-react';

interface Agent {
  id: number;
  name: string;
  description: string;
  icon: JSX.Element;
  category: string;
  rating: number;
  features: string[];
  useCases: string[];
  pricing: string;
}

const agents: Agent[] = [
  {
    id: 1,
    name: 'Email Automation Agent',
    description: 'Automate your email campaigns and follow-ups with our intelligent Email Automation Agent. Schedule, send, and track emails effortlessly.',
    icon: <MailCheck className="w-8 h-8 text-[var(--primary)]" />,
    category: 'Development',
    rating: 4.9,
    features: [
      'Real-time code suggestions',
      'Multi-language support',
      'Code refactoring',
      'Bug detection',
      'Performance optimization'
    ],
    useCases: [
      'Web development',
      'Mobile app development',
      'Code review automation'
    ],
    pricing: 'Starting at $29/month'
  },
  {
    id: 2,
    name: 'Data Extraction Agent',
    description: 'Extract valuable data from various sources including websites, documents, and databases with our Data Extraction Agent. Simplify your data collection process.',
    icon: <FileHeartIcon className="w-8 h-8 text-[var(--secondary)]" />,
    category: 'Analytics',
    rating: 4.8,
    features: [
      'Automated data processing',
      'Custom visualizations',
      'Predictive analytics',
      'Report generation',
      'Data cleaning'
    ],
    useCases: [
      'Business intelligence',
      'Market analysis',
      'Performance tracking'
    ],
    pricing: 'Starting at $49/month'
  },
  {
    id: 3,
    name: 'SEO Optimization Agent',
    description: 'Boost your website’s search engine ranking with our SEO Optimization Agent. Analyze keywords, optimize content, and track performance.',
    icon: <SearchCheck className="w-8 h-8 text-blue-500" />,
    category: 'Automation',
    rating: 4.9,
    features: [
      'Task automation',
      'Schedule management',
      'Email processing',
      'Document generation',
      'Workflow optimization'
    ],
    useCases: [
      'Personal productivity',
      'Team collaboration',
      'Project management'
    ],
    pricing: 'Starting at $19/month'
  },
  {
    id: 4,
    name: 'API Connector Agent',
    description: 'Seamlessly integrate and connect different APIs with our API Connector Agent. Facilitate data exchange and automate workflows between systems.',
    icon: <Waypoints className="w-8 h-8 text-green-500" />,
    category: 'Security',
    rating: 4.9,
    features: [
      'Real-time monitoring',
      'Threat detection',
      'Automated response',
      'Security reporting',
      'Compliance checking'
    ],
    useCases: [
      'Network security',
      'Application security',
      'Data protection'
    ],
    pricing: 'Starting at $79/month'
  },
  {
    id: 5,
    name: 'AI Chatbot Agent',
    description: 'Enhance customer engagement with our AI Chatbot Agent. Provide instant responses, handle queries, and improve customer satisfaction.',
    icon: <Bot className="w-8 h-8 text-purple-500" />,
    category: 'Machine Learning',
    rating: 4.8,
    features: [
      'Automated model training',
      'Hyperparameter optimization',
      'Model deployment',
      'Performance monitoring',
      'Version control'
    ],
    useCases: [
      'Custom ML models',
      'AutoML pipelines',
      'Model optimization'
    ],
    pricing: 'Starting at $99/month'
  },
  {
    id: 6,
    name: 'Invoice Processing Agent',
    description: 'Automate your invoice processing with our Invoice Processing Agent. Extract data, validate information, and streamline your accounting processes.',
    icon: <ReceiptText className="w-8 h-8 text-orange-500" />,
    category: 'Database',
    rating: 4.7,
    features: [
      'Query optimization',
      'Index management',
      'Backup automation',
      'Performance tuning',
      'Schema optimization'
    ],
    useCases: [
      'Database administration',
      'Performance optimization',
      'Data management'
    ],
    pricing: 'Starting at $59/month'
  }
];

interface AgentCardProps {
  agent: Agent;
}

const AgentCard: React.FC<AgentCardProps> = ({ agent }) => {
  const [isFlipped, setIsFlipped] = useState(false);

  return (
    <div className="flip-card h-[400px]">
      <div className={`flip-card-inner ${isFlipped ? 'flipped' : ''}`}>
        {/* Front of the card */}
        <motion.div
          className="flip-card-front card card-hover p-6 flex flex-col"
          whileHover={{ scale: 1.02 }}
          transition={{ type: "spring", stiffness: 300 }}
        >
          <div className="flex items-center gap-4 mb-4">
            {agent.icon}
            <div>
              <h3 className="text-xl font-semibold">{agent.name}</h3>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-400">{agent.category}</span>
                <span className="text-sm text-yellow-500">★ {agent.rating}</span>
              </div>
            </div>
          </div>
          <p className="text-gray-400 mb-4 flex-grow">{agent.description}</p>
          <button
            onClick={() => setIsFlipped(true)}
            className="text-[var(--primary)] hover:text-[var(--secondary)] transition-colors inline-flex items-center gap-2"
          >
            Learn More →
          </button>
        </motion.div>

        {/* Back of the card */}
        <div className="flip-card-back card p-6 overflow-y-auto">
          <div className="flex justify-between items-start mb-4">
            <h3 className="text-xl font-semibold">{agent.name}</h3>
            <button
              onClick={() => setIsFlipped(false)}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <X size={20} />
            </button>
          </div>

          <div className="space-y-4">
            <div>
              <h4 className="text-lg font-semibold mb-2">Key Features</h4>
              <ul className="list-disc list-inside text-gray-400 space-y-1">
                {agent.features.map((feature, index) => (
                  <li key={index}>{feature}</li>
                ))}
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-2">Use Cases</h4>
              <ul className="list-disc list-inside text-gray-400 space-y-1">
                {agent.useCases.map((useCase, index) => (
                  <li key={index}>{useCase}</li>
                ))}
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-2">Pricing</h4>
              <p className="text-gray-400">{agent.pricing}</p>
            </div>

            <button className="w-full btn-primary mt-4">
              Get Started
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

const Agents = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All Categories');

  const filteredAgents = agents.filter(agent => {
    const matchesSearch = agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      agent.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'All Categories' || agent.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const categories = ['All Categories', ...new Set(agents.map(agent => agent.category))];

  return (
    <main className="py-24">
      <div className="container">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Discover Our AI Agents
          </h1>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            Explore our collection of specialized AI agents designed to enhance your workflow and boost productivity.
          </p>
        </motion.div>

        {/* Search and Filter */}
        <div className="flex flex-col md:flex-row gap-4 mb-12">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search AI agents..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 rounded-lg bg-[var(--card)] text-white border border-gray-700 focus:border-[var(--primary)] focus:outline-none"
            />
          </div>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-4 py-2 rounded-lg bg-[var(--card)] text-white border border-gray-700 focus:border-[var(--primary)] focus:outline-none"
          >
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
        </div>

        {/* Agents Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredAgents.map((agent, index) => (
            <motion.div
              key={agent.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <AgentCard agent={agent} />
            </motion.div>
          ))}
        </div>
      </div>
    </main>
  );
};

export default Agents;