<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/vite.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>SiliconAgent.ai - AI-Powered Automation Platform</title>

  <meta name="description"
    content="SiliconAgent.ai is an AI-driven platform for building, managing, and executing automated workflows with AI-powered agents." />
  <meta name="keywords"
    content="AI automation, AI agents, workflow automation, intelligent automation, machine learning, AI-powered tools" />
  <meta name="author" content="SiliconAgent.ai" />

  <!-- Open Graph Meta Tags for Social Media -->
  <meta property="og:title" content="SiliconAgent.ai - AI-powered Automation Platform" />
  <meta property="og:description" content="Build, manage, and execute AI-driven workflows with SiliconAgent.ai." />
  <meta property="og:image" content="/og-image.png" />
  <meta property="og:url" content="https://siliconagent.ai" />
  <meta property="og:type" content="website" />

  <!-- Twitter Card for Better Sharing -->
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:title" content="SiliconAgent.ai - AI-powered Automation Platform" />
  <meta name="twitter:description" content="Build, manage, and execute AI-driven workflows with SiliconAgent.ai." />
  <meta name="twitter:image" content="/twitter-image.png" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="/assets/favicon-Dir_3EOT.svg" />
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />

  <!-- Google Analytics -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXXXX"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());
    gtag('config', 'G-XXXXXXXXXX');
  </script>
  <script type="module" crossorigin src="/assets/index-BK5cBUaf.js"></script>
  <link rel="stylesheet" crossorigin href="/assets/index-O35u0q5e.css">
</head>

<body>
  <div id="root"></div>
</body>

</html>