import React from 'react';
    import { apiManagementData } from './mockData/apiManagementData';
    import { KeyRound, Link, CheckCircle, XCircle } from 'lucide-react';

    const ApiManagement: React.FC = () => {
      return (
        <div className="p-6 bg-gray-900 text-white rounded-lg shadow-md">
          <h1 className="text-3xl font-bold mb-6 flex items-center">
            <KeyRound className="mr-2" /> API &amp; Webhook Management
          </h1>

          <h2 className="text-xl font-semibold mb-4">API Keys</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            {apiManagementData.apiKeys.map((key) => (
              <div key={key.id} className="bg-gray-800 p-4 rounded-lg shadow">
                <h3 className="text-lg font-semibold flex items-center">
                  {key.name} {key.status === 'active' && <CheckCircle className='text-green-500 ml-2' size={16} />} {key.status === 'inactive' && <XCircle className='text-red-500 ml-2' size={16} />}
                </h3>
                <p className="text-gray-400 text-sm">Created: {key.created}</p>
                <p className="text-gray-400 text-sm">Requests: {key.requests}</p>
              </div>
            ))}
          </div>

          <h2 className="text-xl font-semibold mb-4">Webhooks</h2>
          <div className="grid grid-cols-1 gap-6">
            {apiManagementData.webhooks.map((hook) => (
              <div key={hook.id} className="bg-gray-800 p-4 rounded-lg shadow">
                <h3 className="text-lg font-semibold flex items-center">
                  <Link className="mr-2" /> {hook.url} {hook.status === 'active' && <CheckCircle className='text-green-500 ml-2' size={16} />} {hook.status === 'error' && <XCircle className='text-red-500 ml-2' size={16} />}
                </h3>
                <p className="text-gray-400 text-sm">Events: {hook.events.join(', ')}</p>
              </div>
            ))}
          </div>
        </div>
      );
    };

    export default ApiManagement;
