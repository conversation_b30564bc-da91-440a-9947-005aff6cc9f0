import React from 'react';
    import { Check, X, DollarSign } from 'lucide-react';

    const Pricing: React.FC = () => {
      return (
        <div className="bg-gray-900 text-white min-h-screen p-8">
          <div className="container mx-auto">
            <h1 className="text-4xl font-bold text-center mb-12">Pricing Plans</h1>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Free Plan */}
              <div className="bg-gray-800 p-6 rounded-lg shadow-lg">
                <h2 className="text-2xl font-bold mb-4">Free Plan</h2>
                <p className="text-gray-400 mb-4">Get started with limited access.</p>
                <p className="text-4xl font-bold mb-4"><DollarSign className="inline-block h-8 w-8" />0</p>
                <ul className="mb-4">
                  <li className="flex items-center mb-2"><Check className="mr-2 h-4 w-4 text-green-500" /> Limited Agents</li>
                  <li className="flex items-center mb-2"><Check className="mr-2 h-4 w-4 text-green-500" /> Limited Executions</li>
                  <li className="flex items-center mb-2"><X className="mr-2 h-4 w-4 text-red-500" /> Multi-LLM Support</li>
                  <li className="flex items-center mb-2"><X className="mr-2 h-4 w-4 text-red-500" /> Advanced Features</li>
                </ul>
                <button className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                  Get Started
                </button>
              </div>

              {/* Pro Plan */}
              <div className="bg-gray-800 p-6 rounded-lg shadow-lg">
                <h2 className="text-2xl font-bold mb-4">Pro Plan</h2>
                <p className="text-gray-400 mb-4">Full access to all features.</p>
                <p className="text-4xl font-bold mb-4"><DollarSign className="inline-block h-8 w-8" />49<span className="text-lg">/month</span></p>
                <ul className="mb-4">
                  <li className="flex items-center mb-2"><Check className="mr-2 h-4 w-4 text-green-500" /> 100+ Agents</li>
                  <li className="flex items-center mb-2"><Check className="mr-2 h-4 w-4 text-green-500" /> Unlimited Executions</li>
                  <li className="flex items-center mb-2"><Check className="mr-2 h-4 w-4 text-green-500" /> Multi-LLM Support</li>
                  <li className="flex items-center mb-2"><Check className="mr-2 h-4 w-4 text-green-500" /> Priority Support</li>
                </ul>
                <button className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                  Buy Pro
                </button>
              </div>

              {/* Enterprise Plan */}
              <div className="bg-gray-800 p-6 rounded-lg shadow-lg">
                <h2 className="text-2xl font-bold mb-4">Enterprise Plan</h2>
                <p className="text-gray-400 mb-4">Custom solutions for your business.</p>
                <p className="text-4xl font-bold mb-4">Contact Us</p>
                <ul className="mb-4">
                  <li className="flex items-center mb-2"><Check className="mr-2 h-4 w-4 text-green-500" /> Custom AI Agents</li>
                  <li className="flex items-center mb-2"><Check className="mr-2 h-4 w-4 text-green-500" /> Dedicated Support</li>
                  <li className="flex items-center mb-2"><Check className="mr-2 h-4 w-4 text-green-500" /> Custom Integrations</li>
                  <li className="flex items-center mb-2"><Check className="mr-2 h-4 w-4 text-green-500" /> On-Premise Deployment</li>
                </ul>
                <button className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                  Contact Sales
                </button>
              </div>
            </div>
          </div>
        </div>
      );
    };

    export default Pricing;
