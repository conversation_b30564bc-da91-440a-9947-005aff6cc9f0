import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>C<PERSON>, FiZap } from "react-icons/fi";

export function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <motion.footer
      className="bg-gray-100 dark:bg-black py-12 relative overflow-hidden"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6 }}
    >
      {/* Animated background elements */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute top-0 left-1/4 w-32 h-32 bg-indigo-500/10 dark:bg-indigo-500/5 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.5, 0.8, 0.5],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="absolute bottom-0 right-1/4 w-24 h-24 bg-purple-500/10 dark:bg-purple-500/5 rounded-full blur-2xl"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.8, 0.5, 0.8],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1,
          }}
        />
      </div>

      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        <motion.div
          className="text-center"
          initial={{ y: 30, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {/* Animated logo */}
          <motion.div
            className="mb-6"
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.2 }}
          >
            <h3 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 dark:from-indigo-400 dark:to-purple-400 bg-clip-text text-transparent">
              SiliconAgent.ai
            </h3>
          </motion.div>

          {/* Animated tagline */}
          <motion.p
            className="text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            Transforming legacy systems with AI precision and innovation.
          </motion.p>

          {/* Animated icons */}
          <motion.div
            className="flex justify-center space-x-6 sm:space-x-8 mb-8"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <motion.div
              className="flex flex-col items-center group cursor-pointer"
              whileHover={{ scale: 1.1, y: -5 }}
              transition={{ duration: 0.2 }}
            >
              <motion.div
                className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-gradient-to-r from-indigo-100 to-purple-100 dark:from-indigo-500/20 dark:to-purple-500/20 flex items-center justify-center mb-2 group-hover:from-indigo-200 group-hover:to-purple-200 dark:group-hover:from-indigo-500/30 dark:group-hover:to-purple-500/30 transition-all duration-300 border border-indigo-200/50 dark:border-indigo-500/20"
                whileHover={{ rotate: 5 }}
              >
                <FiCode className="w-4 h-4 sm:w-5 sm:h-5 text-indigo-600 dark:text-indigo-400" />
              </motion.div>
              <span className="text-xs text-gray-500 dark:text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-400 transition-colors">
                Transform
              </span>
            </motion.div>

            <motion.div
              className="flex flex-col items-center group cursor-pointer"
              whileHover={{ scale: 1.1, y: -5 }}
              transition={{ duration: 0.2 }}
            >
              <motion.div
                className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-500/20 dark:to-pink-500/20 flex items-center justify-center mb-2 group-hover:from-purple-200 group-hover:to-pink-200 dark:group-hover:from-purple-500/30 dark:group-hover:to-pink-500/30 transition-all duration-300 border border-purple-200/50 dark:border-purple-500/20"
                whileHover={{ rotate: -5 }}
              >
                <FiZap className="w-4 h-4 sm:w-5 sm:h-5 text-purple-600 dark:text-purple-400" />
              </motion.div>
              <span className="text-xs text-gray-500 dark:text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-400 transition-colors">
                Innovate
              </span>
            </motion.div>

            <motion.div
              className="flex flex-col items-center group cursor-pointer"
              whileHover={{ scale: 1.1, y: -5 }}
              transition={{ duration: 0.2 }}
            >
              <motion.div
                className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-gradient-to-r from-pink-100 to-red-100 dark:from-pink-500/20 dark:to-red-500/20 flex items-center justify-center mb-2 group-hover:from-pink-200 group-hover:to-red-200 dark:group-hover:from-pink-500/30 dark:group-hover:to-red-500/30 transition-all duration-300 border border-pink-200/50 dark:border-pink-500/20"
                whileHover={{ rotate: 5 }}
              >
                <FiHeart className="w-4 h-4 sm:w-5 sm:h-5 text-pink-600 dark:text-pink-400" />
              </motion.div>
              <span className="text-xs text-gray-500 dark:text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-400 transition-colors">
                Care
              </span>
            </motion.div>
          </motion.div>

          {/* Animated divider */}
          <motion.div
            className="w-32 h-px bg-gradient-to-r from-transparent via-gray-400 dark:via-gray-600 to-transparent mx-auto mb-6"
            initial={{ scaleX: 0 }}
            whileInView={{ scaleX: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.8 }}
          />

          {/* Copyright with animated elements */}
          <motion.div
            className="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-2 text-gray-600 dark:text-gray-500"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 1 }}
          >
            <div className="flex items-center space-x-2">
              <span className="text-sm">
                &copy; {currentYear} SiliconAgent.ai. All rights reserved.
              </span>
              <motion.span
                animate={{ opacity: [0.5, 1, 0.5] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="text-indigo-500 dark:text-indigo-400"
              >
                •
              </motion.span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-gray-500 dark:text-gray-600 text-sm">
                Made with
              </span>
              <motion.div
                animate={{
                  scale: [1, 1.2, 1],
                }}
                transition={{ duration: 1.5, repeat: Infinity }}
                className="text-pink-500 dark:text-pink-400"
              >
                <FiHeart className="w-4 h-4" />
              </motion.div>
              <span className="text-gray-500 dark:text-gray-600 text-sm">
                for innovation
              </span>
            </div>
          </motion.div>

          {/* Additional subtle animation for enhanced engagement */}
          <motion.div
            className="mt-4 opacity-30"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 0.3 }}
            viewport={{ once: true }}
            transition={{ duration: 1, delay: 1.2 }}
          >
            <motion.div
              className="w-1 h-1 bg-indigo-500 dark:bg-indigo-400 rounded-full mx-auto"
              animate={{
                scale: [0, 1, 0],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
          </motion.div>
        </motion.div>
      </div>
    </motion.footer>
  );
}
