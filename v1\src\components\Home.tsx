import React from 'react';

    const Home: React.FC = () => {
      return (
        <div className="bg-gray-900 text-white min-h-screen">
          {/* Hero Section */}
          <div className="container mx-auto px-4 py-32 flex flex-col items-center justify-center">
            <h1 className="text-4xl md:text-6xl font-bold text-center mb-6">
              Build, Deploy, and Automate with AI Agents
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 text-center mb-12">
              Empowering businesses, developers, and individuals with 100+ AI-powered agents.
            </p>
            <div className="flex space-x-4">
              <a href="/marketplace" className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-6 rounded">
                Explore AI Agents
              </a>
              <a href="/signup" className="bg-gray-700 hover:bg-gray-800 text-white font-bold py-2 px-6 rounded">
                Get Started
              </a>
            </div>
          </div>

          {/* Introduction */}
          <div className="container mx-auto px-4 py-16">
            <h2 className="text-3xl font-bold text-center mb-8">What is siliconagent.ai?</h2>
            <p className="text-lg text-gray-300 text-center mb-12">
              siliconagent.ai is an AI Agent Creation and Execution Platform designed to help you automate workflows, analyze data, and enhance productivity.  We offer over 100 pre-built AI agents to get you started.
            </p>
            {/* Animated Statistics (Placeholder) */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-gray-800 p-6 rounded-lg shadow-lg text-center">
                <h3 className="text-2xl font-bold mb-2">100+</h3>
                <p className="text-gray-300">AI Agents</p>
              </div>
              <div className="bg-gray-800 p-6 rounded-lg shadow-lg text-center">
                <h3 className="text-2xl font-bold mb-2">10x</h3>
                <p className="text-gray-300">Faster Automation</p>
              </div>
              <div className="bg-gray-800 p-6 rounded-lg shadow-lg text-center">
                <h3 className="text-2xl font-bold mb-2">99.9%</h3>
                <p className="text-gray-300">Uptime</p>
              </div>
            </div>
          </div>
        </div>
      );
    };

    export default Home;
