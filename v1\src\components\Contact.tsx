import React from 'react';
    import { Mail, Phone, Linkedin, Twitter, Facebook } from 'lucide-react';

    const Contact: React.FC = () => {
      return (
        <div className="bg-gray-900 text-white min-h-screen p-8">
          <div className="container mx-auto">
            <h1 className="text-4xl font-bold text-center mb-12">Contact Us</h1>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Contact Form */}
              <div>
                <h2 className="text-2xl font-bold mb-4">Get in Touch</h2>
                <form>
                  <div className="mb-4">
                    <label htmlFor="name" className="block text-gray-300 mb-2">Name</label>
                    <input type="text" id="name" className="bg-gray-800 text-white w-full p-2 rounded" />
                  </div>
                  <div className="mb-4">
                    <label htmlFor="email" className="block text-gray-300 mb-2">Email</label>
                    <input type="email" id="email" className="bg-gray-800 text-white w-full p-2 rounded" />
                  </div>
                  <div className="mb-4">
                    <label htmlFor="message" className="block text-gray-300 mb-2">Message</label>
                    <textarea id="message" className="bg-gray-800 text-white w-full p-2 rounded" rows={4}></textarea>
                  </div>
                  <button type="submit" className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                    Send Message
                  </button>
                </form>
              </div>

              {/* Business Inquiries & Social Media */}
              <div>
                <h2 className="text-2xl font-bold mb-4">Business Inquiries</h2>
                <p className="text-gray-300 mb-4">
                  For partnership opportunities and business-related questions, please contact us at:
                </p>
                <p className="text-purple-400 mb-4"><Mail className="inline-block mr-2 h-5 w-5" /> <EMAIL></p>
                <p className="text-purple-400 mb-8"><Phone className="inline-block mr-2 h-5 w-5"/> +****************</p>

                <h2 className="text-2xl font-bold mb-4">Follow Us</h2>
                <div className="flex space-x-4">
                  <a href="https://www.linkedin.com/" target="_blank" rel="noopener noreferrer" className="text-purple-400 hover:text-purple-300"><Linkedin className="h-6 w-6" /></a>
                  <a href="https://twitter.com/" target="_blank" rel="noopener noreferrer" className="text-purple-400 hover:text-purple-300"><Twitter className="h-6 w-6" /></a>
                  <a href="https://www.facebook.com/" target="_blank" rel="noopener noreferrer" className="text-purple-400 hover:text-purple-300"><Facebook className="h-6 w-6" /></a>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    };

    export default Contact;
