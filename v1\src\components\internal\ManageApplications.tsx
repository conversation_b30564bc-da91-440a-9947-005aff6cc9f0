import React from 'react';
    import { applicationsData } from './mockData/manageApplicationsData';
    import { CheckCircle, Edit, AlertTriangle } from 'lucide-react';

    const ManageApplications: React.FC = () => {
      return (
        <div className="p-6 bg-gray-900 text-white rounded-lg shadow-md">
          <h1 className="text-3xl font-bold mb-6">Manage Applications</h1>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {applicationsData.map((app) => (
              <div key={app.id} className="bg-gray-800 p-4 rounded-lg shadow">
                <h2 className="text-lg font-semibold flex items-center">
                  {app.name}
                  {app.status === 'deployed' && <CheckCircle className="text-green-500 ml-2" size={16} />}
                  {app.status === 'draft' && <Edit className="text-yellow-500 ml-2" size={16} />}
                </h2>
                <p className="text-gray-400 text-sm mb-2">Status: {app.status}</p>
                <p className="text-gray-400 text-sm mb-2">Agents: {app.agents.join(', ')}</p>
                <p className="text-gray-400 text-sm">Last Updated: {app.lastUpdated}</p>
              </div>
            ))}
          </div>
        </div>
      );
    };

    export default ManageApplications;
