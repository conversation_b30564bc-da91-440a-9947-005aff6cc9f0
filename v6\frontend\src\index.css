@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: 'Inter', sans-serif;
}

/* Code highlighting styles */
.hljs {
  display: block;
  overflow-x: auto;
  padding: 0.5em;
  background: #f6f8fa;
  color: #24292e;
}

.dark .hljs {
  background: #1f2937;
  color: #e5e7eb;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-title,
.hljs-section,
.hljs-doctag,
.hljs-name,
.hljs-strong {
  font-weight: bold;
}

.hljs-comment {
  color: #6a737d;
}

.dark .hljs-comment {
  color: #9ca3af;
}

.hljs-string,
.hljs-title,
.hljs-section,
.hljs-built_in,
.hljs-literal,
.hljs-type,
.hljs-addition,
.hljs-tag,
.hljs-quote,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class {
  color: #d73a49;
}

.dark .hljs-string,
.dark .hljs-title,
.dark .hljs-section,
.dark .hljs-built_in,
.dark .hljs-literal,
.dark .hljs-type,
.dark .hljs-addition,
.dark .hljs-tag,
.dark .hljs-quote,
.dark .hljs-name,
.dark .hljs-selector-id,
.dark .hljs-selector-class {
  color: #f87171;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-number,
.hljs-doctag {
  color: #005cc5;
}

.dark .hljs-keyword,
.dark .hljs-selector-tag,
.dark .hljs-number,
.dark .hljs-doctag {
  color: #60a5fa;
}