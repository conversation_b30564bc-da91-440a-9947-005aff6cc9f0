import type { MetaFunction } from "@remix-run/node";
import { Layout } from "~/components/Layout";
import { useState, useEffect } from "react";
import {
  motion,
  useInView,
  useAnimation,
  AnimatePresence,
} from "framer-motion";
import { useRef } from "react";
import {
  FiRefreshCw,
  FiCode,
  FiLayers,
  FiServer,
  FiLayout,
  FiDatabase,
  FiCpu,
  FiShield,
  FiTrendingUp,
  FiCheckCircle,
  FiArrowRight,
  FiGlobe,
  FiZap,
  FiPackage,
  FiMonitor,
  FiCloudLightning,
  FiMessageSquare,
  FiPlay,
  FiStar,
  FiClock,
} from "react-icons/fi";
import { Button } from "~/components/ui/Button";
import { Card, CardContent } from "~/components/ui/Card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/Tabs";
import { Link } from "@remix-run/react";
import { BackToTop } from "~/components/ui/BackToTop";

export const meta: MetaFunction = () => {
  return [
    { title: "SiliconAgent Transform - Legacy System Modernization" },
    {
      name: "description",
      content:
        "SiliconAgent Transform is an enterprise platform for modernizing legacy systems, including Java upgrades and UI modernization.",
    },
  ];
};

// Animation variants
const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6, ease: "easeOut" },
};

const fadeInLeft = {
  initial: { opacity: 0, x: -60 },
  animate: { opacity: 1, x: 0 },
  transition: { duration: 0.6, ease: "easeOut" },
};

const fadeInRight = {
  initial: { opacity: 0, x: 60 },
  animate: { opacity: 1, x: 0 },
  transition: { duration: 0.6, ease: "easeOut" },
};

const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const scaleIn = {
  initial: { opacity: 0, scale: 0.8 },
  animate: { opacity: 1, scale: 1 },
  transition: { duration: 0.5, ease: "easeOut" },
};

// Hook for scroll animations
function useScrollAnimation() {
  const controls = useAnimation();
  const ref = useRef(null);
  const inView = useInView(ref, { once: true, margin: "-100px" });

  useEffect(() => {
    if (inView) {
      controls.start("animate");
    }
  }, [controls, inView]);

  return { ref, controls };
}

// Animated Section Wrapper Component
function AnimatedSection({ children }: { children: React.ReactNode }) {
  const { ref, controls } = useScrollAnimation();

  return (
    <motion.div
      ref={ref}
      initial="initial"
      animate={controls}
      variants={{
        initial: { opacity: 0, y: 60 },
        animate: {
          opacity: 1,
          y: 0,
          transition: { duration: 0.6, ease: "easeOut" },
        },
      }}
    >
      {children}
    </motion.div>
  );
}

// Floating animation component
function FloatingElement({
  children,
  delay = 0,
}: {
  children: React.ReactNode;
  delay?: number;
}) {
  return (
    <motion.div
      animate={{
        y: [0, -10, 0],
      }}
      transition={{
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut",
        delay: delay,
      }}
    >
      {children}
    </motion.div>
  );
}

// Clean particle animation component - no effects at top
function CleanParticleBackground() {
  const particles = Array.from({ length: 8 }, (_, i) => i);

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {particles.map((particle) => (
        <motion.div
          key={particle}
          className="absolute w-1 h-1 bg-indigo-400/10 dark:bg-indigo-300/5 rounded-full"
          animate={{
            opacity: [0, 0.3, 0],
            scale: [0.5, 1, 0.5],
          }}
          transition={{
            duration: Math.random() * 4 + 3,
            repeat: Infinity,
            ease: "easeInOut",
            delay: Math.random() * 2,
          }}
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 70 + 30}%`, // Only in bottom 70% of section
          }}
        />
      ))}
    </div>
  );
}

export default function Transform() {
  const [activeService, setActiveService] = useState("java");
  const [stats, setStats] = useState({ projects: 0, clients: 0, success: 0 });

  // Animate stats on mount
  useEffect(() => {
    const animateStats = () => {
      const duration = 2000;
      const steps = 60;
      const stepTime = duration / steps;

      const targets = { projects: 500, clients: 150, success: 99 };
      let current = { projects: 0, clients: 0, success: 0 };

      const timer = setInterval(() => {
        current.projects = Math.min(
          current.projects + targets.projects / steps,
          targets.projects
        );
        current.clients = Math.min(
          current.clients + targets.clients / steps,
          targets.clients
        );
        current.success = Math.min(
          current.success + targets.success / steps,
          targets.success
        );

        setStats({
          projects: Math.floor(current.projects),
          clients: Math.floor(current.clients),
          success: Math.floor(current.success),
        });

        if (current.projects >= targets.projects) {
          clearInterval(timer);
        }
      }, stepTime);
    };

    const timeout = setTimeout(animateStats, 1000);
    return () => clearTimeout(timeout);
  }, []);

  return (
    <Layout>
      {/* Hero Section - Clean blend with header, no top effects */}
      <motion.div
        className="pt-0 pb-20 relative overflow-hidden"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
      >
        <CleanParticleBackground />

        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="flex flex-col xl:flex-row items-center min-h-[80vh] pt-8 gap-8">
            <motion.div
              className="w-full xl:w-1/2 mb-10 xl:mb-0"
              {...fadeInLeft}
            >
              <motion.span
                className="inline-block bg-indigo-100 dark:bg-indigo-900/50 text-indigo-600 dark:text-indigo-300 font-medium rounded-full px-4 py-2 text-sm mb-6 backdrop-blur-sm border border-indigo-200 dark:border-indigo-800"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.2, duration: 0.5 }}
                whileHover={{ scale: 1.05 }}
              >
                ✨ Legacy System Modernization
              </motion.span>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.6 }}
              >
                {/* Fixed heading size and line-height to prevent text cutoff */}
                <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-5xl xl:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600 dark:from-indigo-400 dark:to-purple-400 mb-8 leading-relaxed break-words pb-2">
                  SiliconAgent Transform
                </h1>
              </motion.div>

              <motion.p
                className="text-lg sm:text-xl text-gray-700 dark:text-gray-300 mb-8 leading-relaxed"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5, duration: 0.6 }}
              >
                Upgrade, migrate, and modernize legacy systems with{" "}
                <span className="text-indigo-600 dark:text-indigo-400 font-semibold">
                  AI precision
                </span>{" "}
                while preserving your valuable business logic.
              </motion.p>

              <motion.div
                className="flex flex-col sm:flex-row gap-4 mb-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7, duration: 0.6 }}
              >
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link to="/demo">
                    <Button
                      size="lg"
                      className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto"
                    >
                      <FiZap className="mr-2" />
                      Try yourself
                      <FiArrowRight className="ml-2" />
                    </Button>
                  </Link>
                </motion.div>

                {/* <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    size="lg"
                    variant="outline"
                    className="border-2 border-indigo-600 text-indigo-600 hover:bg-indigo-600 hover:text-white dark:border-indigo-400 dark:text-indigo-400 dark:hover:bg-indigo-400 dark:hover:text-black transition-all duration-300 w-full sm:w-auto"
                  >
                    <FiPlay className="mr-2" />
                    View Case Studies
                  </Button>
                </motion.div> */}
              </motion.div>

              {/* Stats Section */}
              <motion.div
                className="flex flex-wrap gap-6 sm:gap-8"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.9, duration: 0.6 }}
              >
                <div className="text-center">
                  <motion.div
                    className="text-2xl sm:text-3xl font-bold text-indigo-600 dark:text-indigo-400"
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      repeatDelay: 3,
                    }}
                  >
                    {stats.projects}+
                  </motion.div>
                  <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                    Projects Completed
                  </div>
                </div>
                <div className="text-center">
                  <motion.div
                    className="text-2xl sm:text-3xl font-bold text-indigo-600 dark:text-indigo-400"
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      repeatDelay: 3,
                      delay: 0.2,
                    }}
                  >
                    {stats.clients}+
                  </motion.div>
                  <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                    Happy Clients
                  </div>
                </div>
                <div className="text-center">
                  <motion.div
                    className="text-2xl sm:text-3xl font-bold text-indigo-600 dark:text-indigo-400"
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      repeatDelay: 3,
                      delay: 0.4,
                    }}
                  >
                    {stats.success}%
                  </motion.div>
                  <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                    Success Rate
                  </div>
                </div>
              </motion.div>
            </motion.div>

            <motion.div className="w-full xl:w-1/2" {...fadeInRight}>
              <FloatingElement>
                <motion.div
                  className="relative max-w-2xl mx-auto" // Made wider and bigger
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="bg-white/80 dark:bg-black/80 backdrop-blur-xl shadow-2xl rounded-2xl p-8 sm:p-10 border border-gray-200/50 dark:border-gray-800/50">
                    {" "}
                    {/* Increased padding */}
                    <div className="flex items-center mb-8">
                      {" "}
                      {/* Increased margin */}
                      <motion.div
                        className="rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 p-4 sm:p-5 mr-4"
                        animate={{ rotate: [0, 360] }}
                        transition={{
                          duration: 8,
                          repeat: Infinity,
                          ease: "linear",
                        }}
                      >
                        <FiRefreshCw className="text-white w-6 h-6 sm:w-7 sm:h-7" />{" "}
                        {/* Slightly larger icon */}
                      </motion.div>
                      <div>
                        <h3 className="text-2xl sm:text-3xl font-bold text-gray-800 dark:text-white">
                          {" "}
                          {/* Larger text */}
                          AI-Powered Transformation
                        </h3>
                        <p className="text-base sm:text-lg text-gray-600 dark:text-gray-400">
                          {" "}
                          {/* Larger text */}
                          Modernize with precision and reliability
                        </p>
                      </div>
                    </div>
                    <motion.div
                      className="grid grid-cols-2 gap-4 sm:gap-6"
                      variants={staggerContainer}
                      initial="initial"
                      animate="animate"
                    >
                      {[
                        {
                          icon: FiCpu,
                          text: "AI Analysis",
                          color: "from-blue-500 to-cyan-500",
                        },
                        {
                          icon: FiCode,
                          text: "Code Transform",
                          color: "from-green-500 to-emerald-500",
                        },
                        {
                          icon: FiShield,
                          text: "Auto Testing",
                          color: "from-purple-500 to-pink-500",
                        },
                        {
                          icon: FiTrendingUp,
                          text: "Performance",
                          color: "from-orange-500 to-red-500",
                        },
                      ].map((item, index) => (
                        <motion.div
                          key={item.text}
                          className="bg-gray-50/80 dark:bg-gray-900/80 p-4 sm:p-5 rounded-xl border border-gray-200/50 dark:border-gray-700/50 flex items-center group hover:shadow-lg transition-all duration-300"
                          variants={scaleIn}
                          whileHover={{ scale: 1.05, y: -5 }}
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: 1 + index * 0.1, duration: 0.5 }}
                        >
                          <motion.div
                            className={`bg-gradient-to-r ${item.color} p-2 rounded-lg mr-4`}
                            whileHover={{ rotate: 10 }}
                          >
                            <item.icon className="text-white w-4 h-4 sm:w-5 sm:h-5" />{" "}
                            {/* Slightly larger icon */}
                          </motion.div>
                          <span className="text-sm sm:text-base font-medium group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors">
                            {" "}
                            {/* Larger text */}
                            {item.text}
                          </span>
                        </motion.div>
                      ))}
                    </motion.div>
                  </div>

                  {/* Subtle decorative elements */}
                  <motion.div
                    className="absolute -top-6 -right-6 w-32 h-32 sm:w-40 sm:h-40 bg-gradient-to-r from-indigo-400/10 to-purple-400/10 rounded-full blur-2xl"
                    animate={{
                      scale: [1, 1.2, 1],
                      rotate: [0, 180, 360],
                    }}
                    transition={{
                      duration: 8,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  />
                  <motion.div
                    className="absolute -bottom-6 -left-6 w-24 h-24 sm:w-32 sm:h-32 bg-gradient-to-r from-purple-400/10 to-pink-400/10 rounded-full blur-2xl"
                    animate={{
                      scale: [1.2, 1, 1.2],
                      rotate: [360, 180, 0],
                    }}
                    transition={{
                      duration: 6,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  />
                </motion.div>
              </FloatingElement>
            </motion.div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="product-page py-16 bg-white dark:bg-black overflow-hidden">
        <div className="container mx-auto px-4 sm:px-6">
          {/* Overview Section - Fixed width for Transform Process */}
          <AnimatedSection>
            <section className="overview mb-16">
              <motion.div
                className="flex items-center mb-6"
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <motion.div
                  className="rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 p-3 mr-4"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                >
                  <FiRefreshCw className="text-white w-6 h-6" />
                </motion.div>
                <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                  Overview
                </h2>
              </motion.div>

              <div className="grid grid-cols-1 xl:grid-cols-5 gap-8 mb-8">
                <motion.div
                  className="xl:col-span-3"
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  <p className="text-lg text-gray-700 dark:text-gray-300 mb-6">
                    SiliconAgent Transform is an enterprise platform designed to
                    modernize legacy systems using AI-powered tools and
                    techniques. We help you upgrade your technology stack while
                    preserving your valuable business logic.
                  </p>
                  <p className="text-lg text-gray-700 dark:text-gray-300 mb-6">
                    Our approach combines AI analysis, automated code
                    transformation, and rigorous testing to ensure a smooth
                    transition with minimal risk. Whether you're upgrading Java
                    applications, migrating from Struts to Spring, or
                    transforming legacy UIs into modern web applications, our
                    platform delivers consistent, reliable results.
                  </p>

                  <motion.div
                    className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-8"
                    variants={staggerContainer}
                    initial="initial"
                    whileInView="animate"
                    viewport={{ once: true }}
                  >
                    {[
                      {
                        title: "60% Reduced Cost",
                        desc: "Lower modernization expenses",
                        icon: FiCheckCircle,
                      },
                      {
                        title: "Minimized Risk",
                        desc: "Automated testing and validation",
                        icon: FiCheckCircle,
                      },
                      {
                        title: "Preserved Logic",
                        desc: "Keep business logic while upgrading",
                        icon: FiCheckCircle,
                      },
                      {
                        title: "Zero Downtime",
                        desc: "Continuous operation during updates",
                        icon: FiCheckCircle,
                      },
                    ].map((item, index) => (
                      <motion.div
                        key={item.title}
                        className="flex items-start group"
                        variants={fadeInUp}
                        whileHover={{ scale: 1.02, x: 5 }}
                      >
                        <motion.div
                          className="rounded-full bg-green-100 dark:bg-green-900 p-2 mr-3 mt-1 group-hover:scale-110 transition-transform"
                          whileHover={{ rotate: 15 }}
                        >
                          <item.icon className="text-green-600 dark:text-green-400 w-4 h-4" />
                        </motion.div>
                        <div>
                          <h3 className="font-bold text-gray-900 dark:text-white">
                            {item.title}
                          </h3>
                          <p className="text-gray-600 dark:text-gray-400 text-sm">
                            {item.desc}
                          </p>
                        </div>
                      </motion.div>
                    ))}
                  </motion.div>
                </motion.div>

                {/* Fixed width for Transform Process */}
                <motion.div
                  className="xl:col-span-2 bg-indigo-50 dark:bg-indigo-900/20 p-6 rounded-lg border border-indigo-100 dark:border-indigo-800"
                  initial={{ opacity: 0, scale: 0.95 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  whileHover={{ scale: 1.02 }}
                >
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                    The Transform Process
                  </h3>
                  <div className="space-y-4">
                    {[
                      {
                        step: "1",
                        title: "Analysis",
                        desc: "AI-powered code analysis identifies opportunities",
                      },
                      {
                        step: "2",
                        title: "Planning",
                        desc: "Detailed transformation plan with risk assessment",
                      },
                      {
                        step: "3",
                        title: "Transform",
                        desc: "Automated code transformation with oversight",
                      },
                      {
                        step: "4",
                        title: "Testing",
                        desc: "Comprehensive testing ensures equivalence",
                      },
                      {
                        step: "5",
                        title: "Deploy",
                        desc: "Zero-downtime deployment with rollback",
                      },
                    ].map((item, index) => (
                      <motion.div
                        key={item.step}
                        className="flex"
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        viewport={{ once: true }}
                        transition={{ delay: 0.6 + index * 0.1 }}
                        whileHover={{ x: 5 }}
                      >
                        <motion.div
                          className="flex-shrink-0 w-8 h-8 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center mr-3"
                          whileHover={{ scale: 1.1, rotate: 5 }}
                        >
                          <span className="text-indigo-600 dark:text-indigo-400 font-bold text-sm">
                            {item.step}
                          </span>
                        </motion.div>
                        <div>
                          <h4 className="font-bold text-gray-900 dark:text-white text-sm">
                            {item.title}
                          </h4>
                          <p className="text-xs text-gray-600 dark:text-gray-400">
                            {item.desc}
                          </p>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              </div>
            </section>
          </AnimatedSection>

          {/* Services Section - Removed borders from categories */}
          <AnimatedSection>
            <section className="services mb-16">
              <motion.div
                className="flex items-center mb-6"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
              >
                <motion.div
                  className="rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 p-3 mr-4"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                >
                  <FiServer className="text-white w-6 h-6" />
                </motion.div>
                <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                  Transformation Services
                </h2>
              </motion.div>

              <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
                {/* Service categories - removed borders */}
                <motion.div
                  className="lg:col-span-4 flex flex-col"
                  initial={{ opacity: 0, x: -30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                >
                  <div className="flex-1 bg-gray-50 dark:bg-gray-900/50 rounded-lg p-2">
                    <div className="space-y-2">
                      {[
                        { id: "java", icon: FiCode, label: "Java Upgrades" },
                        {
                          id: "framework",
                          icon: FiPackage,
                          label: "Framework Migration",
                        },
                        { id: "ui", icon: FiLayout, label: "UI Modernization" },
                        {
                          id: "api",
                          icon: FiGlobe,
                          label: "API Transformation",
                        },
                        {
                          id: "stack",
                          icon: FiLayers,
                          label: "Stack Transformation",
                        },
                        { id: "ai", icon: FiCpu, label: "AI Integration" },
                      ].map((service, index) => (
                        <motion.button
                          key={service.id}
                          onClick={() => setActiveService(service.id)}
                          className={`w-full flex items-center p-4 rounded-lg transition-all duration-300 ${
                            activeService === service.id
                              ? "bg-gradient-to-r from-indigo-500 to-purple-500 text-white shadow-lg scale-105"
                              : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                          }`}
                          initial={{ opacity: 0, y: 20 }}
                          whileInView={{ opacity: 1, y: 0 }}
                          viewport={{ once: true }}
                          transition={{ delay: index * 0.1 }}
                          whileHover={{
                            scale: activeService === service.id ? 1.05 : 1.02,
                            x: 3,
                          }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <motion.div
                            className={`p-2 rounded-lg mr-3 ${
                              activeService === service.id
                                ? "bg-white/20"
                                : "bg-indigo-100 dark:bg-indigo-900"
                            }`}
                            whileHover={{ rotate: 5 }}
                            animate={
                              activeService === service.id
                                ? { rotate: [0, 5, 0] }
                                : {}
                            }
                            transition={{ duration: 0.3 }}
                          >
                            <service.icon
                              className={`w-4 h-4 ${
                                activeService === service.id
                                  ? "text-white"
                                  : "text-indigo-600 dark:text-indigo-400"
                              }`}
                            />
                          </motion.div>
                          <span className="font-medium text-sm">
                            {service.label}
                          </span>
                        </motion.button>
                      ))}
                    </div>
                  </div>
                </motion.div>

                {/* Content area - exactly matched height, no horizontal scroll */}
                <motion.div
                  className="lg:col-span-8 flex flex-col"
                  initial={{ opacity: 0, x: 30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  key={activeService}
                >
                  <div className="flex-1 bg-white dark:bg-gray-900 p-6 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                    <AnimatePresence mode="wait">
                      <motion.div
                        key={activeService}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3 }}
                        className="h-full overflow-y-auto"
                      >
                        {/* Enhanced heading with animation */}
                        <motion.div
                          className="flex items-center mb-6 pb-4 border-b border-gray-200 dark:border-gray-700"
                          initial={{ scale: 0.9, opacity: 0 }}
                          animate={{ scale: 1, opacity: 1 }}
                          transition={{ duration: 0.4, delay: 0.1 }}
                        >
                          <motion.div
                            className="rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 p-3 mr-4"
                            initial={{ rotate: -180, scale: 0 }}
                            animate={{ rotate: 0, scale: 1 }}
                            transition={{
                              type: "spring",
                              stiffness: 200,
                              delay: 0.2,
                            }}
                            whileHover={{ rotate: 10, scale: 1.1 }}
                          >
                            {activeService === "java" && (
                              <FiCode className="text-white w-6 h-6" />
                            )}
                            {activeService === "framework" && (
                              <FiPackage className="text-white w-6 h-6" />
                            )}
                            {activeService === "ui" && (
                              <FiLayout className="text-white w-6 h-6" />
                            )}
                            {activeService === "api" && (
                              <FiGlobe className="text-white w-6 h-6" />
                            )}
                            {activeService === "stack" && (
                              <FiLayers className="text-white w-6 h-6" />
                            )}
                            {activeService === "ai" && (
                              <FiCpu className="text-white w-6 h-6" />
                            )}
                          </motion.div>
                          <motion.div
                            initial={{ x: -20, opacity: 0 }}
                            animate={{ x: 0, opacity: 1 }}
                            transition={{ delay: 0.3 }}
                          >
                            <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                              {activeService === "java" && "Java Upgrades"}
                              {activeService === "framework" &&
                                "Framework Migration"}
                              {activeService === "ui" && "UI Modernization"}
                              {activeService === "api" && "API Transformation"}
                              {activeService === "stack" &&
                                "Stack Transformation"}
                              {activeService === "ai" && "AI Integration"}
                            </h3>
                          </motion.div>
                        </motion.div>

                        {/* Service content with proper responsive layout */}
                        <div className="space-y-6">
                          {activeService === "java" && (
                            <motion.div
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ delay: 0.4 }}
                            >
                              <p className="text-gray-600 dark:text-gray-300 mb-6">
                                Seamlessly upgrade your Java applications to the
                                latest versions with automated code analysis,
                                transformation, and testing. Our platform
                                identifies deprecated APIs, language features,
                                and patterns, then transforms them to modern
                                equivalents.
                              </p>

                              <div className="mb-6">
                                <h4 className="font-bold text-gray-900 dark:text-white mb-3">
                                  Upgrade Paths:
                                </h4>
                                <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                                  {[
                                    "Java 6 → Java 8+",
                                    "Java 8 → Java 11+",
                                    "Java 11 → Java 17+",
                                  ].map((path, index) => (
                                    <motion.div
                                      key={path}
                                      className="bg-gray-50 dark:bg-gray-800 p-3 rounded text-center text-sm font-medium"
                                      initial={{ opacity: 0, scale: 0.9 }}
                                      animate={{ opacity: 1, scale: 1 }}
                                      transition={{ delay: 0.5 + index * 0.1 }}
                                      whileHover={{ scale: 1.05 }}
                                    >
                                      {path}
                                    </motion.div>
                                  ))}
                                </div>
                              </div>

                              <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg mb-6">
                                <h4 className="font-bold text-gray-900 dark:text-white mb-3">
                                  Key Features:
                                </h4>
                                <ul className="space-y-2">
                                  {[
                                    "Automated identification of deprecated APIs and language features",
                                    "Intelligent refactoring to use modern Java language features",
                                    "Comprehensive test coverage to ensure functional equivalence",
                                    "Detailed reporting and documentation of all changes",
                                  ].map((feature, index) => (
                                    <motion.li
                                      key={index}
                                      className="flex items-start text-sm"
                                      initial={{ opacity: 0, x: -10 }}
                                      animate={{ opacity: 1, x: 0 }}
                                      transition={{ delay: 0.6 + index * 0.1 }}
                                    >
                                      <FiCheckCircle className="text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                                      <span>{feature}</span>
                                    </motion.li>
                                  ))}
                                </ul>
                              </div>

                              <div className="text-center">
                                <motion.div
                                  whileHover={{ scale: 1.05 }}
                                  whileTap={{ scale: 0.95 }}
                                  initial={{ opacity: 0, y: 20 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ delay: 0.8 }}
                                >
                                  <Button className="bg-indigo-600 hover:bg-indigo-700 text-white">
                                    <Link to="/demo">
                                      Learn More About Java Upgrades
                                    </Link>
                                  </Button>
                                </motion.div>
                              </div>
                            </motion.div>
                          )}

                          {activeService === "framework" && (
                            <motion.div
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ delay: 0.4 }}
                            >
                              <p className="text-gray-600 dark:text-gray-300 mb-6">
                                Migrate from outdated frameworks to modern
                                alternatives while preserving functionality and
                                business logic. Our AI-powered platform analyzes
                                your codebase, maps concepts between frameworks,
                                and generates equivalent code.
                              </p>

                              <div className="mb-6">
                                <h4 className="font-bold text-gray-900 dark:text-white mb-3">
                                  Common Migrations:
                                </h4>
                                <div className="space-y-3">
                                  {[
                                    {
                                      from: "Struts",
                                      to: "Spring",
                                      fromColor: "bg-gray-300 dark:bg-gray-700",
                                      toColor: "bg-green-300 dark:bg-green-700",
                                    },
                                    {
                                      from: "Angular.js",
                                      to: "React",
                                      fromColor: "bg-red-300 dark:bg-red-700",
                                      toColor: "bg-blue-300 dark:bg-blue-700",
                                    },
                                  ].map((migration, index) => (
                                    <motion.div
                                      key={`${migration.from}-${migration.to}`}
                                      className="bg-gray-50 dark:bg-gray-800 p-4 rounded flex items-center justify-between"
                                      initial={{ opacity: 0, x: -20 }}
                                      animate={{ opacity: 1, x: 0 }}
                                      transition={{ delay: 0.5 + index * 0.1 }}
                                    >
                                      <div className="flex items-center">
                                        <div
                                          className={`w-6 h-6 ${migration.fromColor} rounded mr-3`}
                                        ></div>
                                        <span className="text-sm">
                                          {migration.from}
                                        </span>
                                      </div>
                                      <FiArrowRight className="text-indigo-500 mx-4" />
                                      <div className="flex items-center">
                                        <div
                                          className={`w-6 h-6 ${migration.toColor} rounded mr-3`}
                                        ></div>
                                        <span className="text-sm">
                                          {migration.to}
                                        </span>
                                      </div>
                                    </motion.div>
                                  ))}
                                </div>
                              </div>

                              <div className="text-center">
                                <motion.div
                                  whileHover={{ scale: 1.05 }}
                                  whileTap={{ scale: 0.95 }}
                                  initial={{ opacity: 0, y: 20 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ delay: 0.8 }}
                                >
                                  <Button className="bg-indigo-600 hover:bg-indigo-700 text-white">
                                    <Link to="/demo">
                                      Learn More About Framework Migration
                                    </Link>
                                  </Button>
                                </motion.div>
                              </div>
                            </motion.div>
                          )}

                          {activeService === "ui" && (
                            <motion.div
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ delay: 0.4 }}
                            >
                              <p className="text-gray-600 dark:text-gray-300 mb-6">
                                Transform legacy UIs into responsive, modern web
                                applications that work across all devices. Our
                                platform analyzes your existing UI, extracts
                                design patterns and business logic, then
                                rebuilds it using modern frameworks.
                              </p>

                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                                  <h4 className="font-bold text-gray-900 dark:text-white mb-2 flex items-center text-sm">
                                    <FiMonitor className="text-indigo-500 mr-2" />{" "}
                                    Before
                                  </h4>
                                  <div className="border border-gray-300 dark:border-gray-700 rounded bg-white dark:bg-gray-900 p-3 h-24 flex items-center justify-center">
                                    <div className="text-center w-full">
                                      <div className="w-full h-2 bg-gray-300 dark:bg-gray-700 mb-1"></div>
                                      <div className="w-full h-2 bg-gray-300 dark:bg-gray-700 mb-1"></div>
                                      <div className="w-3/4 mx-auto h-2 bg-gray-300 dark:bg-gray-700 mb-1"></div>
                                      <div className="w-1/2 mx-auto h-3 bg-gray-400 dark:bg-gray-600 mt-2"></div>
                                    </div>
                                  </div>
                                  <p className="text-xs text-gray-600 dark:text-gray-400 mt-2">
                                    Static, non-responsive legacy UI
                                  </p>
                                </div>

                                <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                                  <h4 className="font-bold text-gray-900 dark:text-white mb-2 flex items-center text-sm">
                                    <FiMonitor className="text-green-500 mr-2" />{" "}
                                    After
                                  </h4>
                                  <div className="border border-gray-300 dark:border-gray-700 rounded bg-white dark:bg-gray-900 p-3 h-24 flex items-center justify-center">
                                    <div className="text-center w-full">
                                      <div className="w-full h-2 bg-indigo-300 dark:bg-indigo-700 mb-1 rounded-full"></div>
                                      <div className="w-full h-2 bg-indigo-300 dark:bg-indigo-700 mb-1 rounded-full"></div>
                                      <div className="w-3/4 mx-auto h-2 bg-indigo-300 dark:bg-indigo-700 mb-1 rounded-full"></div>
                                      <div className="w-1/2 mx-auto h-3 bg-indigo-500 dark:bg-indigo-600 mt-2 rounded-full"></div>
                                    </div>
                                  </div>
                                  <p className="text-xs text-gray-600 dark:text-gray-400 mt-2">
                                    Modern, responsive UI for all devices
                                  </p>
                                </div>
                              </div>

                              <div className="text-center">
                                <motion.div
                                  whileHover={{ scale: 1.05 }}
                                  whileTap={{ scale: 0.95 }}
                                  initial={{ opacity: 0, y: 20 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ delay: 0.8 }}
                                >
                                  <Button className="bg-indigo-600 hover:bg-indigo-700 text-white">
                                    <Link to="/demo">
                                      Learn More About UI Modernization
                                    </Link>
                                  </Button>
                                </motion.div>
                              </div>
                            </motion.div>
                          )}

                          {activeService === "api" && (
                            <motion.div
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ delay: 0.4 }}
                            >
                              <p className="text-gray-600 dark:text-gray-300 mb-6">
                                Modernize your APIs for better performance,
                                scalability, and developer experience. Our
                                platform can transform legacy SOAP services to
                                REST/GraphQL, upgrade API versions, and
                                implement modern security practices.
                              </p>

                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-6">
                                {[
                                  {
                                    icon: FiZap,
                                    title: "SOAP to REST/GraphQL",
                                    desc: "Transform verbose SOAP services into modern, lightweight APIs",
                                  },
                                  {
                                    icon: FiShield,
                                    title: "Security Modernization",
                                    desc: "Upgrade to OAuth 2.0, JWT, and modern security standards",
                                  },
                                  {
                                    icon: FiTrendingUp,
                                    title: "Performance Optimization",
                                    desc: "Improve API performance with caching and pagination",
                                  },
                                  {
                                    icon: FiCode,
                                    title: "API Versioning",
                                    desc: "Implement proper versioning and backward compatibility",
                                  },
                                ].map((service, index) => (
                                  <motion.div
                                    key={service.title}
                                    className="bg-gray-50 dark:bg-gray-800 p-3 rounded border border-gray-200 dark:border-gray-700"
                                    initial={{ opacity: 0, scale: 0.9 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    transition={{ delay: 0.5 + index * 0.1 }}
                                    whileHover={{ scale: 1.02 }}
                                  >
                                    <h5 className="font-medium text-gray-900 dark:text-white flex items-center mb-2 text-sm">
                                      <service.icon className="text-indigo-500 mr-2 w-4 h-4" />{" "}
                                      {service.title}
                                    </h5>
                                    <p className="text-xs text-gray-600 dark:text-gray-400">
                                      {service.desc}
                                    </p>
                                  </motion.div>
                                ))}
                              </div>

                              <div className="text-center">
                                <motion.div
                                  whileHover={{ scale: 1.05 }}
                                  whileTap={{ scale: 0.95 }}
                                  initial={{ opacity: 0, y: 20 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ delay: 0.8 }}
                                >
                                  <Button className="bg-indigo-600 hover:bg-indigo-700 text-white">
                                    <Link to="/demo">
                                      Learn More About API Transformation
                                    </Link>
                                  </Button>
                                </motion.div>
                              </div>
                            </motion.div>
                          )}

                          {activeService === "stack" && (
                            <motion.div
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ delay: 0.4 }}
                            >
                              <p className="text-gray-600 dark:text-gray-300 mb-6">
                                Transform your entire legacy technology stack to
                                a modern, cloud-native architecture. Our
                                comprehensive approach ensures all components
                                work together harmoniously while preserving
                                business logic.
                              </p>

                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                                <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                                  <h4 className="font-bold text-gray-900 dark:text-white mb-3 flex items-center text-sm">
                                    <FiServer className="text-red-500 mr-2" />{" "}
                                    Legacy Stack
                                  </h4>
                                  <div className="space-y-2">
                                    {[
                                      "Monolithic Architecture",
                                      "On-Premise Infrastructure",
                                      "Heavyweight Frameworks",
                                      "Manual Deployment",
                                    ].map((item, index) => (
                                      <div
                                        key={item}
                                        className="flex items-center p-2 bg-white dark:bg-gray-900 rounded border border-gray-200 dark:border-gray-700"
                                      >
                                        <span className="w-3 h-3 bg-red-200 dark:bg-red-900 rounded-full mr-2"></span>
                                        <span className="text-xs">{item}</span>
                                      </div>
                                    ))}
                                  </div>
                                </div>

                                <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                                  <h4 className="font-bold text-gray-900 dark:text-white mb-3 flex items-center text-sm">
                                    <FiServer className="text-green-500 mr-2" />{" "}
                                    Modern Stack
                                  </h4>
                                  <div className="space-y-2">
                                    {[
                                      "Microservices Architecture",
                                      "Cloud-Native Infrastructure",
                                      "Lightweight Frameworks",
                                      "CI/CD Automation",
                                    ].map((item, index) => (
                                      <div
                                        key={item}
                                        className="flex items-center p-2 bg-white dark:bg-gray-900 rounded border border-gray-200 dark:border-gray-700"
                                      >
                                        <span className="w-3 h-3 bg-green-200 dark:bg-green-900 rounded-full mr-2"></span>
                                        <span className="text-xs">{item}</span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </div>

                              <div className="text-center">
                                <motion.div
                                  whileHover={{ scale: 1.05 }}
                                  whileTap={{ scale: 0.95 }}
                                  initial={{ opacity: 0, y: 20 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ delay: 0.8 }}
                                >
                                  <Button className="bg-indigo-600 hover:bg-indigo-700 text-white">
                                    <Link to="/demo">
                                      Learn More About Stack Transformation
                                    </Link>
                                  </Button>
                                </motion.div>
                              </div>
                            </motion.div>
                          )}

                          {activeService === "ai" && (
                            <motion.div
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ delay: 0.4 }}
                            >
                              <p className="text-gray-600 dark:text-gray-300 mb-6">
                                Introduce AI capabilities into your existing
                                products and codebases without a complete
                                rewrite. Our platform identifies opportunities
                                for AI enhancement and implements them with
                                minimal disruption.
                              </p>

                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-6">
                                {[
                                  {
                                    icon: FiCloudLightning,
                                    title: "Predictive Analytics",
                                    desc: "Add forecasting and trend detection to existing data systems",
                                  },
                                  {
                                    icon: FiMessageSquare,
                                    title: "Conversational Interfaces",
                                    desc: "Integrate chatbots and natural language processing",
                                  },
                                  {
                                    icon: FiZap,
                                    title: "Intelligent Automation",
                                    desc: "Enhance existing workflows with AI-driven decision making",
                                  },
                                  {
                                    icon: FiMonitor,
                                    title: "Computer Vision",
                                    desc: "Add image recognition and processing capabilities",
                                  },
                                ].map((option, index) => (
                                  <motion.div
                                    key={option.title}
                                    className="bg-gray-50 dark:bg-gray-800 p-3 rounded border border-gray-200 dark:border-gray-700"
                                    initial={{ opacity: 0, scale: 0.9 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    transition={{ delay: 0.5 + index * 0.1 }}
                                    whileHover={{ scale: 1.02 }}
                                  >
                                    <h5 className="font-medium text-gray-900 dark:text-white flex items-center mb-2 text-sm">
                                      <option.icon className="text-indigo-500 mr-2 w-4 h-4" />{" "}
                                      {option.title}
                                    </h5>
                                    <p className="text-xs text-gray-600 dark:text-gray-400">
                                      {option.desc}
                                    </p>
                                  </motion.div>
                                ))}
                              </div>

                              <div className="text-center">
                                <motion.div
                                  whileHover={{ scale: 1.05 }}
                                  whileTap={{ scale: 0.95 }}
                                  initial={{ opacity: 0, y: 20 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ delay: 0.8 }}
                                >
                                  <Button className="bg-indigo-600 hover:bg-indigo-700 text-white">
                                    <Link to="/demo">
                                      Learn More About AI Integration
                                    </Link>
                                  </Button>
                                </motion.div>
                              </div>
                            </motion.div>
                          )}
                        </div>
                      </motion.div>
                    </AnimatePresence>
                  </div>
                </motion.div>
              </div>
            </section>
          </AnimatedSection>

          {/* Technologies Section */}
          <AnimatedSection>
            <section className="technologies mb-16">
              <motion.div
                className="flex items-center mb-6"
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <motion.div
                  className="rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 p-3 mr-4"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                >
                  <FiLayers className="text-white w-6 h-6" />
                </motion.div>
                <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                  Technologies
                </h2>
              </motion.div>

              <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
                We leverage the latest technologies to ensure a smooth and
                efficient transformation process, combining AI-powered tools
                with industry-standard platforms.
              </p>

              <Tabs defaultValue="languages" className="w-full">
                <TabsList className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-8">
                  <TabsTrigger
                    value="languages"
                    className="flex items-center gap-2"
                  >
                    <FiCode className="w-5 h-5" />
                    <span className="hidden sm:inline">Languages</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="frameworks"
                    className="flex items-center gap-2"
                  >
                    <FiPackage className="w-5 h-5" />
                    <span className="hidden sm:inline">Frameworks</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="cloud"
                    className="flex items-center gap-2"
                  >
                    <FiCloudLightning className="w-5 h-5" />
                    <span className="hidden sm:inline">Cloud & DevOps</span>
                  </TabsTrigger>
                  <TabsTrigger value="ai" className="flex items-center gap-2">
                    <FiCpu className="w-5 h-5" />
                    <span className="hidden sm:inline">AI & ML</span>
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="languages">
                  <motion.div
                    className="grid grid-cols-2 md:grid-cols-4 gap-4"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                  >
                    {[
                      "Java",
                      "JavaScript",
                      "TypeScript",
                      "Python",
                      "C#",
                      "Go",
                      "Kotlin",
                      "Swift",
                    ].map((lang, index) => (
                      <motion.div
                        key={lang}
                        className="bg-white dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700 flex items-center hover:shadow-lg transition-all duration-300"
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: index * 0.1 }}
                        whileHover={{ scale: 1.05, y: -5 }}
                      >
                        <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center mr-3">
                          <FiCode className="text-indigo-600 dark:text-indigo-400" />
                        </div>
                        <span className="font-medium text-sm">{lang}</span>
                      </motion.div>
                    ))}
                  </motion.div>
                </TabsContent>

                <TabsContent value="frameworks">
                  <motion.div
                    className="grid grid-cols-2 md:grid-cols-4 gap-4"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                  >
                    {[
                      "Spring",
                      "React",
                      "Angular",
                      "Vue",
                      "Django",
                      ".NET Core",
                      "Express",
                      "Rails",
                    ].map((framework, index) => (
                      <motion.div
                        key={framework}
                        className="bg-white dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700 flex items-center hover:shadow-lg transition-all duration-300"
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: index * 0.1 }}
                        whileHover={{ scale: 1.05, y: -5 }}
                      >
                        <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center mr-3">
                          <FiPackage className="text-indigo-600 dark:text-indigo-400" />
                        </div>
                        <span className="font-medium text-sm">{framework}</span>
                      </motion.div>
                    ))}
                  </motion.div>
                </TabsContent>

                <TabsContent value="cloud">
                  <motion.div
                    className="grid grid-cols-2 md:grid-cols-4 gap-4"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                  >
                    {[
                      "AWS",
                      "Azure",
                      "Google Cloud",
                      "Kubernetes",
                      "Docker",
                      "Jenkins",
                      "Terraform",
                      "GitLab CI",
                    ].map((tool, index) => (
                      <motion.div
                        key={tool}
                        className="bg-white dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700 flex items-center hover:shadow-lg transition-all duration-300"
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: index * 0.1 }}
                        whileHover={{ scale: 1.05, y: -5 }}
                      >
                        <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center mr-3">
                          <FiCloudLightning className="text-indigo-600 dark:text-indigo-400" />
                        </div>
                        <span className="font-medium text-sm">{tool}</span>
                      </motion.div>
                    ))}
                  </motion.div>
                </TabsContent>

                <TabsContent value="ai">
                  <motion.div
                    className="grid grid-cols-2 md:grid-cols-4 gap-4"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                  >
                    {[
                      "TensorFlow",
                      "PyTorch",
                      "OpenAI API",
                      "Hugging Face",
                      "Scikit-learn",
                      "NLP",
                      "Computer Vision",
                      "MLOps",
                    ].map((tech, index) => (
                      <motion.div
                        key={tech}
                        className="bg-white dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700 flex items-center hover:shadow-lg transition-all duration-300"
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: index * 0.1 }}
                        whileHover={{ scale: 1.05, y: -5 }}
                      >
                        <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center mr-3">
                          <FiCpu className="text-indigo-600 dark:text-indigo-400" />
                        </div>
                        <span className="font-medium text-sm">{tech}</span>
                      </motion.div>
                    ))}
                  </motion.div>
                </TabsContent>
              </Tabs>
            </section>
          </AnimatedSection>

          {/* Case Studies Section */}
          <AnimatedSection>
            <section className="case-studies mb-16">
              <motion.div
                className="flex items-center mb-6"
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <motion.div
                  className="rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 p-3 mr-4"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                >
                  <FiCheckCircle className="text-white w-6 h-6" />
                </motion.div>
                <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                  Case Studies
                </h2>
              </motion.div>

              <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
                Explore our case studies to see how we've helped enterprises
                modernize their systems, reduce costs, and accelerate
                innovation.
              </p>

              <motion.div
                className="grid grid-cols-1 md:grid-cols-3 gap-6"
                variants={staggerContainer}
                initial="initial"
                whileInView="animate"
                viewport={{ once: true }}
              >
                {[
                  {
                    category: "Java Modernization",
                    title:
                      "Financial Services Firm Upgrades Legacy Java Platform",
                    description:
                      "Learn how we helped a major financial services company upgrade their Java 6 platform to Java 17, reducing operational costs by 60%.",
                    metric: "4x",
                    metricLabel: "performance boost",
                  },
                  {
                    category: "UI Modernization",
                    title: "Healthcare Provider Transforms Patient Portal",
                    description:
                      "See how we transformed a legacy healthcare patient portal into a modern, responsive application that improved patient satisfaction.",
                    metric: "92%",
                    metricLabel: "patient satisfaction",
                  },
                  {
                    category: "Stack Transformation",
                    title: "Retail Giant Moves to Microservices Architecture",
                    description:
                      "Discover how we helped a major retailer transform their monolithic e-commerce platform into a scalable microservices architecture.",
                    metric: "300%",
                    metricLabel: "scalability increase",
                  },
                ].map((study, index) => (
                  <motion.div
                    key={study.title}
                    variants={scaleIn}
                    whileHover={{ scale: 1.03, y: -5 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card className="overflow-hidden hover:shadow-2xl transition-all duration-300 h-full">
                      <div className="h-32 sm:h-40 bg-gradient-to-br from-indigo-100 to-purple-100 dark:from-indigo-900 dark:to-purple-900 relative overflow-hidden">
                        <motion.div
                          className="absolute inset-0 bg-gradient-to-br from-indigo-500/20 to-purple-500/20"
                          animate={{
                            scale: [1, 1.1, 1],
                            rotate: [0, 5, 0],
                          }}
                          transition={{
                            duration: 4,
                            repeat: Infinity,
                            ease: "easeInOut",
                          }}
                        />
                        <div className="absolute inset-0 flex items-center justify-center">
                          <motion.div
                            className="text-4xl sm:text-5xl font-bold text-indigo-600/30 dark:text-indigo-400/30"
                            initial={{ opacity: 0, scale: 0 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: 0.5 + index * 0.2 }}
                          >
                            {study.metric}
                          </motion.div>
                        </div>
                      </div>
                      <CardContent className="p-4 sm:p-6">
                        <div className="inline-block bg-indigo-100 dark:bg-indigo-900/40 text-indigo-600 dark:text-indigo-400 px-2 py-1 text-xs rounded mb-3">
                          {study.category}
                        </div>
                        <h3 className="text-lg font-bold mb-2 text-gray-900 dark:text-white line-clamp-2">
                          {study.title}
                        </h3>
                        <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm line-clamp-3">
                          {study.description}
                        </p>
                        <div className="flex items-center justify-between">
                          <div className="flex items-baseline">
                            <span className="text-xl font-bold text-indigo-600 dark:text-indigo-400">
                              {study.metric}
                            </span>
                            <span className="ml-2 text-xs text-gray-600 dark:text-gray-400">
                              {study.metricLabel}
                            </span>
                          </div>
                          <motion.div
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-indigo-600 border-indigo-600 hover:bg-indigo-600 hover:text-white dark:text-indigo-400 dark:border-indigo-400 dark:hover:bg-indigo-400 dark:hover:text-black text-xs"
                            >
                              Read Study
                            </Button>
                          </motion.div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </motion.div>

              <div className="text-center mt-8">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white">
                    View All Case Studies <FiArrowRight className="ml-2" />
                  </Button>
                </motion.div>
              </div>
            </section>
          </AnimatedSection>

          {/* Call to Action - Fixed button text visibility for all themes */}
          <AnimatedSection>
            <motion.section
              className="bg-gradient-to-r from-indigo-600 to-purple-700 text-white rounded-2xl p-6 sm:p-8 mb-8 relative overflow-hidden"
              whileHover={{ scale: 1.01 }}
              transition={{ duration: 0.3 }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/90 to-purple-700/90" />
              <div className="relative z-10 max-w-3xl mx-auto text-center">
                <motion.h2
                  className="text-2xl sm:text-3xl font-bold mb-4"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                >
                  Ready to modernize your legacy systems?
                </motion.h2>
                <motion.p
                  className="text-lg sm:text-xl mb-8 text-indigo-100"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.2 }}
                >
                  Get started with SiliconAgent Transform today and see how our
                  AI-powered platform can help you upgrade, migrate, and
                  modernize your systems with minimal risk.
                </motion.p>
                <motion.div
                  className="flex flex-col sm:flex-row justify-center gap-4"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.4 }}
                >
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Link to="/demo">
                      <Button
                        size="lg"
                        className="bg-white text-indigo-600 hover:bg-indigo-50 hover:text-indigo-700 dark:bg-white dark:text-indigo-600 dark:hover:bg-indigo-50 dark:hover:text-indigo-700 shadow-lg hover:shadow-xl font-semibold"
                      >
                        <FiZap className="mr-2" />
                        Try yourself
                        <FiArrowRight className="ml-2" />
                      </Button>
                    </Link>
                  </motion.div>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Link to="/contact">
                      <Button
                        size="lg"
                        variant="outline"
                        className="border-2 border-white text-white hover:bg-white hover:text-indigo-600 font-semibold"
                      >
                        Contact Sales
                      </Button>
                    </Link>
                  </motion.div>
                </motion.div>
              </div>
            </motion.section>
          </AnimatedSection>

          {/* FAQ Section */}
          <AnimatedSection>
            <section className="mb-16">
              <motion.div
                className="flex items-center mb-6"
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <motion.div
                  className="rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 p-3 mr-4"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                >
                  <FiMessageSquare className="text-white w-6 h-6" />
                </motion.div>
                <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                  Frequently Asked Questions
                </h2>
              </motion.div>

              <motion.div
                className="space-y-4"
                variants={staggerContainer}
                initial="initial"
                whileInView="animate"
                viewport={{ once: true }}
              >
                {[
                  {
                    question:
                      "How long does a typical modernization project take?",
                    answer:
                      "Project timelines vary based on complexity and scope, but our AI-powered approach significantly reduces traditional timeframes. A typical Java upgrade might take 2-3 months, while a complete stack transformation could take 6-12 months. We work with you to establish realistic milestones and deliver value incrementally.",
                  },
                  {
                    question:
                      "How do you ensure business continuity during transformation?",
                    answer:
                      "We employ a parallel development approach with comprehensive testing at each stage. Our zero-downtime deployment strategy uses techniques like blue-green deployments and feature flags to ensure your systems remain operational throughout the transformation process.",
                  },
                  {
                    question:
                      "How do you preserve business logic during modernization?",
                    answer:
                      "Our AI-powered analysis tools extract and document existing business logic before any transformation begins. We use automated testing to ensure functional equivalence, and our transformation process prioritizes preserving business logic while upgrading the technical implementation.",
                  },
                  {
                    question:
                      "What level of involvement is required from our team?",
                    answer:
                      "While our platform reduces the technical lift, we do require collaboration with your subject matter experts to understand business requirements and validate transformations. We typically work with a small team of your developers, architects, and business analysts to ensure successful outcomes.",
                  },
                ].map((faq, index) => (
                  <motion.div
                    key={faq.question}
                    className="bg-white dark:bg-gray-900 rounded-lg shadow p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300"
                    variants={fadeInUp}
                    whileHover={{ scale: 1.01, y: -2 }}
                  >
                    <h3 className="text-lg font-bold mb-2 text-gray-900 dark:text-white">
                      {faq.question}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      {faq.answer}
                    </p>
                  </motion.div>
                ))}
              </motion.div>
            </section>
          </AnimatedSection>
        </div>
      </div>

      {/* Back to Top Button */}
      <BackToTop />
    </Layout>
  );
}
