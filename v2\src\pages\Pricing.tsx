import { motion } from 'framer-motion';
import { Check } from 'lucide-react';

const plans = [
  {
    name: 'Basic',
    price: 0,
    description: 'Perfect for individuals and small teams',
    features: [
      '5 AI Agents',
      '1,000 API calls/month',
      'Basic analytics',
      'Email support',
      'Community access'
    ]
  },
  {
    name: 'Professional',
    price: 49,
    description: 'Ideal for growing businesses',
    features: [
      '20 AI Agents',
      '10,000 API calls/month',
      'Advanced analytics',
      'Priority support',
      'API access',
      'Custom integrations',
      'Team collaboration'
    ],
    popular: true
  },
  {
    name: 'Enterprise',
    price: 299,
    description: 'For large organizations',
    features: [
      'Unlimited AI Agents',
      'Unlimited API calls',
      'Enterprise analytics',
      '24/7 dedicated support',
      'Custom AI model training',
      'SLA guarantee',
      'Advanced security',
      'Custom deployment'
    ]
  }
];

const Pricing = () => {
  return (
    <main className="py-24">
      <div className="container">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            <span className="gradient-text">Plans</span>
          </h1>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            Choose the perfect plan for your needs. All plans include our core features with different quotas and capabilities.
          </p>
        </motion.div>

        {/* Pricing Grid */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`card card-hover relative ${plan.popular ? 'border-2 border-[var(--primary)]' : ''}`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-[var(--primary)] text-white text-sm font-medium px-4 py-1 rounded-full">
                    Most Popular
                  </span>
                </div>
              )}
              <div className="p-6">
                <h3 className="text-2xl font-bold mb-2">{plan.name}</h3>
                <p className="text-gray-400 mb-6">{plan.description}</p>
                {plan.name !== 'Enterprise' && (
                  <div className="mb-6">
                    <span className="text-4xl font-bold">${plan.price}</span>
                    <span className="text-gray-400">/month</span>
                  </div>
                )}
                {plan.name === 'Enterprise' && (
                  <div className="mb-6">
                    <span className="text-4xl font-bold">Contact Us</span>
                  </div>
                )}
                <ul className="space-y-4 mb-8">
                  {plan.features.map((feature) => (
                    <li key={feature} className="flex items-center gap-3">
                      <Check className="w-5 h-5 text-[var(--primary)]" />
                      <span className="text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>
                <button className={`w-full ${plan.popular ? 'btn-primary' : 'btn-secondary'}`}>
                  Get Started
                </button>
              </div>
            </motion.div>
          ))}
        </div>

        {/* FAQ Section */}
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl font-bold text-center mb-8">Frequently Asked Questions</h2>
          <div className="space-y-6">
            <div className="card">
              <h3 className="text-xl font-semibold mb-2">Can I switch plans later?</h3>
              <p className="text-gray-400">Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.</p>
            </div>
            <div className="card">
              <h3 className="text-xl font-semibold mb-2">What payment methods do you accept?</h3>
              <p className="text-gray-400">We accept all major credit cards, PayPal, and wire transfers for enterprise customers.</p>
            </div>
            <div className="card">
              <h3 className="text-xl font-semibold mb-2">Do you offer a free trial?</h3>
              <p className="text-gray-400">Yes, all plans come with a 14-day free trial. No credit card required.</p>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
};

export default Pricing;