import React from 'react';

    const Features: React.FC = () => {
      return (
        <div className="bg-gray-900 text-white min-h-screen p-8">
          <div className="container mx-auto">
            <h1 className="text-4xl font-bold text-center mb-12">Key Features</h1>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Feature Card 1 */}
              <div className="bg-gray-800 p-6 rounded-lg shadow-lg">
                <h2 className="text-2xl font-bold mb-4">No-Code & Low-Code Agent Creation</h2>
                <p className="text-gray-300">Build AI agents with or without coding using our intuitive interface.</p>
              </div>

              {/* Feature Card 2 */}
              <div className="bg-gray-800 p-6 rounded-lg shadow-lg">
                <h2 className="text-2xl font-bold mb-4">Agent Execution Engine</h2>
                <p className="text-gray-300">Run agents on-demand or as part of automated workflows.</p>
              </div>

              {/* Feature Card 3 */}
              <div className="bg-gray-800 p-6 rounded-lg shadow-lg">
                <h2 className="text-2xl font-bold mb-4">Multi-LLM Support</h2>
                <p className="text-gray-300">Integrate different AI models like OpenAI, Ollama, and Gemini.</p>
              </div>

              {/* Feature Card 4 */}
              <div className="bg-gray-800 p-6 rounded-lg shadow-lg">
                <h2 className="text-2xl font-bold mb-4">API Integration</h2>
                <p className="text-gray-300">Seamlessly connect agents with external services and APIs.</p>
              </div>

              {/* Feature Card 5 */}
              <div className="bg-gray-800 p-6 rounded-lg shadow-lg">
                <h2 className="text-2xl font-bold mb-4">Automated Workflows</h2>
                <p className="text-gray-300">Implement AI-driven process automation for your business needs.</p>
              </div>

              {/* Feature Card 6 */}
              <div className="bg-gray-800 p-6 rounded-lg shadow-lg">
                <h2 className="text-2xl font-bold mb-4">Real-time Monitoring & Analytics</h2>
                <p className="text-gray-300">Track AI agent performance and gain valuable insights.</p>
              </div>

              {/* Feature Card 7 */}
              <div className="bg-gray-800 p-6 rounded-lg shadow-lg">
                <h2 className="text-2xl font-bold mb-4">Security & RBAC</h2>
                <p className="text-gray-300">Ensure secure access and control with role-based access control.</p>
              </div>
            </div>
          </div>
        </div>
      );
    };

    export default Features;
