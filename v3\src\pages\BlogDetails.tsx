import { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";

const BlogDetails = () => {
    const { id } = useParams<{ id: string }>();
    const navigate = useNavigate();
    const [post, setPost] = useState<any>(null);

    useEffect(() => {
        // console.log("Fetching blog post with ID:", id); // Log the ID for debugging
        fetch(`http://localhost:5000/api/posts/${id}`, {
            method: "GET",
            headers: { "Content-Type": "application/json" },
            mode: "cors",
        })
            .then((response) => {
                // console.log("Response status:", response.status); // Log the response status
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then((data) => {
                // console.log("Fetched post data:", data); // Log the fetched data
                setPost(data);
            })
            .catch((error) => console.error("Error fetching blog post:", error));
    }, [id]);

    if (!post) {
        return <div>Loading...</div>;
    }

    return (
        <motion.main
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="py-24"
        >
            <div className="container">
                <button
                    className="btn-secondary mb-8"
                    onClick={() => navigate("/blog")}
                >
                    Back to Blogs
                </button>
                <img
                    src={post.image}
                    alt={post.title}
                    className="w-full h-96 object-cover mb-8"
                />
                <h1 className="text-4xl font-bold mb-4">{post.title}</h1>
                <div className="flex items-center gap-4 text-gray-400 mb-8">
                    <span>{post.author}</span>
                    <span>•</span>
                    <span>{post.readTime}</span>
                </div>
                <p className="text-lg text-justify leading-relaxed text-gray-300">{post.content}</p>
            </div>
        </motion.main>
    );
};

export default BlogDetails;
