import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, X } from 'lucide-react';
import { Link } from 'react-router-dom';

const Navbar: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <nav className="bg-gray-900 text-white p-4 flex justify-between items-center">
      {/* Logo Section */}
      <div className="flex items-center">
        <Sparkles className="mr-2 h-6 w-6 text-purple-500 animate-spin" />
        <Link to="/" className="text-lg font-bold font-sans">
          SiliconAgent.ai
        </Link>
      </div>

      {/* Desktop Menu */}
      <div className="hidden md:flex space-x-4">
        <Link to="/features" className="hover:text-purple-300 font-sans">Features</Link>
        <Link to="/how-it-works" className="hover:text-purple-300 font-sans">How It Works</Link>
        <Link to="/marketplace" className="hover:text-purple-300 font-sans">AI Agents</Link>
        <Link to="/advantages" className="hover:text-purple-300 font-sans">Advantages</Link>
        <Link to="/pricing" className="hover:text-purple-300 font-sans">Pricing</Link>
        <Link to="/about" className="hover:text-purple-300 font-sans">About Us</Link>
        <Link to="/contact" className="hover:text-purple-300 font-sans">Contact</Link>
        <Link to="/blog" className="hover:text-purple-300 font-sans">Blog</Link>
      </div>

      {/* Mobile Menu Toggle Button */}
      <div className="md:hidden">
        {isOpen ? (
          <X className="h-6 w-6 cursor-pointer" onClick={() => setIsOpen(false)} />
        ) : (
          <Menu className="h-6 w-6 cursor-pointer" onClick={() => setIsOpen(true)} />
        )}
      </div>

      {/* Mobile Menu (Dropdown) */}
      {isOpen && (
        <div className="absolute top-16 left-0 w-full bg-gray-900 text-white flex flex-col space-y-2 p-4 md:hidden">
          <Link to="/features" className="hover:text-purple-300 font-sans" onClick={() => setIsOpen(false)}>Features</Link>
          <Link to="/how-it-works" className="hover:text-purple-300 font-sans" onClick={() => setIsOpen(false)}>How It Works</Link>
          <Link to="/marketplace" className="hover:text-purple-300 font-sans" onClick={() => setIsOpen(false)}>AI Agents</Link>
          <Link to="/advantages" className="hover:text-purple-300 font-sans" onClick={() => setIsOpen(false)}>Advantages</Link>
          <Link to="/pricing" className="hover:text-purple-300 font-sans" onClick={() => setIsOpen(false)}>Pricing</Link>
          <Link to="/about" className="hover:text-purple-300 font-sans" onClick={() => setIsOpen(false)}>About Us</Link>
          <Link to="/contact" className="hover:text-purple-300 font-sans" onClick={() => setIsOpen(false)}>Contact</Link>
          <Link to="/blog" className="hover:text-purple-300 font-sans" onClick={() => setIsOpen(false)}>Blog</Link>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
