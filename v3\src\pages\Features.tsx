import { motion } from 'framer-motion';

const Features = () => {
    const features = [
        {
            title: 'No-Code & Low-Code Agent Creation',
            description: 'Build AI agents with or without coding using our intuitive interface.',
        },
        {
            title: 'Agent Execution Engine',
            description: 'Run agents on-demand or as part of automated workflows.',
        },
        {
            title: 'Multi-LLM Support',
            description: 'Integrate different AI models like OpenAI, Ollama, and Gemini.',
        },
        {
            title: 'API Integration',
            description: 'Seamlessly connect agents with external services and APIs.',
        },
        {
            title: 'Automated Workflows',
            description: 'Implement AI-driven process automation for your business needs.',
        },
        {
            title: 'Real-time Monitoring & Analytics',
            description: 'Track AI agent performance and gain valuable insights.',
        },
        {
            title: 'Security & RBAC',
            description: 'Ensure secure access and control with role-based access control.',
        },
    ];

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.5 }}
            className="container mx-auto p-4 py-24"
        >
            <h1 className="text-4xl md:text-6xl font-bold text-center mb-12">Key Features</h1>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {features.map((feature, index) => (
                    <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                        className="card text-center p-6 rounded-lg shadow-lg"
                    >
                        <h2 className="text-xl font-semibold mb-4">{feature.title}</h2>
                        <p className="text-gray-300">{feature.description}</p>
                    </motion.div>
                ))}
            </div>
        </motion.div>
    );
};

export default Features;
