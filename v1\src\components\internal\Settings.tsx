import React from 'react';
    import { settingsData } from './mockData/settingsData';
    import { Settings as SettingsIcon, User, Mail, ToggleLeft, ToggleRight } from 'lucide-react';

    const Settings: React.FC = () => {
      return (
        <div className="p-6 bg-gray-900 text-white rounded-lg shadow-md">
          <h1 className="text-3xl font-bold mb-6 flex items-center"><SettingsIcon className="mr-2" /> Settings &amp; Configuration</h1>

          <h2 className="text-xl font-semibold mb-4">User Profile</h2>
          <div className="bg-gray-800 p-4 rounded-lg shadow mb-8">
            <div className="flex items-center mb-4">
              <User className="mr-2" />
              <p className="text-lg font-semibold">{settingsData.userProfile.name}</p>
            </div>
            <div className="flex items-center mb-4">
              <Mail className="mr-2" />
              <p className="text-gray-400">{settingsData.userProfile.email}</p>
            </div>
            <p className="text-gray-400">Plan: {settingsData.userProfile.plan}</p>
          </div>

          <h2 className="text-xl font-semibold mb-4">Preferences</h2>
          <div className="bg-gray-800 p-4 rounded-lg shadow">
            <div className="flex items-center mb-4">
              <span className="mr-2">Theme:</span>
              {settingsData.preferences.theme === 'dark' ? <ToggleLeft className="text-gray-400" /> : <ToggleRight className="text-purple-500" />}
              <span className="ml-2">{settingsData.preferences.theme}</span>
            </div>
            <div className="flex items-center">
              <span className="mr-2">Notifications:</span>
              {settingsData.preferences.notifications === 'enabled' ? <ToggleRight className="text-purple-500" /> : <ToggleLeft className="text-gray-400" />}
              <span className="ml-2">{settingsData.preferences.notifications}</span>
            </div>
          </div>
        </div>
      );
    };

    export default Settings;
