import type { MetaFunction } from "@remix-run/node";
import { Layout } from "~/components/Layout";
import { motion } from "framer-motion";
import {
  FiMail,
  FiPhone,
  FiLinkedin,
  FiTwitter,
  FiGithub,
  FiGlobe,
  FiCopy,
  FiExternalLink,
} from "react-icons/fi";
import { useState } from "react";

export const meta: MetaFunction = () => {
  return [
    { title: "Contact Us - SiliconAgent.ai" },
    {
      name: "description",
      content: "Contact SiliconAgent.ai for sales inquiries or support.",
    },
  ];
};

// Animation variants
const fadeInUp = {
  initial: { opacity: 0, y: 30 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6, ease: "easeOut" },
};

const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1,
    },
  },
};

// Contact item component
function ContactItem({
  icon: Icon,
  label,
  value,
  href,
  delay = 0,
  color = "indigo",
}: {
  icon: any;
  label: string;
  value: string;
  href: string;
  delay?: number;
  color?: string;
}) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  return (
    <motion.div
      className="flex items-center space-x-4 p-4 bg-white dark:bg-gray-900 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-lg dark:hover:shadow-indigo-500/10 theme-transition group"
      initial={{ opacity: 0, x: -20 }}
      whileInView={{ opacity: 1, x: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay }}
      whileHover={{ scale: 1.02 }}
    >
      {/* Icon */}
      <motion.div
        className={`w-12 h-12 bg-gradient-to-r from-${color}-500 to-${color}-600 rounded-lg flex items-center justify-center flex-shrink-0`}
        whileHover={{ scale: 1.1, rotate: 5 }}
      >
        <Icon className="w-6 h-6 text-white" />
      </motion.div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-500 dark:text-gray-400 theme-transition">
          {label}
        </p>
        <motion.a
          href={href}
          className="text-lg font-semibold text-gray-900 dark:text-white hover:text-indigo-600 dark:hover:text-indigo-400 theme-transition block truncate"
          whileHover={{ x: 2 }}
        >
          {value}
        </motion.a>
      </div>

      {/* Actions */}
      <div className="flex space-x-2 opacity-0 group-hover:opacity-100 theme-transition">
        <motion.button
          onClick={() => handleCopy(value)}
          className="p-2 bg-gray-100 dark:bg-gray-800 hover:bg-indigo-100 dark:hover:bg-indigo-900/50 rounded-lg theme-transition"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          title="Copy"
        >
          <FiCopy className="w-4 h-4 text-gray-600 dark:text-gray-400" />
        </motion.button>

        <motion.a
          href={href}
          className="p-2 bg-indigo-600 hover:bg-indigo-700 rounded-lg theme-transition"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          title="Open"
        >
          <FiExternalLink className="w-4 h-4 text-white" />
        </motion.a>
      </div>

      {/* Copy feedback */}
      {copied && (
        <motion.div
          className="absolute top-0 right-0 bg-green-500 text-white text-xs px-2 py-1 rounded"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
        >
          Copied!
        </motion.div>
      )}
    </motion.div>
  );
}

// Social media button component
function SocialButton({
  icon: Icon,
  href,
  label,
  color,
  delay = 0,
}: {
  icon: any;
  href: string;
  label: string;
  color: string;
  delay?: number;
}) {
  return (
    <motion.a
      href={href}
      target="_blank"
      rel="noopener noreferrer"
      className={`w-16 h-16 rounded-xl ${color} flex items-center justify-center text-white shadow-lg hover:shadow-xl theme-transition group`}
      initial={{ opacity: 0, scale: 0, rotate: -90 }}
      whileInView={{ opacity: 1, scale: 1, rotate: 0 }}
      viewport={{ once: true }}
      transition={{
        duration: 0.6,
        delay,
        type: "spring",
        stiffness: 200,
      }}
      whileHover={{ scale: 1.15, rotate: 8, y: -5 }}
      whileTap={{ scale: 0.95 }}
      aria-label={label}
    >
      <motion.div
        whileHover={{ rotate: [0, -5, 5, 0] }}
        transition={{ duration: 0.5 }}
      >
        <Icon className="w-7 h-7" />
      </motion.div>
    </motion.a>
  );
}

export default function Contact() {
  const contactMethods = [
    {
      icon: FiMail,
      label: "Sales",
      value: "<EMAIL>",
      href: "mailto:<EMAIL>",
      color: "blue",
    },
    {
      icon: FiMail,
      label: "Support",
      value: "<EMAIL>",
      href: "mailto:<EMAIL>",
      color: "green",
    },
    {
      icon: FiMail,
      label: "Info",
      value: "<EMAIL>",
      href: "mailto:<EMAIL>",
      color: "purple",
    },
    {
      icon: FiPhone,
      label: "Phone",
      value: "+91 6301704900",
      href: "tel:+916301704900",
      color: "orange",
    },
  ];

  const socialLinks = [
    {
      icon: FiLinkedin,
      href: "https://www.linkedin.com/company/siliconagent-ai/",
      label: "LinkedIn",
      color: "bg-blue-600 hover:bg-blue-700",
    },
    {
      icon: FiTwitter,
      href: "",
      label: "Twitter",
      color: "bg-sky-500 hover:bg-sky-600",
    },
    {
      icon: FiGithub,
      href: "",
      label: "GitHub",
      color:
        "bg-gray-800 hover:bg-gray-900 dark:bg-gray-700 dark:hover:bg-gray-600",
    },
    {
      icon: FiGlobe,
      href: "https://siliconagent.ai",
      label: "Website",
      color:
        "bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700",
    },
  ];

  return (
    <Layout>
      {/* Hero Section - Fit to viewport */}
      <motion.div
        className="min-h-screen absolute flex items-center justify-center relative overflow-hidden theme-transition"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
      >
        {/* Background effects */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {Array.from({ length: 5 }, (_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-indigo-400/20 dark:bg-indigo-300/10 rounded-full"
              animate={{
                opacity: [0, 0.8, 0],
                scale: [0.5, 1.5, 0.5],
              }}
              transition={{
                duration: Math.random() * 3 + 2,
                repeat: Infinity,
                ease: "easeInOut",
                delay: Math.random() * 2,
              }}
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
            />
          ))}
        </div>

        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="max-w-6xl mx-auto">
            {/* Header */}
            <motion.div
              className="text-center mb-16"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h1 className="text-4xl sm:text-4xl lg:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600 dark:from-indigo-400 dark:to-purple-400 mb-6 theme-transition">
                Contact Us
              </h1>
              <p className="text-xl sm:text-2xl text-gray-700 dark:text-gray-300 theme-transition">
                Get in touch with our team
              </p>
            </motion.div>

            {/* Two Column Layout */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start">
              {/* Left - Contact Methods */}
              <motion.div
                className="space-y-6"
                variants={staggerContainer}
                initial="initial"
                animate="animate"
              >
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-8 theme-transition">
                  Reach Out
                </h2>

                <div className="space-y-4">
                  {contactMethods.map((method, index) => (
                    <ContactItem
                      key={`${method.label}-${index}`}
                      icon={method.icon}
                      label={method.label}
                      value={method.value}
                      href={method.href}
                      delay={index * 0.1}
                      color={method.color}
                    />
                  ))}
                </div>
              </motion.div>

              {/* Right - Social Media */}
              <motion.div
                className="space-y-8"
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
              >
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-8 theme-transition">
                  Follow Us
                </h2>

                {/* Social Media in Single Row - Centered */}
                <div className="flex justify-center space-x-6">
                  {socialLinks.map((social, index) => (
                    <SocialButton
                      key={social.label}
                      icon={social.icon}
                      href={social.href}
                      label={social.label}
                      color={social.color}
                      delay={0.6 + index * 0.1}
                    />
                  ))}
                </div>

                {/* Quick Response Note */}
                <motion.div
                  className="mt-12 p-6 bg-white dark:bg-gray-900 rounded-xl border border-gray-200 dark:border-gray-700 theme-transition"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1 }}
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="text-center">
                    <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                      <FiMail className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2 theme-transition">
                      Quick Response
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 text-sm theme-transition">
                      We respond within 24 hours
                    </p>
                  </div>
                </motion.div>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.div>
    </Layout>
  );
}
