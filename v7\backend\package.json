{"name": "backend", "version": "1.0.0", "description": "Express.js backend with TypeScript", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "nodemon src/server.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["express", "typescript", "api"], "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.0.1", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}