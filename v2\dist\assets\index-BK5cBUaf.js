function yg(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in e)){const s=Object.getOwnPropertyDescriptor(r,i);s&&Object.defineProperty(e,i,s.get?s:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const s of i)if(s.type==="childList")for(const o of s.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(i){const s={};return i.integrity&&(s.integrity=i.integrity),i.referrerPolicy&&(s.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?s.credentials="include":i.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(i){if(i.ep)return;i.ep=!0;const s=n(i);fetch(i.href,s)}})();function vg(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var mf={exports:{}},Vs={},gf={exports:{}},_={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var si=Symbol.for("react.element"),xg=Symbol.for("react.portal"),wg=Symbol.for("react.fragment"),Sg=Symbol.for("react.strict_mode"),kg=Symbol.for("react.profiler"),Cg=Symbol.for("react.provider"),Pg=Symbol.for("react.context"),Eg=Symbol.for("react.forward_ref"),Tg=Symbol.for("react.suspense"),jg=Symbol.for("react.memo"),Ng=Symbol.for("react.lazy"),Vu=Symbol.iterator;function Ag(e){return e===null||typeof e!="object"?null:(e=Vu&&e[Vu]||e["@@iterator"],typeof e=="function"?e:null)}var yf={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},vf=Object.assign,xf={};function Jn(e,t,n){this.props=e,this.context=t,this.refs=xf,this.updater=n||yf}Jn.prototype.isReactComponent={};Jn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Jn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function wf(){}wf.prototype=Jn.prototype;function rl(e,t,n){this.props=e,this.context=t,this.refs=xf,this.updater=n||yf}var il=rl.prototype=new wf;il.constructor=rl;vf(il,Jn.prototype);il.isPureReactComponent=!0;var _u=Array.isArray,Sf=Object.prototype.hasOwnProperty,sl={current:null},kf={key:!0,ref:!0,__self:!0,__source:!0};function Cf(e,t,n){var r,i={},s=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(s=""+t.key),t)Sf.call(t,r)&&!kf.hasOwnProperty(r)&&(i[r]=t[r]);var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];i.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)i[r]===void 0&&(i[r]=a[r]);return{$$typeof:si,type:e,key:s,ref:o,props:i,_owner:sl.current}}function Mg(e,t){return{$$typeof:si,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ol(e){return typeof e=="object"&&e!==null&&e.$$typeof===si}function Rg(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Iu=/\/+/g;function io(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Rg(""+e.key):t.toString(36)}function Bi(e,t,n,r,i){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(s){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case si:case xg:o=!0}}if(o)return o=e,i=i(o),e=r===""?"."+io(o,0):r,_u(i)?(n="",e!=null&&(n=e.replace(Iu,"$&/")+"/"),Bi(i,t,n,"",function(u){return u})):i!=null&&(ol(i)&&(i=Mg(i,n+(!i.key||o&&o.key===i.key?"":(""+i.key).replace(Iu,"$&/")+"/")+e)),t.push(i)),1;if(o=0,r=r===""?".":r+":",_u(e))for(var a=0;a<e.length;a++){s=e[a];var l=r+io(s,a);o+=Bi(s,t,n,l,i)}else if(l=Ag(e),typeof l=="function")for(e=l.call(e),a=0;!(s=e.next()).done;)s=s.value,l=r+io(s,a++),o+=Bi(s,t,n,l,i);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function xi(e,t,n){if(e==null)return e;var r=[],i=0;return Bi(e,r,"","",function(s){return t.call(n,s,i++)}),r}function Lg(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ke={current:null},Ui={transition:null},Dg={ReactCurrentDispatcher:ke,ReactCurrentBatchConfig:Ui,ReactCurrentOwner:sl};function Pf(){throw Error("act(...) is not supported in production builds of React.")}_.Children={map:xi,forEach:function(e,t,n){xi(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return xi(e,function(){t++}),t},toArray:function(e){return xi(e,function(t){return t})||[]},only:function(e){if(!ol(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};_.Component=Jn;_.Fragment=wg;_.Profiler=kg;_.PureComponent=rl;_.StrictMode=Sg;_.Suspense=Tg;_.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Dg;_.act=Pf;_.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=vf({},e.props),i=e.key,s=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,o=sl.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)Sf.call(t,l)&&!kf.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:si,type:e.type,key:i,ref:s,props:r,_owner:o}};_.createContext=function(e){return e={$$typeof:Pg,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Cg,_context:e},e.Consumer=e};_.createElement=Cf;_.createFactory=function(e){var t=Cf.bind(null,e);return t.type=e,t};_.createRef=function(){return{current:null}};_.forwardRef=function(e){return{$$typeof:Eg,render:e}};_.isValidElement=ol;_.lazy=function(e){return{$$typeof:Ng,_payload:{_status:-1,_result:e},_init:Lg}};_.memo=function(e,t){return{$$typeof:jg,type:e,compare:t===void 0?null:t}};_.startTransition=function(e){var t=Ui.transition;Ui.transition={};try{e()}finally{Ui.transition=t}};_.unstable_act=Pf;_.useCallback=function(e,t){return ke.current.useCallback(e,t)};_.useContext=function(e){return ke.current.useContext(e)};_.useDebugValue=function(){};_.useDeferredValue=function(e){return ke.current.useDeferredValue(e)};_.useEffect=function(e,t){return ke.current.useEffect(e,t)};_.useId=function(){return ke.current.useId()};_.useImperativeHandle=function(e,t,n){return ke.current.useImperativeHandle(e,t,n)};_.useInsertionEffect=function(e,t){return ke.current.useInsertionEffect(e,t)};_.useLayoutEffect=function(e,t){return ke.current.useLayoutEffect(e,t)};_.useMemo=function(e,t){return ke.current.useMemo(e,t)};_.useReducer=function(e,t,n){return ke.current.useReducer(e,t,n)};_.useRef=function(e){return ke.current.useRef(e)};_.useState=function(e){return ke.current.useState(e)};_.useSyncExternalStore=function(e,t,n){return ke.current.useSyncExternalStore(e,t,n)};_.useTransition=function(){return ke.current.useTransition()};_.version="18.3.1";gf.exports=_;var S=gf.exports;const Vg=vg(S),_g=yg({__proto__:null,default:Vg},[S]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ig=S,Og=Symbol.for("react.element"),Fg=Symbol.for("react.fragment"),zg=Object.prototype.hasOwnProperty,Bg=Ig.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Ug={key:!0,ref:!0,__self:!0,__source:!0};function Ef(e,t,n){var r,i={},s=null,o=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)zg.call(t,r)&&!Ug.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Og,type:e,key:s,ref:o,props:i,_owner:Bg.current}}Vs.Fragment=Fg;Vs.jsx=Ef;Vs.jsxs=Ef;mf.exports=Vs;var f=mf.exports,Tf={exports:{}},_e={},jf={exports:{}},Nf={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(N,L){var D=N.length;N.push(L);e:for(;0<D;){var Z=D-1>>>1,ae=N[Z];if(0<i(ae,L))N[Z]=L,N[D]=ae,D=Z;else break e}}function n(N){return N.length===0?null:N[0]}function r(N){if(N.length===0)return null;var L=N[0],D=N.pop();if(D!==L){N[0]=D;e:for(var Z=0,ae=N.length,yi=ae>>>1;Z<yi;){var Qt=2*(Z+1)-1,ro=N[Qt],Yt=Qt+1,vi=N[Yt];if(0>i(ro,D))Yt<ae&&0>i(vi,ro)?(N[Z]=vi,N[Yt]=D,Z=Yt):(N[Z]=ro,N[Qt]=D,Z=Qt);else if(Yt<ae&&0>i(vi,D))N[Z]=vi,N[Yt]=D,Z=Yt;else break e}}return L}function i(N,L){var D=N.sortIndex-L.sortIndex;return D!==0?D:N.id-L.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var o=Date,a=o.now();e.unstable_now=function(){return o.now()-a}}var l=[],u=[],c=1,d=null,h=3,y=!1,v=!1,x=!1,C=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,m=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(N){for(var L=n(u);L!==null;){if(L.callback===null)r(u);else if(L.startTime<=N)r(u),L.sortIndex=L.expirationTime,t(l,L);else break;L=n(u)}}function w(N){if(x=!1,g(N),!v)if(n(l)!==null)v=!0,gi(k);else{var L=n(u);L!==null&&re(w,L.startTime-N)}}function k(N,L){v=!1,x&&(x=!1,p(P),P=-1),y=!0;var D=h;try{for(g(L),d=n(l);d!==null&&(!(d.expirationTime>L)||N&&!oe());){var Z=d.callback;if(typeof Z=="function"){d.callback=null,h=d.priorityLevel;var ae=Z(d.expirationTime<=L);L=e.unstable_now(),typeof ae=="function"?d.callback=ae:d===n(l)&&r(l),g(L)}else r(l);d=n(l)}if(d!==null)var yi=!0;else{var Qt=n(u);Qt!==null&&re(w,Qt.startTime-L),yi=!1}return yi}finally{d=null,h=D,y=!1}}var T=!1,j=null,P=-1,V=5,R=-1;function oe(){return!(e.unstable_now()-R<V)}function wt(){if(j!==null){var N=e.unstable_now();R=N;var L=!0;try{L=j(!0,N)}finally{L?Gt():(T=!1,j=null)}}else T=!1}var Gt;if(typeof m=="function")Gt=function(){m(wt)};else if(typeof MessageChannel<"u"){var sr=new MessageChannel,Du=sr.port2;sr.port1.onmessage=wt,Gt=function(){Du.postMessage(null)}}else Gt=function(){C(wt,0)};function gi(N){j=N,T||(T=!0,Gt())}function re(N,L){P=C(function(){N(e.unstable_now())},L)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(N){N.callback=null},e.unstable_continueExecution=function(){v||y||(v=!0,gi(k))},e.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):V=0<N?Math.floor(1e3/N):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(N){switch(h){case 1:case 2:case 3:var L=3;break;default:L=h}var D=h;h=L;try{return N()}finally{h=D}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(N,L){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var D=h;h=N;try{return L()}finally{h=D}},e.unstable_scheduleCallback=function(N,L,D){var Z=e.unstable_now();switch(typeof D=="object"&&D!==null?(D=D.delay,D=typeof D=="number"&&0<D?Z+D:Z):D=Z,N){case 1:var ae=-1;break;case 2:ae=250;break;case 5:ae=**********;break;case 4:ae=1e4;break;default:ae=5e3}return ae=D+ae,N={id:c++,callback:L,priorityLevel:N,startTime:D,expirationTime:ae,sortIndex:-1},D>Z?(N.sortIndex=D,t(u,N),n(l)===null&&N===n(u)&&(x?(p(P),P=-1):x=!0,re(w,D-Z))):(N.sortIndex=ae,t(l,N),v||y||(v=!0,gi(k))),N},e.unstable_shouldYield=oe,e.unstable_wrapCallback=function(N){var L=h;return function(){var D=h;h=L;try{return N.apply(this,arguments)}finally{h=D}}}})(Nf);jf.exports=Nf;var $g=jf.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Wg=S,De=$g;function E(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Af=new Set,_r={};function pn(e,t){Wn(e,t),Wn(e+"Capture",t)}function Wn(e,t){for(_r[e]=t,e=0;e<t.length;e++)Af.add(t[e])}var pt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ho=Object.prototype.hasOwnProperty,bg=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Ou={},Fu={};function Hg(e){return Ho.call(Fu,e)?!0:Ho.call(Ou,e)?!1:bg.test(e)?Fu[e]=!0:(Ou[e]=!0,!1)}function Kg(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Gg(e,t,n,r){if(t===null||typeof t>"u"||Kg(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ce(e,t,n,r,i,s,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=o}var he={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){he[e]=new Ce(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];he[t]=new Ce(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){he[e]=new Ce(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){he[e]=new Ce(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){he[e]=new Ce(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){he[e]=new Ce(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){he[e]=new Ce(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){he[e]=new Ce(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){he[e]=new Ce(e,5,!1,e.toLowerCase(),null,!1,!1)});var al=/[\-:]([a-z])/g;function ll(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(al,ll);he[t]=new Ce(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(al,ll);he[t]=new Ce(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(al,ll);he[t]=new Ce(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){he[e]=new Ce(e,1,!1,e.toLowerCase(),null,!1,!1)});he.xlinkHref=new Ce("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){he[e]=new Ce(e,1,!1,e.toLowerCase(),null,!0,!0)});function ul(e,t,n,r){var i=he.hasOwnProperty(t)?he[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Gg(t,n,i,r)&&(n=null),r||i===null?Hg(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var xt=Wg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,wi=Symbol.for("react.element"),wn=Symbol.for("react.portal"),Sn=Symbol.for("react.fragment"),cl=Symbol.for("react.strict_mode"),Ko=Symbol.for("react.profiler"),Mf=Symbol.for("react.provider"),Rf=Symbol.for("react.context"),dl=Symbol.for("react.forward_ref"),Go=Symbol.for("react.suspense"),Qo=Symbol.for("react.suspense_list"),fl=Symbol.for("react.memo"),Ct=Symbol.for("react.lazy"),Lf=Symbol.for("react.offscreen"),zu=Symbol.iterator;function or(e){return e===null||typeof e!="object"?null:(e=zu&&e[zu]||e["@@iterator"],typeof e=="function"?e:null)}var Q=Object.assign,so;function mr(e){if(so===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);so=t&&t[1]||""}return`
`+so+e}var oo=!1;function ao(e,t){if(!e||oo)return"";oo=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),s=r.stack.split(`
`),o=i.length-1,a=s.length-1;1<=o&&0<=a&&i[o]!==s[a];)a--;for(;1<=o&&0<=a;o--,a--)if(i[o]!==s[a]){if(o!==1||a!==1)do if(o--,a--,0>a||i[o]!==s[a]){var l=`
`+i[o].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=o&&0<=a);break}}}finally{oo=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?mr(e):""}function Qg(e){switch(e.tag){case 5:return mr(e.type);case 16:return mr("Lazy");case 13:return mr("Suspense");case 19:return mr("SuspenseList");case 0:case 2:case 15:return e=ao(e.type,!1),e;case 11:return e=ao(e.type.render,!1),e;case 1:return e=ao(e.type,!0),e;default:return""}}function Yo(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Sn:return"Fragment";case wn:return"Portal";case Ko:return"Profiler";case cl:return"StrictMode";case Go:return"Suspense";case Qo:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Rf:return(e.displayName||"Context")+".Consumer";case Mf:return(e._context.displayName||"Context")+".Provider";case dl:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case fl:return t=e.displayName||null,t!==null?t:Yo(e.type)||"Memo";case Ct:t=e._payload,e=e._init;try{return Yo(e(t))}catch{}}return null}function Yg(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Yo(t);case 8:return t===cl?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function zt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Df(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Xg(e){var t=Df(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(o){r=""+o,s.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Si(e){e._valueTracker||(e._valueTracker=Xg(e))}function Vf(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Df(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function ts(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Xo(e,t){var n=t.checked;return Q({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Bu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=zt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function _f(e,t){t=t.checked,t!=null&&ul(e,"checked",t,!1)}function Zo(e,t){_f(e,t);var n=zt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?qo(e,t.type,n):t.hasOwnProperty("defaultValue")&&qo(e,t.type,zt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Uu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function qo(e,t,n){(t!=="number"||ts(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var gr=Array.isArray;function On(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+zt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Jo(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(E(91));return Q({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function $u(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(E(92));if(gr(n)){if(1<n.length)throw Error(E(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:zt(n)}}function If(e,t){var n=zt(t.value),r=zt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Wu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Of(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ea(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Of(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var ki,Ff=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(ki=ki||document.createElement("div"),ki.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ki.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Ir(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var kr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Zg=["Webkit","ms","Moz","O"];Object.keys(kr).forEach(function(e){Zg.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),kr[t]=kr[e]})});function zf(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||kr.hasOwnProperty(e)&&kr[e]?(""+t).trim():t+"px"}function Bf(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=zf(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var qg=Q({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ta(e,t){if(t){if(qg[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(E(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(E(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(E(61))}if(t.style!=null&&typeof t.style!="object")throw Error(E(62))}}function na(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ra=null;function hl(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ia=null,Fn=null,zn=null;function bu(e){if(e=li(e)){if(typeof ia!="function")throw Error(E(280));var t=e.stateNode;t&&(t=zs(t),ia(e.stateNode,e.type,t))}}function Uf(e){Fn?zn?zn.push(e):zn=[e]:Fn=e}function $f(){if(Fn){var e=Fn,t=zn;if(zn=Fn=null,bu(e),t)for(e=0;e<t.length;e++)bu(t[e])}}function Wf(e,t){return e(t)}function bf(){}var lo=!1;function Hf(e,t,n){if(lo)return e(t,n);lo=!0;try{return Wf(e,t,n)}finally{lo=!1,(Fn!==null||zn!==null)&&(bf(),$f())}}function Or(e,t){var n=e.stateNode;if(n===null)return null;var r=zs(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(E(231,t,typeof n));return n}var sa=!1;if(pt)try{var ar={};Object.defineProperty(ar,"passive",{get:function(){sa=!0}}),window.addEventListener("test",ar,ar),window.removeEventListener("test",ar,ar)}catch{sa=!1}function Jg(e,t,n,r,i,s,o,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Cr=!1,ns=null,rs=!1,oa=null,ey={onError:function(e){Cr=!0,ns=e}};function ty(e,t,n,r,i,s,o,a,l){Cr=!1,ns=null,Jg.apply(ey,arguments)}function ny(e,t,n,r,i,s,o,a,l){if(ty.apply(this,arguments),Cr){if(Cr){var u=ns;Cr=!1,ns=null}else throw Error(E(198));rs||(rs=!0,oa=u)}}function mn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Kf(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Hu(e){if(mn(e)!==e)throw Error(E(188))}function ry(e){var t=e.alternate;if(!t){if(t=mn(e),t===null)throw Error(E(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var s=i.alternate;if(s===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return Hu(i),e;if(s===r)return Hu(i),t;s=s.sibling}throw Error(E(188))}if(n.return!==r.return)n=i,r=s;else{for(var o=!1,a=i.child;a;){if(a===n){o=!0,n=i,r=s;break}if(a===r){o=!0,r=i,n=s;break}a=a.sibling}if(!o){for(a=s.child;a;){if(a===n){o=!0,n=s,r=i;break}if(a===r){o=!0,r=s,n=i;break}a=a.sibling}if(!o)throw Error(E(189))}}if(n.alternate!==r)throw Error(E(190))}if(n.tag!==3)throw Error(E(188));return n.stateNode.current===n?e:t}function Gf(e){return e=ry(e),e!==null?Qf(e):null}function Qf(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Qf(e);if(t!==null)return t;e=e.sibling}return null}var Yf=De.unstable_scheduleCallback,Ku=De.unstable_cancelCallback,iy=De.unstable_shouldYield,sy=De.unstable_requestPaint,J=De.unstable_now,oy=De.unstable_getCurrentPriorityLevel,pl=De.unstable_ImmediatePriority,Xf=De.unstable_UserBlockingPriority,is=De.unstable_NormalPriority,ay=De.unstable_LowPriority,Zf=De.unstable_IdlePriority,_s=null,nt=null;function ly(e){if(nt&&typeof nt.onCommitFiberRoot=="function")try{nt.onCommitFiberRoot(_s,e,void 0,(e.current.flags&128)===128)}catch{}}var Xe=Math.clz32?Math.clz32:dy,uy=Math.log,cy=Math.LN2;function dy(e){return e>>>=0,e===0?32:31-(uy(e)/cy|0)|0}var Ci=64,Pi=4194304;function yr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ss(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,s=e.pingedLanes,o=n&268435455;if(o!==0){var a=o&~i;a!==0?r=yr(a):(s&=o,s!==0&&(r=yr(s)))}else o=n&~i,o!==0?r=yr(o):s!==0&&(r=yr(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,s=t&-t,i>=s||i===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Xe(t),i=1<<n,r|=e[n],t&=~i;return r}function fy(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function hy(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,s=e.pendingLanes;0<s;){var o=31-Xe(s),a=1<<o,l=i[o];l===-1?(!(a&n)||a&r)&&(i[o]=fy(a,t)):l<=t&&(e.expiredLanes|=a),s&=~a}}function aa(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function qf(){var e=Ci;return Ci<<=1,!(Ci&4194240)&&(Ci=64),e}function uo(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function oi(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Xe(t),e[t]=n}function py(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-Xe(n),s=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~s}}function ml(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Xe(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var O=0;function Jf(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var eh,gl,th,nh,rh,la=!1,Ei=[],Mt=null,Rt=null,Lt=null,Fr=new Map,zr=new Map,Et=[],my="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Gu(e,t){switch(e){case"focusin":case"focusout":Mt=null;break;case"dragenter":case"dragleave":Rt=null;break;case"mouseover":case"mouseout":Lt=null;break;case"pointerover":case"pointerout":Fr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":zr.delete(t.pointerId)}}function lr(e,t,n,r,i,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[i]},t!==null&&(t=li(t),t!==null&&gl(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function gy(e,t,n,r,i){switch(t){case"focusin":return Mt=lr(Mt,e,t,n,r,i),!0;case"dragenter":return Rt=lr(Rt,e,t,n,r,i),!0;case"mouseover":return Lt=lr(Lt,e,t,n,r,i),!0;case"pointerover":var s=i.pointerId;return Fr.set(s,lr(Fr.get(s)||null,e,t,n,r,i)),!0;case"gotpointercapture":return s=i.pointerId,zr.set(s,lr(zr.get(s)||null,e,t,n,r,i)),!0}return!1}function ih(e){var t=tn(e.target);if(t!==null){var n=mn(t);if(n!==null){if(t=n.tag,t===13){if(t=Kf(n),t!==null){e.blockedOn=t,rh(e.priority,function(){th(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function $i(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=ua(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);ra=r,n.target.dispatchEvent(r),ra=null}else return t=li(n),t!==null&&gl(t),e.blockedOn=n,!1;t.shift()}return!0}function Qu(e,t,n){$i(e)&&n.delete(t)}function yy(){la=!1,Mt!==null&&$i(Mt)&&(Mt=null),Rt!==null&&$i(Rt)&&(Rt=null),Lt!==null&&$i(Lt)&&(Lt=null),Fr.forEach(Qu),zr.forEach(Qu)}function ur(e,t){e.blockedOn===t&&(e.blockedOn=null,la||(la=!0,De.unstable_scheduleCallback(De.unstable_NormalPriority,yy)))}function Br(e){function t(i){return ur(i,e)}if(0<Ei.length){ur(Ei[0],e);for(var n=1;n<Ei.length;n++){var r=Ei[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Mt!==null&&ur(Mt,e),Rt!==null&&ur(Rt,e),Lt!==null&&ur(Lt,e),Fr.forEach(t),zr.forEach(t),n=0;n<Et.length;n++)r=Et[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Et.length&&(n=Et[0],n.blockedOn===null);)ih(n),n.blockedOn===null&&Et.shift()}var Bn=xt.ReactCurrentBatchConfig,os=!0;function vy(e,t,n,r){var i=O,s=Bn.transition;Bn.transition=null;try{O=1,yl(e,t,n,r)}finally{O=i,Bn.transition=s}}function xy(e,t,n,r){var i=O,s=Bn.transition;Bn.transition=null;try{O=4,yl(e,t,n,r)}finally{O=i,Bn.transition=s}}function yl(e,t,n,r){if(os){var i=ua(e,t,n,r);if(i===null)wo(e,t,r,as,n),Gu(e,r);else if(gy(i,e,t,n,r))r.stopPropagation();else if(Gu(e,r),t&4&&-1<my.indexOf(e)){for(;i!==null;){var s=li(i);if(s!==null&&eh(s),s=ua(e,t,n,r),s===null&&wo(e,t,r,as,n),s===i)break;i=s}i!==null&&r.stopPropagation()}else wo(e,t,r,null,n)}}var as=null;function ua(e,t,n,r){if(as=null,e=hl(r),e=tn(e),e!==null)if(t=mn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Kf(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return as=e,null}function sh(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(oy()){case pl:return 1;case Xf:return 4;case is:case ay:return 16;case Zf:return 536870912;default:return 16}default:return 16}}var jt=null,vl=null,Wi=null;function oh(){if(Wi)return Wi;var e,t=vl,n=t.length,r,i="value"in jt?jt.value:jt.textContent,s=i.length;for(e=0;e<n&&t[e]===i[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===i[s-r];r++);return Wi=i.slice(e,1<r?1-r:void 0)}function bi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ti(){return!0}function Yu(){return!1}function Ie(e){function t(n,r,i,s,o){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=s,this.target=o,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(s):s[a]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?Ti:Yu,this.isPropagationStopped=Yu,this}return Q(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ti)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ti)},persist:function(){},isPersistent:Ti}),t}var er={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},xl=Ie(er),ai=Q({},er,{view:0,detail:0}),wy=Ie(ai),co,fo,cr,Is=Q({},ai,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:wl,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==cr&&(cr&&e.type==="mousemove"?(co=e.screenX-cr.screenX,fo=e.screenY-cr.screenY):fo=co=0,cr=e),co)},movementY:function(e){return"movementY"in e?e.movementY:fo}}),Xu=Ie(Is),Sy=Q({},Is,{dataTransfer:0}),ky=Ie(Sy),Cy=Q({},ai,{relatedTarget:0}),ho=Ie(Cy),Py=Q({},er,{animationName:0,elapsedTime:0,pseudoElement:0}),Ey=Ie(Py),Ty=Q({},er,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),jy=Ie(Ty),Ny=Q({},er,{data:0}),Zu=Ie(Ny),Ay={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},My={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ry={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Ly(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Ry[e])?!!t[e]:!1}function wl(){return Ly}var Dy=Q({},ai,{key:function(e){if(e.key){var t=Ay[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=bi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?My[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:wl,charCode:function(e){return e.type==="keypress"?bi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?bi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Vy=Ie(Dy),_y=Q({},Is,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),qu=Ie(_y),Iy=Q({},ai,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:wl}),Oy=Ie(Iy),Fy=Q({},er,{propertyName:0,elapsedTime:0,pseudoElement:0}),zy=Ie(Fy),By=Q({},Is,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Uy=Ie(By),$y=[9,13,27,32],Sl=pt&&"CompositionEvent"in window,Pr=null;pt&&"documentMode"in document&&(Pr=document.documentMode);var Wy=pt&&"TextEvent"in window&&!Pr,ah=pt&&(!Sl||Pr&&8<Pr&&11>=Pr),Ju=" ",ec=!1;function lh(e,t){switch(e){case"keyup":return $y.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function uh(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var kn=!1;function by(e,t){switch(e){case"compositionend":return uh(t);case"keypress":return t.which!==32?null:(ec=!0,Ju);case"textInput":return e=t.data,e===Ju&&ec?null:e;default:return null}}function Hy(e,t){if(kn)return e==="compositionend"||!Sl&&lh(e,t)?(e=oh(),Wi=vl=jt=null,kn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return ah&&t.locale!=="ko"?null:t.data;default:return null}}var Ky={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function tc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Ky[e.type]:t==="textarea"}function ch(e,t,n,r){Uf(r),t=ls(t,"onChange"),0<t.length&&(n=new xl("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Er=null,Ur=null;function Gy(e){Sh(e,0)}function Os(e){var t=En(e);if(Vf(t))return e}function Qy(e,t){if(e==="change")return t}var dh=!1;if(pt){var po;if(pt){var mo="oninput"in document;if(!mo){var nc=document.createElement("div");nc.setAttribute("oninput","return;"),mo=typeof nc.oninput=="function"}po=mo}else po=!1;dh=po&&(!document.documentMode||9<document.documentMode)}function rc(){Er&&(Er.detachEvent("onpropertychange",fh),Ur=Er=null)}function fh(e){if(e.propertyName==="value"&&Os(Ur)){var t=[];ch(t,Ur,e,hl(e)),Hf(Gy,t)}}function Yy(e,t,n){e==="focusin"?(rc(),Er=t,Ur=n,Er.attachEvent("onpropertychange",fh)):e==="focusout"&&rc()}function Xy(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Os(Ur)}function Zy(e,t){if(e==="click")return Os(t)}function qy(e,t){if(e==="input"||e==="change")return Os(t)}function Jy(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var qe=typeof Object.is=="function"?Object.is:Jy;function $r(e,t){if(qe(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!Ho.call(t,i)||!qe(e[i],t[i]))return!1}return!0}function ic(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function sc(e,t){var n=ic(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=ic(n)}}function hh(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?hh(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function ph(){for(var e=window,t=ts();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ts(e.document)}return t}function kl(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function ev(e){var t=ph(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&hh(n.ownerDocument.documentElement,n)){if(r!==null&&kl(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,s=Math.min(r.start,i);r=r.end===void 0?s:Math.min(r.end,i),!e.extend&&s>r&&(i=r,r=s,s=i),i=sc(n,s);var o=sc(n,r);i&&o&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var tv=pt&&"documentMode"in document&&11>=document.documentMode,Cn=null,ca=null,Tr=null,da=!1;function oc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;da||Cn==null||Cn!==ts(r)||(r=Cn,"selectionStart"in r&&kl(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Tr&&$r(Tr,r)||(Tr=r,r=ls(ca,"onSelect"),0<r.length&&(t=new xl("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Cn)))}function ji(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Pn={animationend:ji("Animation","AnimationEnd"),animationiteration:ji("Animation","AnimationIteration"),animationstart:ji("Animation","AnimationStart"),transitionend:ji("Transition","TransitionEnd")},go={},mh={};pt&&(mh=document.createElement("div").style,"AnimationEvent"in window||(delete Pn.animationend.animation,delete Pn.animationiteration.animation,delete Pn.animationstart.animation),"TransitionEvent"in window||delete Pn.transitionend.transition);function Fs(e){if(go[e])return go[e];if(!Pn[e])return e;var t=Pn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in mh)return go[e]=t[n];return e}var gh=Fs("animationend"),yh=Fs("animationiteration"),vh=Fs("animationstart"),xh=Fs("transitionend"),wh=new Map,ac="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Wt(e,t){wh.set(e,t),pn(t,[e])}for(var yo=0;yo<ac.length;yo++){var vo=ac[yo],nv=vo.toLowerCase(),rv=vo[0].toUpperCase()+vo.slice(1);Wt(nv,"on"+rv)}Wt(gh,"onAnimationEnd");Wt(yh,"onAnimationIteration");Wt(vh,"onAnimationStart");Wt("dblclick","onDoubleClick");Wt("focusin","onFocus");Wt("focusout","onBlur");Wt(xh,"onTransitionEnd");Wn("onMouseEnter",["mouseout","mouseover"]);Wn("onMouseLeave",["mouseout","mouseover"]);Wn("onPointerEnter",["pointerout","pointerover"]);Wn("onPointerLeave",["pointerout","pointerover"]);pn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));pn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));pn("onBeforeInput",["compositionend","keypress","textInput","paste"]);pn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));pn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));pn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var vr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),iv=new Set("cancel close invalid load scroll toggle".split(" ").concat(vr));function lc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,ny(r,t,void 0,e),e.currentTarget=null}function Sh(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var o=r.length-1;0<=o;o--){var a=r[o],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==s&&i.isPropagationStopped())break e;lc(i,a,u),s=l}else for(o=0;o<r.length;o++){if(a=r[o],l=a.instance,u=a.currentTarget,a=a.listener,l!==s&&i.isPropagationStopped())break e;lc(i,a,u),s=l}}}if(rs)throw e=oa,rs=!1,oa=null,e}function B(e,t){var n=t[ga];n===void 0&&(n=t[ga]=new Set);var r=e+"__bubble";n.has(r)||(kh(t,e,2,!1),n.add(r))}function xo(e,t,n){var r=0;t&&(r|=4),kh(n,e,r,t)}var Ni="_reactListening"+Math.random().toString(36).slice(2);function Wr(e){if(!e[Ni]){e[Ni]=!0,Af.forEach(function(n){n!=="selectionchange"&&(iv.has(n)||xo(n,!1,e),xo(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ni]||(t[Ni]=!0,xo("selectionchange",!1,t))}}function kh(e,t,n,r){switch(sh(t)){case 1:var i=vy;break;case 4:i=xy;break;default:i=yl}n=i.bind(null,t,n,e),i=void 0,!sa||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function wo(e,t,n,r,i){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var a=r.stateNode.containerInfo;if(a===i||a.nodeType===8&&a.parentNode===i)break;if(o===4)for(o=r.return;o!==null;){var l=o.tag;if((l===3||l===4)&&(l=o.stateNode.containerInfo,l===i||l.nodeType===8&&l.parentNode===i))return;o=o.return}for(;a!==null;){if(o=tn(a),o===null)return;if(l=o.tag,l===5||l===6){r=s=o;continue e}a=a.parentNode}}r=r.return}Hf(function(){var u=s,c=hl(n),d=[];e:{var h=wh.get(e);if(h!==void 0){var y=xl,v=e;switch(e){case"keypress":if(bi(n)===0)break e;case"keydown":case"keyup":y=Vy;break;case"focusin":v="focus",y=ho;break;case"focusout":v="blur",y=ho;break;case"beforeblur":case"afterblur":y=ho;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=Xu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=ky;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=Oy;break;case gh:case yh:case vh:y=Ey;break;case xh:y=zy;break;case"scroll":y=wy;break;case"wheel":y=Uy;break;case"copy":case"cut":case"paste":y=jy;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=qu}var x=(t&4)!==0,C=!x&&e==="scroll",p=x?h!==null?h+"Capture":null:h;x=[];for(var m=u,g;m!==null;){g=m;var w=g.stateNode;if(g.tag===5&&w!==null&&(g=w,p!==null&&(w=Or(m,p),w!=null&&x.push(br(m,w,g)))),C)break;m=m.return}0<x.length&&(h=new y(h,v,null,n,c),d.push({event:h,listeners:x}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",h&&n!==ra&&(v=n.relatedTarget||n.fromElement)&&(tn(v)||v[mt]))break e;if((y||h)&&(h=c.window===c?c:(h=c.ownerDocument)?h.defaultView||h.parentWindow:window,y?(v=n.relatedTarget||n.toElement,y=u,v=v?tn(v):null,v!==null&&(C=mn(v),v!==C||v.tag!==5&&v.tag!==6)&&(v=null)):(y=null,v=u),y!==v)){if(x=Xu,w="onMouseLeave",p="onMouseEnter",m="mouse",(e==="pointerout"||e==="pointerover")&&(x=qu,w="onPointerLeave",p="onPointerEnter",m="pointer"),C=y==null?h:En(y),g=v==null?h:En(v),h=new x(w,m+"leave",y,n,c),h.target=C,h.relatedTarget=g,w=null,tn(c)===u&&(x=new x(p,m+"enter",v,n,c),x.target=g,x.relatedTarget=C,w=x),C=w,y&&v)t:{for(x=y,p=v,m=0,g=x;g;g=xn(g))m++;for(g=0,w=p;w;w=xn(w))g++;for(;0<m-g;)x=xn(x),m--;for(;0<g-m;)p=xn(p),g--;for(;m--;){if(x===p||p!==null&&x===p.alternate)break t;x=xn(x),p=xn(p)}x=null}else x=null;y!==null&&uc(d,h,y,x,!1),v!==null&&C!==null&&uc(d,C,v,x,!0)}}e:{if(h=u?En(u):window,y=h.nodeName&&h.nodeName.toLowerCase(),y==="select"||y==="input"&&h.type==="file")var k=Qy;else if(tc(h))if(dh)k=qy;else{k=Xy;var T=Yy}else(y=h.nodeName)&&y.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(k=Zy);if(k&&(k=k(e,u))){ch(d,k,n,c);break e}T&&T(e,h,u),e==="focusout"&&(T=h._wrapperState)&&T.controlled&&h.type==="number"&&qo(h,"number",h.value)}switch(T=u?En(u):window,e){case"focusin":(tc(T)||T.contentEditable==="true")&&(Cn=T,ca=u,Tr=null);break;case"focusout":Tr=ca=Cn=null;break;case"mousedown":da=!0;break;case"contextmenu":case"mouseup":case"dragend":da=!1,oc(d,n,c);break;case"selectionchange":if(tv)break;case"keydown":case"keyup":oc(d,n,c)}var j;if(Sl)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else kn?lh(e,n)&&(P="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(P="onCompositionStart");P&&(ah&&n.locale!=="ko"&&(kn||P!=="onCompositionStart"?P==="onCompositionEnd"&&kn&&(j=oh()):(jt=c,vl="value"in jt?jt.value:jt.textContent,kn=!0)),T=ls(u,P),0<T.length&&(P=new Zu(P,e,null,n,c),d.push({event:P,listeners:T}),j?P.data=j:(j=uh(n),j!==null&&(P.data=j)))),(j=Wy?by(e,n):Hy(e,n))&&(u=ls(u,"onBeforeInput"),0<u.length&&(c=new Zu("onBeforeInput","beforeinput",null,n,c),d.push({event:c,listeners:u}),c.data=j))}Sh(d,t)})}function br(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ls(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,s=i.stateNode;i.tag===5&&s!==null&&(i=s,s=Or(e,n),s!=null&&r.unshift(br(e,s,i)),s=Or(e,t),s!=null&&r.push(br(e,s,i))),e=e.return}return r}function xn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function uc(e,t,n,r,i){for(var s=t._reactName,o=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,i?(l=Or(n,s),l!=null&&o.unshift(br(n,l,a))):i||(l=Or(n,s),l!=null&&o.push(br(n,l,a)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var sv=/\r\n?/g,ov=/\u0000|\uFFFD/g;function cc(e){return(typeof e=="string"?e:""+e).replace(sv,`
`).replace(ov,"")}function Ai(e,t,n){if(t=cc(t),cc(e)!==t&&n)throw Error(E(425))}function us(){}var fa=null,ha=null;function pa(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var ma=typeof setTimeout=="function"?setTimeout:void 0,av=typeof clearTimeout=="function"?clearTimeout:void 0,dc=typeof Promise=="function"?Promise:void 0,lv=typeof queueMicrotask=="function"?queueMicrotask:typeof dc<"u"?function(e){return dc.resolve(null).then(e).catch(uv)}:ma;function uv(e){setTimeout(function(){throw e})}function So(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Br(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Br(t)}function Dt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function fc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var tr=Math.random().toString(36).slice(2),tt="__reactFiber$"+tr,Hr="__reactProps$"+tr,mt="__reactContainer$"+tr,ga="__reactEvents$"+tr,cv="__reactListeners$"+tr,dv="__reactHandles$"+tr;function tn(e){var t=e[tt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[mt]||n[tt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=fc(e);e!==null;){if(n=e[tt])return n;e=fc(e)}return t}e=n,n=e.parentNode}return null}function li(e){return e=e[tt]||e[mt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function En(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(E(33))}function zs(e){return e[Hr]||null}var ya=[],Tn=-1;function bt(e){return{current:e}}function U(e){0>Tn||(e.current=ya[Tn],ya[Tn]=null,Tn--)}function z(e,t){Tn++,ya[Tn]=e.current,e.current=t}var Bt={},xe=bt(Bt),Te=bt(!1),un=Bt;function bn(e,t){var n=e.type.contextTypes;if(!n)return Bt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},s;for(s in n)i[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function je(e){return e=e.childContextTypes,e!=null}function cs(){U(Te),U(xe)}function hc(e,t,n){if(xe.current!==Bt)throw Error(E(168));z(xe,t),z(Te,n)}function Ch(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(E(108,Yg(e)||"Unknown",i));return Q({},n,r)}function ds(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Bt,un=xe.current,z(xe,e),z(Te,Te.current),!0}function pc(e,t,n){var r=e.stateNode;if(!r)throw Error(E(169));n?(e=Ch(e,t,un),r.__reactInternalMemoizedMergedChildContext=e,U(Te),U(xe),z(xe,e)):U(Te),z(Te,n)}var lt=null,Bs=!1,ko=!1;function Ph(e){lt===null?lt=[e]:lt.push(e)}function fv(e){Bs=!0,Ph(e)}function Ht(){if(!ko&&lt!==null){ko=!0;var e=0,t=O;try{var n=lt;for(O=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}lt=null,Bs=!1}catch(i){throw lt!==null&&(lt=lt.slice(e+1)),Yf(pl,Ht),i}finally{O=t,ko=!1}}return null}var jn=[],Nn=0,fs=null,hs=0,ze=[],Be=0,cn=null,ut=1,ct="";function Zt(e,t){jn[Nn++]=hs,jn[Nn++]=fs,fs=e,hs=t}function Eh(e,t,n){ze[Be++]=ut,ze[Be++]=ct,ze[Be++]=cn,cn=e;var r=ut;e=ct;var i=32-Xe(r)-1;r&=~(1<<i),n+=1;var s=32-Xe(t)+i;if(30<s){var o=i-i%5;s=(r&(1<<o)-1).toString(32),r>>=o,i-=o,ut=1<<32-Xe(t)+i|n<<i|r,ct=s+e}else ut=1<<s|n<<i|r,ct=e}function Cl(e){e.return!==null&&(Zt(e,1),Eh(e,1,0))}function Pl(e){for(;e===fs;)fs=jn[--Nn],jn[Nn]=null,hs=jn[--Nn],jn[Nn]=null;for(;e===cn;)cn=ze[--Be],ze[Be]=null,ct=ze[--Be],ze[Be]=null,ut=ze[--Be],ze[Be]=null}var Re=null,Me=null,W=!1,Ye=null;function Th(e,t){var n=Ue(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function mc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Re=e,Me=Dt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Re=e,Me=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=cn!==null?{id:ut,overflow:ct}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ue(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Re=e,Me=null,!0):!1;default:return!1}}function va(e){return(e.mode&1)!==0&&(e.flags&128)===0}function xa(e){if(W){var t=Me;if(t){var n=t;if(!mc(e,t)){if(va(e))throw Error(E(418));t=Dt(n.nextSibling);var r=Re;t&&mc(e,t)?Th(r,n):(e.flags=e.flags&-4097|2,W=!1,Re=e)}}else{if(va(e))throw Error(E(418));e.flags=e.flags&-4097|2,W=!1,Re=e}}}function gc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Re=e}function Mi(e){if(e!==Re)return!1;if(!W)return gc(e),W=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!pa(e.type,e.memoizedProps)),t&&(t=Me)){if(va(e))throw jh(),Error(E(418));for(;t;)Th(e,t),t=Dt(t.nextSibling)}if(gc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(E(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Me=Dt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Me=null}}else Me=Re?Dt(e.stateNode.nextSibling):null;return!0}function jh(){for(var e=Me;e;)e=Dt(e.nextSibling)}function Hn(){Me=Re=null,W=!1}function El(e){Ye===null?Ye=[e]:Ye.push(e)}var hv=xt.ReactCurrentBatchConfig;function dr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(E(309));var r=n.stateNode}if(!r)throw Error(E(147,e));var i=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(o){var a=i.refs;o===null?delete a[s]:a[s]=o},t._stringRef=s,t)}if(typeof e!="string")throw Error(E(284));if(!n._owner)throw Error(E(290,e))}return e}function Ri(e,t){throw e=Object.prototype.toString.call(t),Error(E(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function yc(e){var t=e._init;return t(e._payload)}function Nh(e){function t(p,m){if(e){var g=p.deletions;g===null?(p.deletions=[m],p.flags|=16):g.push(m)}}function n(p,m){if(!e)return null;for(;m!==null;)t(p,m),m=m.sibling;return null}function r(p,m){for(p=new Map;m!==null;)m.key!==null?p.set(m.key,m):p.set(m.index,m),m=m.sibling;return p}function i(p,m){return p=Ot(p,m),p.index=0,p.sibling=null,p}function s(p,m,g){return p.index=g,e?(g=p.alternate,g!==null?(g=g.index,g<m?(p.flags|=2,m):g):(p.flags|=2,m)):(p.flags|=1048576,m)}function o(p){return e&&p.alternate===null&&(p.flags|=2),p}function a(p,m,g,w){return m===null||m.tag!==6?(m=Ao(g,p.mode,w),m.return=p,m):(m=i(m,g),m.return=p,m)}function l(p,m,g,w){var k=g.type;return k===Sn?c(p,m,g.props.children,w,g.key):m!==null&&(m.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===Ct&&yc(k)===m.type)?(w=i(m,g.props),w.ref=dr(p,m,g),w.return=p,w):(w=Zi(g.type,g.key,g.props,null,p.mode,w),w.ref=dr(p,m,g),w.return=p,w)}function u(p,m,g,w){return m===null||m.tag!==4||m.stateNode.containerInfo!==g.containerInfo||m.stateNode.implementation!==g.implementation?(m=Mo(g,p.mode,w),m.return=p,m):(m=i(m,g.children||[]),m.return=p,m)}function c(p,m,g,w,k){return m===null||m.tag!==7?(m=an(g,p.mode,w,k),m.return=p,m):(m=i(m,g),m.return=p,m)}function d(p,m,g){if(typeof m=="string"&&m!==""||typeof m=="number")return m=Ao(""+m,p.mode,g),m.return=p,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case wi:return g=Zi(m.type,m.key,m.props,null,p.mode,g),g.ref=dr(p,null,m),g.return=p,g;case wn:return m=Mo(m,p.mode,g),m.return=p,m;case Ct:var w=m._init;return d(p,w(m._payload),g)}if(gr(m)||or(m))return m=an(m,p.mode,g,null),m.return=p,m;Ri(p,m)}return null}function h(p,m,g,w){var k=m!==null?m.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return k!==null?null:a(p,m,""+g,w);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case wi:return g.key===k?l(p,m,g,w):null;case wn:return g.key===k?u(p,m,g,w):null;case Ct:return k=g._init,h(p,m,k(g._payload),w)}if(gr(g)||or(g))return k!==null?null:c(p,m,g,w,null);Ri(p,g)}return null}function y(p,m,g,w,k){if(typeof w=="string"&&w!==""||typeof w=="number")return p=p.get(g)||null,a(m,p,""+w,k);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case wi:return p=p.get(w.key===null?g:w.key)||null,l(m,p,w,k);case wn:return p=p.get(w.key===null?g:w.key)||null,u(m,p,w,k);case Ct:var T=w._init;return y(p,m,g,T(w._payload),k)}if(gr(w)||or(w))return p=p.get(g)||null,c(m,p,w,k,null);Ri(m,w)}return null}function v(p,m,g,w){for(var k=null,T=null,j=m,P=m=0,V=null;j!==null&&P<g.length;P++){j.index>P?(V=j,j=null):V=j.sibling;var R=h(p,j,g[P],w);if(R===null){j===null&&(j=V);break}e&&j&&R.alternate===null&&t(p,j),m=s(R,m,P),T===null?k=R:T.sibling=R,T=R,j=V}if(P===g.length)return n(p,j),W&&Zt(p,P),k;if(j===null){for(;P<g.length;P++)j=d(p,g[P],w),j!==null&&(m=s(j,m,P),T===null?k=j:T.sibling=j,T=j);return W&&Zt(p,P),k}for(j=r(p,j);P<g.length;P++)V=y(j,p,P,g[P],w),V!==null&&(e&&V.alternate!==null&&j.delete(V.key===null?P:V.key),m=s(V,m,P),T===null?k=V:T.sibling=V,T=V);return e&&j.forEach(function(oe){return t(p,oe)}),W&&Zt(p,P),k}function x(p,m,g,w){var k=or(g);if(typeof k!="function")throw Error(E(150));if(g=k.call(g),g==null)throw Error(E(151));for(var T=k=null,j=m,P=m=0,V=null,R=g.next();j!==null&&!R.done;P++,R=g.next()){j.index>P?(V=j,j=null):V=j.sibling;var oe=h(p,j,R.value,w);if(oe===null){j===null&&(j=V);break}e&&j&&oe.alternate===null&&t(p,j),m=s(oe,m,P),T===null?k=oe:T.sibling=oe,T=oe,j=V}if(R.done)return n(p,j),W&&Zt(p,P),k;if(j===null){for(;!R.done;P++,R=g.next())R=d(p,R.value,w),R!==null&&(m=s(R,m,P),T===null?k=R:T.sibling=R,T=R);return W&&Zt(p,P),k}for(j=r(p,j);!R.done;P++,R=g.next())R=y(j,p,P,R.value,w),R!==null&&(e&&R.alternate!==null&&j.delete(R.key===null?P:R.key),m=s(R,m,P),T===null?k=R:T.sibling=R,T=R);return e&&j.forEach(function(wt){return t(p,wt)}),W&&Zt(p,P),k}function C(p,m,g,w){if(typeof g=="object"&&g!==null&&g.type===Sn&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case wi:e:{for(var k=g.key,T=m;T!==null;){if(T.key===k){if(k=g.type,k===Sn){if(T.tag===7){n(p,T.sibling),m=i(T,g.props.children),m.return=p,p=m;break e}}else if(T.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===Ct&&yc(k)===T.type){n(p,T.sibling),m=i(T,g.props),m.ref=dr(p,T,g),m.return=p,p=m;break e}n(p,T);break}else t(p,T);T=T.sibling}g.type===Sn?(m=an(g.props.children,p.mode,w,g.key),m.return=p,p=m):(w=Zi(g.type,g.key,g.props,null,p.mode,w),w.ref=dr(p,m,g),w.return=p,p=w)}return o(p);case wn:e:{for(T=g.key;m!==null;){if(m.key===T)if(m.tag===4&&m.stateNode.containerInfo===g.containerInfo&&m.stateNode.implementation===g.implementation){n(p,m.sibling),m=i(m,g.children||[]),m.return=p,p=m;break e}else{n(p,m);break}else t(p,m);m=m.sibling}m=Mo(g,p.mode,w),m.return=p,p=m}return o(p);case Ct:return T=g._init,C(p,m,T(g._payload),w)}if(gr(g))return v(p,m,g,w);if(or(g))return x(p,m,g,w);Ri(p,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,m!==null&&m.tag===6?(n(p,m.sibling),m=i(m,g),m.return=p,p=m):(n(p,m),m=Ao(g,p.mode,w),m.return=p,p=m),o(p)):n(p,m)}return C}var Kn=Nh(!0),Ah=Nh(!1),ps=bt(null),ms=null,An=null,Tl=null;function jl(){Tl=An=ms=null}function Nl(e){var t=ps.current;U(ps),e._currentValue=t}function wa(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Un(e,t){ms=e,Tl=An=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ee=!0),e.firstContext=null)}function We(e){var t=e._currentValue;if(Tl!==e)if(e={context:e,memoizedValue:t,next:null},An===null){if(ms===null)throw Error(E(308));An=e,ms.dependencies={lanes:0,firstContext:e}}else An=An.next=e;return t}var nn=null;function Al(e){nn===null?nn=[e]:nn.push(e)}function Mh(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,Al(t)):(n.next=i.next,i.next=n),t.interleaved=n,gt(e,r)}function gt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Pt=!1;function Ml(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Rh(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function dt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Vt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,I&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,gt(e,n)}return i=r.interleaved,i===null?(t.next=t,Al(r)):(t.next=i.next,i.next=t),r.interleaved=t,gt(e,n)}function Hi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ml(e,n)}}function vc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?i=s=o:s=s.next=o,n=n.next}while(n!==null);s===null?i=s=t:s=s.next=t}else i=s=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function gs(e,t,n,r){var i=e.updateQueue;Pt=!1;var s=i.firstBaseUpdate,o=i.lastBaseUpdate,a=i.shared.pending;if(a!==null){i.shared.pending=null;var l=a,u=l.next;l.next=null,o===null?s=u:o.next=u,o=l;var c=e.alternate;c!==null&&(c=c.updateQueue,a=c.lastBaseUpdate,a!==o&&(a===null?c.firstBaseUpdate=u:a.next=u,c.lastBaseUpdate=l))}if(s!==null){var d=i.baseState;o=0,c=u=l=null,a=s;do{var h=a.lane,y=a.eventTime;if((r&h)===h){c!==null&&(c=c.next={eventTime:y,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var v=e,x=a;switch(h=t,y=n,x.tag){case 1:if(v=x.payload,typeof v=="function"){d=v.call(y,d,h);break e}d=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=x.payload,h=typeof v=="function"?v.call(y,d,h):v,h==null)break e;d=Q({},d,h);break e;case 2:Pt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,h=i.effects,h===null?i.effects=[a]:h.push(a))}else y={eventTime:y,lane:h,tag:a.tag,payload:a.payload,callback:a.callback,next:null},c===null?(u=c=y,l=d):c=c.next=y,o|=h;if(a=a.next,a===null){if(a=i.shared.pending,a===null)break;h=a,a=h.next,h.next=null,i.lastBaseUpdate=h,i.shared.pending=null}}while(!0);if(c===null&&(l=d),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do o|=i.lane,i=i.next;while(i!==t)}else s===null&&(i.shared.lanes=0);fn|=o,e.lanes=o,e.memoizedState=d}}function xc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(E(191,i));i.call(r)}}}var ui={},rt=bt(ui),Kr=bt(ui),Gr=bt(ui);function rn(e){if(e===ui)throw Error(E(174));return e}function Rl(e,t){switch(z(Gr,t),z(Kr,e),z(rt,ui),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ea(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=ea(t,e)}U(rt),z(rt,t)}function Gn(){U(rt),U(Kr),U(Gr)}function Lh(e){rn(Gr.current);var t=rn(rt.current),n=ea(t,e.type);t!==n&&(z(Kr,e),z(rt,n))}function Ll(e){Kr.current===e&&(U(rt),U(Kr))}var H=bt(0);function ys(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Co=[];function Dl(){for(var e=0;e<Co.length;e++)Co[e]._workInProgressVersionPrimary=null;Co.length=0}var Ki=xt.ReactCurrentDispatcher,Po=xt.ReactCurrentBatchConfig,dn=0,G=null,ie=null,le=null,vs=!1,jr=!1,Qr=0,pv=0;function pe(){throw Error(E(321))}function Vl(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!qe(e[n],t[n]))return!1;return!0}function _l(e,t,n,r,i,s){if(dn=s,G=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ki.current=e===null||e.memoizedState===null?vv:xv,e=n(r,i),jr){s=0;do{if(jr=!1,Qr=0,25<=s)throw Error(E(301));s+=1,le=ie=null,t.updateQueue=null,Ki.current=wv,e=n(r,i)}while(jr)}if(Ki.current=xs,t=ie!==null&&ie.next!==null,dn=0,le=ie=G=null,vs=!1,t)throw Error(E(300));return e}function Il(){var e=Qr!==0;return Qr=0,e}function et(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return le===null?G.memoizedState=le=e:le=le.next=e,le}function be(){if(ie===null){var e=G.alternate;e=e!==null?e.memoizedState:null}else e=ie.next;var t=le===null?G.memoizedState:le.next;if(t!==null)le=t,ie=e;else{if(e===null)throw Error(E(310));ie=e,e={memoizedState:ie.memoizedState,baseState:ie.baseState,baseQueue:ie.baseQueue,queue:ie.queue,next:null},le===null?G.memoizedState=le=e:le=le.next=e}return le}function Yr(e,t){return typeof t=="function"?t(e):t}function Eo(e){var t=be(),n=t.queue;if(n===null)throw Error(E(311));n.lastRenderedReducer=e;var r=ie,i=r.baseQueue,s=n.pending;if(s!==null){if(i!==null){var o=i.next;i.next=s.next,s.next=o}r.baseQueue=i=s,n.pending=null}if(i!==null){s=i.next,r=r.baseState;var a=o=null,l=null,u=s;do{var c=u.lane;if((dn&c)===c)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=d,o=r):l=l.next=d,G.lanes|=c,fn|=c}u=u.next}while(u!==null&&u!==s);l===null?o=r:l.next=a,qe(r,t.memoizedState)||(Ee=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do s=i.lane,G.lanes|=s,fn|=s,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function To(e){var t=be(),n=t.queue;if(n===null)throw Error(E(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,s=t.memoizedState;if(i!==null){n.pending=null;var o=i=i.next;do s=e(s,o.action),o=o.next;while(o!==i);qe(s,t.memoizedState)||(Ee=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function Dh(){}function Vh(e,t){var n=G,r=be(),i=t(),s=!qe(r.memoizedState,i);if(s&&(r.memoizedState=i,Ee=!0),r=r.queue,Ol(Oh.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||le!==null&&le.memoizedState.tag&1){if(n.flags|=2048,Xr(9,Ih.bind(null,n,r,i,t),void 0,null),ue===null)throw Error(E(349));dn&30||_h(n,t,i)}return i}function _h(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=G.updateQueue,t===null?(t={lastEffect:null,stores:null},G.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Ih(e,t,n,r){t.value=n,t.getSnapshot=r,Fh(t)&&zh(e)}function Oh(e,t,n){return n(function(){Fh(t)&&zh(e)})}function Fh(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!qe(e,n)}catch{return!0}}function zh(e){var t=gt(e,1);t!==null&&Ze(t,e,1,-1)}function wc(e){var t=et();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Yr,lastRenderedState:e},t.queue=e,e=e.dispatch=yv.bind(null,G,e),[t.memoizedState,e]}function Xr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=G.updateQueue,t===null?(t={lastEffect:null,stores:null},G.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Bh(){return be().memoizedState}function Gi(e,t,n,r){var i=et();G.flags|=e,i.memoizedState=Xr(1|t,n,void 0,r===void 0?null:r)}function Us(e,t,n,r){var i=be();r=r===void 0?null:r;var s=void 0;if(ie!==null){var o=ie.memoizedState;if(s=o.destroy,r!==null&&Vl(r,o.deps)){i.memoizedState=Xr(t,n,s,r);return}}G.flags|=e,i.memoizedState=Xr(1|t,n,s,r)}function Sc(e,t){return Gi(8390656,8,e,t)}function Ol(e,t){return Us(2048,8,e,t)}function Uh(e,t){return Us(4,2,e,t)}function $h(e,t){return Us(4,4,e,t)}function Wh(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function bh(e,t,n){return n=n!=null?n.concat([e]):null,Us(4,4,Wh.bind(null,t,e),n)}function Fl(){}function Hh(e,t){var n=be();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Vl(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Kh(e,t){var n=be();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Vl(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Gh(e,t,n){return dn&21?(qe(n,t)||(n=qf(),G.lanes|=n,fn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ee=!0),e.memoizedState=n)}function mv(e,t){var n=O;O=n!==0&&4>n?n:4,e(!0);var r=Po.transition;Po.transition={};try{e(!1),t()}finally{O=n,Po.transition=r}}function Qh(){return be().memoizedState}function gv(e,t,n){var r=It(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Yh(e))Xh(t,n);else if(n=Mh(e,t,n,r),n!==null){var i=Se();Ze(n,e,r,i),Zh(n,t,r)}}function yv(e,t,n){var r=It(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Yh(e))Xh(t,i);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var o=t.lastRenderedState,a=s(o,n);if(i.hasEagerState=!0,i.eagerState=a,qe(a,o)){var l=t.interleaved;l===null?(i.next=i,Al(t)):(i.next=l.next,l.next=i),t.interleaved=i;return}}catch{}finally{}n=Mh(e,t,i,r),n!==null&&(i=Se(),Ze(n,e,r,i),Zh(n,t,r))}}function Yh(e){var t=e.alternate;return e===G||t!==null&&t===G}function Xh(e,t){jr=vs=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Zh(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ml(e,n)}}var xs={readContext:We,useCallback:pe,useContext:pe,useEffect:pe,useImperativeHandle:pe,useInsertionEffect:pe,useLayoutEffect:pe,useMemo:pe,useReducer:pe,useRef:pe,useState:pe,useDebugValue:pe,useDeferredValue:pe,useTransition:pe,useMutableSource:pe,useSyncExternalStore:pe,useId:pe,unstable_isNewReconciler:!1},vv={readContext:We,useCallback:function(e,t){return et().memoizedState=[e,t===void 0?null:t],e},useContext:We,useEffect:Sc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Gi(4194308,4,Wh.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Gi(4194308,4,e,t)},useInsertionEffect:function(e,t){return Gi(4,2,e,t)},useMemo:function(e,t){var n=et();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=et();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=gv.bind(null,G,e),[r.memoizedState,e]},useRef:function(e){var t=et();return e={current:e},t.memoizedState=e},useState:wc,useDebugValue:Fl,useDeferredValue:function(e){return et().memoizedState=e},useTransition:function(){var e=wc(!1),t=e[0];return e=mv.bind(null,e[1]),et().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=G,i=et();if(W){if(n===void 0)throw Error(E(407));n=n()}else{if(n=t(),ue===null)throw Error(E(349));dn&30||_h(r,t,n)}i.memoizedState=n;var s={value:n,getSnapshot:t};return i.queue=s,Sc(Oh.bind(null,r,s,e),[e]),r.flags|=2048,Xr(9,Ih.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=et(),t=ue.identifierPrefix;if(W){var n=ct,r=ut;n=(r&~(1<<32-Xe(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Qr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=pv++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},xv={readContext:We,useCallback:Hh,useContext:We,useEffect:Ol,useImperativeHandle:bh,useInsertionEffect:Uh,useLayoutEffect:$h,useMemo:Kh,useReducer:Eo,useRef:Bh,useState:function(){return Eo(Yr)},useDebugValue:Fl,useDeferredValue:function(e){var t=be();return Gh(t,ie.memoizedState,e)},useTransition:function(){var e=Eo(Yr)[0],t=be().memoizedState;return[e,t]},useMutableSource:Dh,useSyncExternalStore:Vh,useId:Qh,unstable_isNewReconciler:!1},wv={readContext:We,useCallback:Hh,useContext:We,useEffect:Ol,useImperativeHandle:bh,useInsertionEffect:Uh,useLayoutEffect:$h,useMemo:Kh,useReducer:To,useRef:Bh,useState:function(){return To(Yr)},useDebugValue:Fl,useDeferredValue:function(e){var t=be();return ie===null?t.memoizedState=e:Gh(t,ie.memoizedState,e)},useTransition:function(){var e=To(Yr)[0],t=be().memoizedState;return[e,t]},useMutableSource:Dh,useSyncExternalStore:Vh,useId:Qh,unstable_isNewReconciler:!1};function Ge(e,t){if(e&&e.defaultProps){t=Q({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Sa(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Q({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var $s={isMounted:function(e){return(e=e._reactInternals)?mn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Se(),i=It(e),s=dt(r,i);s.payload=t,n!=null&&(s.callback=n),t=Vt(e,s,i),t!==null&&(Ze(t,e,i,r),Hi(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Se(),i=It(e),s=dt(r,i);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=Vt(e,s,i),t!==null&&(Ze(t,e,i,r),Hi(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Se(),r=It(e),i=dt(n,r);i.tag=2,t!=null&&(i.callback=t),t=Vt(e,i,r),t!==null&&(Ze(t,e,r,n),Hi(t,e,r))}};function kc(e,t,n,r,i,s,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,o):t.prototype&&t.prototype.isPureReactComponent?!$r(n,r)||!$r(i,s):!0}function qh(e,t,n){var r=!1,i=Bt,s=t.contextType;return typeof s=="object"&&s!==null?s=We(s):(i=je(t)?un:xe.current,r=t.contextTypes,s=(r=r!=null)?bn(e,i):Bt),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=$s,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=s),t}function Cc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&$s.enqueueReplaceState(t,t.state,null)}function ka(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Ml(e);var s=t.contextType;typeof s=="object"&&s!==null?i.context=We(s):(s=je(t)?un:xe.current,i.context=bn(e,s)),i.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(Sa(e,t,s,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&$s.enqueueReplaceState(i,i.state,null),gs(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function Qn(e,t){try{var n="",r=t;do n+=Qg(r),r=r.return;while(r);var i=n}catch(s){i=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:i,digest:null}}function jo(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Ca(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Sv=typeof WeakMap=="function"?WeakMap:Map;function Jh(e,t,n){n=dt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ss||(Ss=!0,Da=r),Ca(e,t)},n}function ep(e,t,n){n=dt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){Ca(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){Ca(e,t),typeof r!="function"&&(_t===null?_t=new Set([this]):_t.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function Pc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Sv;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=_v.bind(null,e,t,n),t.then(e,e))}function Ec(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Tc(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=dt(-1,1),t.tag=2,Vt(n,t,1))),n.lanes|=1),e)}var kv=xt.ReactCurrentOwner,Ee=!1;function we(e,t,n,r){t.child=e===null?Ah(t,null,n,r):Kn(t,e.child,n,r)}function jc(e,t,n,r,i){n=n.render;var s=t.ref;return Un(t,i),r=_l(e,t,n,r,s,i),n=Il(),e!==null&&!Ee?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,yt(e,t,i)):(W&&n&&Cl(t),t.flags|=1,we(e,t,r,i),t.child)}function Nc(e,t,n,r,i){if(e===null){var s=n.type;return typeof s=="function"&&!Kl(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,tp(e,t,s,r,i)):(e=Zi(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&i)){var o=s.memoizedProps;if(n=n.compare,n=n!==null?n:$r,n(o,r)&&e.ref===t.ref)return yt(e,t,i)}return t.flags|=1,e=Ot(s,r),e.ref=t.ref,e.return=t,t.child=e}function tp(e,t,n,r,i){if(e!==null){var s=e.memoizedProps;if($r(s,r)&&e.ref===t.ref)if(Ee=!1,t.pendingProps=r=s,(e.lanes&i)!==0)e.flags&131072&&(Ee=!0);else return t.lanes=e.lanes,yt(e,t,i)}return Pa(e,t,n,r,i)}function np(e,t,n){var r=t.pendingProps,i=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},z(Rn,Ae),Ae|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,z(Rn,Ae),Ae|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,z(Rn,Ae),Ae|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,z(Rn,Ae),Ae|=r;return we(e,t,i,n),t.child}function rp(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Pa(e,t,n,r,i){var s=je(n)?un:xe.current;return s=bn(t,s),Un(t,i),n=_l(e,t,n,r,s,i),r=Il(),e!==null&&!Ee?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,yt(e,t,i)):(W&&r&&Cl(t),t.flags|=1,we(e,t,n,i),t.child)}function Ac(e,t,n,r,i){if(je(n)){var s=!0;ds(t)}else s=!1;if(Un(t,i),t.stateNode===null)Qi(e,t),qh(t,n,r),ka(t,n,r,i),r=!0;else if(e===null){var o=t.stateNode,a=t.memoizedProps;o.props=a;var l=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=We(u):(u=je(n)?un:xe.current,u=bn(t,u));var c=n.getDerivedStateFromProps,d=typeof c=="function"||typeof o.getSnapshotBeforeUpdate=="function";d||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==r||l!==u)&&Cc(t,o,r,u),Pt=!1;var h=t.memoizedState;o.state=h,gs(t,r,o,i),l=t.memoizedState,a!==r||h!==l||Te.current||Pt?(typeof c=="function"&&(Sa(t,n,c,r),l=t.memoizedState),(a=Pt||kc(t,n,a,r,h,l,u))?(d||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),o.props=r,o.state=l,o.context=u,r=a):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Rh(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:Ge(t.type,a),o.props=u,d=t.pendingProps,h=o.context,l=n.contextType,typeof l=="object"&&l!==null?l=We(l):(l=je(n)?un:xe.current,l=bn(t,l));var y=n.getDerivedStateFromProps;(c=typeof y=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==d||h!==l)&&Cc(t,o,r,l),Pt=!1,h=t.memoizedState,o.state=h,gs(t,r,o,i);var v=t.memoizedState;a!==d||h!==v||Te.current||Pt?(typeof y=="function"&&(Sa(t,n,y,r),v=t.memoizedState),(u=Pt||kc(t,n,u,r,h,v,l)||!1)?(c||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,v,l),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,v,l)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),o.props=r,o.state=v,o.context=l,r=u):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),r=!1)}return Ea(e,t,n,r,s,i)}function Ea(e,t,n,r,i,s){rp(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return i&&pc(t,n,!1),yt(e,t,s);r=t.stateNode,kv.current=t;var a=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=Kn(t,e.child,null,s),t.child=Kn(t,null,a,s)):we(e,t,a,s),t.memoizedState=r.state,i&&pc(t,n,!0),t.child}function ip(e){var t=e.stateNode;t.pendingContext?hc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&hc(e,t.context,!1),Rl(e,t.containerInfo)}function Mc(e,t,n,r,i){return Hn(),El(i),t.flags|=256,we(e,t,n,r),t.child}var Ta={dehydrated:null,treeContext:null,retryLane:0};function ja(e){return{baseLanes:e,cachePool:null,transitions:null}}function sp(e,t,n){var r=t.pendingProps,i=H.current,s=!1,o=(t.flags&128)!==0,a;if((a=o)||(a=e!==null&&e.memoizedState===null?!1:(i&2)!==0),a?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),z(H,i&1),e===null)return xa(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,s?(r=t.mode,s=t.child,o={mode:"hidden",children:o},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=o):s=Hs(o,r,0,null),e=an(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=ja(n),t.memoizedState=Ta,e):zl(t,o));if(i=e.memoizedState,i!==null&&(a=i.dehydrated,a!==null))return Cv(e,t,o,r,a,i,n);if(s){s=r.fallback,o=t.mode,i=e.child,a=i.sibling;var l={mode:"hidden",children:r.children};return!(o&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=Ot(i,l),r.subtreeFlags=i.subtreeFlags&14680064),a!==null?s=Ot(a,s):(s=an(s,o,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,o=e.child.memoizedState,o=o===null?ja(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},s.memoizedState=o,s.childLanes=e.childLanes&~n,t.memoizedState=Ta,r}return s=e.child,e=s.sibling,r=Ot(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function zl(e,t){return t=Hs({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Li(e,t,n,r){return r!==null&&El(r),Kn(t,e.child,null,n),e=zl(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Cv(e,t,n,r,i,s,o){if(n)return t.flags&256?(t.flags&=-257,r=jo(Error(E(422))),Li(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,i=t.mode,r=Hs({mode:"visible",children:r.children},i,0,null),s=an(s,i,o,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&Kn(t,e.child,null,o),t.child.memoizedState=ja(o),t.memoizedState=Ta,s);if(!(t.mode&1))return Li(e,t,o,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var a=r.dgst;return r=a,s=Error(E(419)),r=jo(s,r,void 0),Li(e,t,o,r)}if(a=(o&e.childLanes)!==0,Ee||a){if(r=ue,r!==null){switch(o&-o){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|o)?0:i,i!==0&&i!==s.retryLane&&(s.retryLane=i,gt(e,i),Ze(r,e,i,-1))}return Hl(),r=jo(Error(E(421))),Li(e,t,o,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=Iv.bind(null,e),i._reactRetry=t,null):(e=s.treeContext,Me=Dt(i.nextSibling),Re=t,W=!0,Ye=null,e!==null&&(ze[Be++]=ut,ze[Be++]=ct,ze[Be++]=cn,ut=e.id,ct=e.overflow,cn=t),t=zl(t,r.children),t.flags|=4096,t)}function Rc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),wa(e.return,t,n)}function No(e,t,n,r,i){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=i)}function op(e,t,n){var r=t.pendingProps,i=r.revealOrder,s=r.tail;if(we(e,t,r.children,n),r=H.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Rc(e,n,t);else if(e.tag===19)Rc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(z(H,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&ys(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),No(t,!1,i,n,s);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&ys(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}No(t,!0,n,null,s);break;case"together":No(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Qi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function yt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),fn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(E(153));if(t.child!==null){for(e=t.child,n=Ot(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Ot(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Pv(e,t,n){switch(t.tag){case 3:ip(t),Hn();break;case 5:Lh(t);break;case 1:je(t.type)&&ds(t);break;case 4:Rl(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;z(ps,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(z(H,H.current&1),t.flags|=128,null):n&t.child.childLanes?sp(e,t,n):(z(H,H.current&1),e=yt(e,t,n),e!==null?e.sibling:null);z(H,H.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return op(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),z(H,H.current),r)break;return null;case 22:case 23:return t.lanes=0,np(e,t,n)}return yt(e,t,n)}var ap,Na,lp,up;ap=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Na=function(){};lp=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,rn(rt.current);var s=null;switch(n){case"input":i=Xo(e,i),r=Xo(e,r),s=[];break;case"select":i=Q({},i,{value:void 0}),r=Q({},r,{value:void 0}),s=[];break;case"textarea":i=Jo(e,i),r=Jo(e,r),s=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=us)}ta(n,r);var o;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var a=i[u];for(o in a)a.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(_r.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var l=r[u];if(a=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(o in a)!a.hasOwnProperty(o)||l&&l.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in l)l.hasOwnProperty(o)&&a[o]!==l[o]&&(n||(n={}),n[o]=l[o])}else n||(s||(s=[]),s.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(s=s||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(s=s||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(_r.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&B("scroll",e),s||a===l||(s=[])):(s=s||[]).push(u,l))}n&&(s=s||[]).push("style",n);var u=s;(t.updateQueue=u)&&(t.flags|=4)}};up=function(e,t,n,r){n!==r&&(t.flags|=4)};function fr(e,t){if(!W)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function me(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Ev(e,t,n){var r=t.pendingProps;switch(Pl(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return me(t),null;case 1:return je(t.type)&&cs(),me(t),null;case 3:return r=t.stateNode,Gn(),U(Te),U(xe),Dl(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Mi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ye!==null&&(Ia(Ye),Ye=null))),Na(e,t),me(t),null;case 5:Ll(t);var i=rn(Gr.current);if(n=t.type,e!==null&&t.stateNode!=null)lp(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(E(166));return me(t),null}if(e=rn(rt.current),Mi(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[tt]=t,r[Hr]=s,e=(t.mode&1)!==0,n){case"dialog":B("cancel",r),B("close",r);break;case"iframe":case"object":case"embed":B("load",r);break;case"video":case"audio":for(i=0;i<vr.length;i++)B(vr[i],r);break;case"source":B("error",r);break;case"img":case"image":case"link":B("error",r),B("load",r);break;case"details":B("toggle",r);break;case"input":Bu(r,s),B("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},B("invalid",r);break;case"textarea":$u(r,s),B("invalid",r)}ta(n,s),i=null;for(var o in s)if(s.hasOwnProperty(o)){var a=s[o];o==="children"?typeof a=="string"?r.textContent!==a&&(s.suppressHydrationWarning!==!0&&Ai(r.textContent,a,e),i=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(s.suppressHydrationWarning!==!0&&Ai(r.textContent,a,e),i=["children",""+a]):_r.hasOwnProperty(o)&&a!=null&&o==="onScroll"&&B("scroll",r)}switch(n){case"input":Si(r),Uu(r,s,!0);break;case"textarea":Si(r),Wu(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=us)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Of(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[tt]=t,e[Hr]=r,ap(e,t,!1,!1),t.stateNode=e;e:{switch(o=na(n,r),n){case"dialog":B("cancel",e),B("close",e),i=r;break;case"iframe":case"object":case"embed":B("load",e),i=r;break;case"video":case"audio":for(i=0;i<vr.length;i++)B(vr[i],e);i=r;break;case"source":B("error",e),i=r;break;case"img":case"image":case"link":B("error",e),B("load",e),i=r;break;case"details":B("toggle",e),i=r;break;case"input":Bu(e,r),i=Xo(e,r),B("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=Q({},r,{value:void 0}),B("invalid",e);break;case"textarea":$u(e,r),i=Jo(e,r),B("invalid",e);break;default:i=r}ta(n,i),a=i;for(s in a)if(a.hasOwnProperty(s)){var l=a[s];s==="style"?Bf(e,l):s==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Ff(e,l)):s==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&Ir(e,l):typeof l=="number"&&Ir(e,""+l):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(_r.hasOwnProperty(s)?l!=null&&s==="onScroll"&&B("scroll",e):l!=null&&ul(e,s,l,o))}switch(n){case"input":Si(e),Uu(e,r,!1);break;case"textarea":Si(e),Wu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+zt(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?On(e,!!r.multiple,s,!1):r.defaultValue!=null&&On(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=us)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return me(t),null;case 6:if(e&&t.stateNode!=null)up(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(E(166));if(n=rn(Gr.current),rn(rt.current),Mi(t)){if(r=t.stateNode,n=t.memoizedProps,r[tt]=t,(s=r.nodeValue!==n)&&(e=Re,e!==null))switch(e.tag){case 3:Ai(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ai(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[tt]=t,t.stateNode=r}return me(t),null;case 13:if(U(H),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(W&&Me!==null&&t.mode&1&&!(t.flags&128))jh(),Hn(),t.flags|=98560,s=!1;else if(s=Mi(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(E(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(E(317));s[tt]=t}else Hn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;me(t),s=!1}else Ye!==null&&(Ia(Ye),Ye=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||H.current&1?se===0&&(se=3):Hl())),t.updateQueue!==null&&(t.flags|=4),me(t),null);case 4:return Gn(),Na(e,t),e===null&&Wr(t.stateNode.containerInfo),me(t),null;case 10:return Nl(t.type._context),me(t),null;case 17:return je(t.type)&&cs(),me(t),null;case 19:if(U(H),s=t.memoizedState,s===null)return me(t),null;if(r=(t.flags&128)!==0,o=s.rendering,o===null)if(r)fr(s,!1);else{if(se!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=ys(e),o!==null){for(t.flags|=128,fr(s,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,o=s.alternate,o===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=o.childLanes,s.lanes=o.lanes,s.child=o.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=o.memoizedProps,s.memoizedState=o.memoizedState,s.updateQueue=o.updateQueue,s.type=o.type,e=o.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return z(H,H.current&1|2),t.child}e=e.sibling}s.tail!==null&&J()>Yn&&(t.flags|=128,r=!0,fr(s,!1),t.lanes=4194304)}else{if(!r)if(e=ys(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),fr(s,!0),s.tail===null&&s.tailMode==="hidden"&&!o.alternate&&!W)return me(t),null}else 2*J()-s.renderingStartTime>Yn&&n!==1073741824&&(t.flags|=128,r=!0,fr(s,!1),t.lanes=4194304);s.isBackwards?(o.sibling=t.child,t.child=o):(n=s.last,n!==null?n.sibling=o:t.child=o,s.last=o)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=J(),t.sibling=null,n=H.current,z(H,r?n&1|2:n&1),t):(me(t),null);case 22:case 23:return bl(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ae&1073741824&&(me(t),t.subtreeFlags&6&&(t.flags|=8192)):me(t),null;case 24:return null;case 25:return null}throw Error(E(156,t.tag))}function Tv(e,t){switch(Pl(t),t.tag){case 1:return je(t.type)&&cs(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Gn(),U(Te),U(xe),Dl(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ll(t),null;case 13:if(U(H),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(E(340));Hn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return U(H),null;case 4:return Gn(),null;case 10:return Nl(t.type._context),null;case 22:case 23:return bl(),null;case 24:return null;default:return null}}var Di=!1,ye=!1,jv=typeof WeakSet=="function"?WeakSet:Set,A=null;function Mn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){X(e,t,r)}else n.current=null}function Aa(e,t,n){try{n()}catch(r){X(e,t,r)}}var Lc=!1;function Nv(e,t){if(fa=os,e=ph(),kl(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var o=0,a=-1,l=-1,u=0,c=0,d=e,h=null;t:for(;;){for(var y;d!==n||i!==0&&d.nodeType!==3||(a=o+i),d!==s||r!==0&&d.nodeType!==3||(l=o+r),d.nodeType===3&&(o+=d.nodeValue.length),(y=d.firstChild)!==null;)h=d,d=y;for(;;){if(d===e)break t;if(h===n&&++u===i&&(a=o),h===s&&++c===r&&(l=o),(y=d.nextSibling)!==null)break;d=h,h=d.parentNode}d=y}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(ha={focusedElem:e,selectionRange:n},os=!1,A=t;A!==null;)if(t=A,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,A=e;else for(;A!==null;){t=A;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var x=v.memoizedProps,C=v.memoizedState,p=t.stateNode,m=p.getSnapshotBeforeUpdate(t.elementType===t.type?x:Ge(t.type,x),C);p.__reactInternalSnapshotBeforeUpdate=m}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(E(163))}}catch(w){X(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,A=e;break}A=t.return}return v=Lc,Lc=!1,v}function Nr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var s=i.destroy;i.destroy=void 0,s!==void 0&&Aa(t,n,s)}i=i.next}while(i!==r)}}function Ws(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Ma(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function cp(e){var t=e.alternate;t!==null&&(e.alternate=null,cp(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[tt],delete t[Hr],delete t[ga],delete t[cv],delete t[dv])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function dp(e){return e.tag===5||e.tag===3||e.tag===4}function Dc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||dp(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ra(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=us));else if(r!==4&&(e=e.child,e!==null))for(Ra(e,t,n),e=e.sibling;e!==null;)Ra(e,t,n),e=e.sibling}function La(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(La(e,t,n),e=e.sibling;e!==null;)La(e,t,n),e=e.sibling}var ce=null,Qe=!1;function St(e,t,n){for(n=n.child;n!==null;)fp(e,t,n),n=n.sibling}function fp(e,t,n){if(nt&&typeof nt.onCommitFiberUnmount=="function")try{nt.onCommitFiberUnmount(_s,n)}catch{}switch(n.tag){case 5:ye||Mn(n,t);case 6:var r=ce,i=Qe;ce=null,St(e,t,n),ce=r,Qe=i,ce!==null&&(Qe?(e=ce,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ce.removeChild(n.stateNode));break;case 18:ce!==null&&(Qe?(e=ce,n=n.stateNode,e.nodeType===8?So(e.parentNode,n):e.nodeType===1&&So(e,n),Br(e)):So(ce,n.stateNode));break;case 4:r=ce,i=Qe,ce=n.stateNode.containerInfo,Qe=!0,St(e,t,n),ce=r,Qe=i;break;case 0:case 11:case 14:case 15:if(!ye&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var s=i,o=s.destroy;s=s.tag,o!==void 0&&(s&2||s&4)&&Aa(n,t,o),i=i.next}while(i!==r)}St(e,t,n);break;case 1:if(!ye&&(Mn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){X(n,t,a)}St(e,t,n);break;case 21:St(e,t,n);break;case 22:n.mode&1?(ye=(r=ye)||n.memoizedState!==null,St(e,t,n),ye=r):St(e,t,n);break;default:St(e,t,n)}}function Vc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new jv),t.forEach(function(r){var i=Ov.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function He(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var s=e,o=t,a=o;e:for(;a!==null;){switch(a.tag){case 5:ce=a.stateNode,Qe=!1;break e;case 3:ce=a.stateNode.containerInfo,Qe=!0;break e;case 4:ce=a.stateNode.containerInfo,Qe=!0;break e}a=a.return}if(ce===null)throw Error(E(160));fp(s,o,i),ce=null,Qe=!1;var l=i.alternate;l!==null&&(l.return=null),i.return=null}catch(u){X(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)hp(t,e),t=t.sibling}function hp(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(He(t,e),Je(e),r&4){try{Nr(3,e,e.return),Ws(3,e)}catch(x){X(e,e.return,x)}try{Nr(5,e,e.return)}catch(x){X(e,e.return,x)}}break;case 1:He(t,e),Je(e),r&512&&n!==null&&Mn(n,n.return);break;case 5:if(He(t,e),Je(e),r&512&&n!==null&&Mn(n,n.return),e.flags&32){var i=e.stateNode;try{Ir(i,"")}catch(x){X(e,e.return,x)}}if(r&4&&(i=e.stateNode,i!=null)){var s=e.memoizedProps,o=n!==null?n.memoizedProps:s,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&s.type==="radio"&&s.name!=null&&_f(i,s),na(a,o);var u=na(a,s);for(o=0;o<l.length;o+=2){var c=l[o],d=l[o+1];c==="style"?Bf(i,d):c==="dangerouslySetInnerHTML"?Ff(i,d):c==="children"?Ir(i,d):ul(i,c,d,u)}switch(a){case"input":Zo(i,s);break;case"textarea":If(i,s);break;case"select":var h=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!s.multiple;var y=s.value;y!=null?On(i,!!s.multiple,y,!1):h!==!!s.multiple&&(s.defaultValue!=null?On(i,!!s.multiple,s.defaultValue,!0):On(i,!!s.multiple,s.multiple?[]:"",!1))}i[Hr]=s}catch(x){X(e,e.return,x)}}break;case 6:if(He(t,e),Je(e),r&4){if(e.stateNode===null)throw Error(E(162));i=e.stateNode,s=e.memoizedProps;try{i.nodeValue=s}catch(x){X(e,e.return,x)}}break;case 3:if(He(t,e),Je(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Br(t.containerInfo)}catch(x){X(e,e.return,x)}break;case 4:He(t,e),Je(e);break;case 13:He(t,e),Je(e),i=e.child,i.flags&8192&&(s=i.memoizedState!==null,i.stateNode.isHidden=s,!s||i.alternate!==null&&i.alternate.memoizedState!==null||($l=J())),r&4&&Vc(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(ye=(u=ye)||c,He(t,e),ye=u):He(t,e),Je(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(A=e,c=e.child;c!==null;){for(d=A=c;A!==null;){switch(h=A,y=h.child,h.tag){case 0:case 11:case 14:case 15:Nr(4,h,h.return);break;case 1:Mn(h,h.return);var v=h.stateNode;if(typeof v.componentWillUnmount=="function"){r=h,n=h.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(x){X(r,n,x)}}break;case 5:Mn(h,h.return);break;case 22:if(h.memoizedState!==null){Ic(d);continue}}y!==null?(y.return=h,A=y):Ic(d)}c=c.sibling}e:for(c=null,d=e;;){if(d.tag===5){if(c===null){c=d;try{i=d.stateNode,u?(s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(a=d.stateNode,l=d.memoizedProps.style,o=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=zf("display",o))}catch(x){X(e,e.return,x)}}}else if(d.tag===6){if(c===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(x){X(e,e.return,x)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;c===d&&(c=null),d=d.return}c===d&&(c=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:He(t,e),Je(e),r&4&&Vc(e);break;case 21:break;default:He(t,e),Je(e)}}function Je(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(dp(n)){var r=n;break e}n=n.return}throw Error(E(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Ir(i,""),r.flags&=-33);var s=Dc(e);La(e,s,i);break;case 3:case 4:var o=r.stateNode.containerInfo,a=Dc(e);Ra(e,a,o);break;default:throw Error(E(161))}}catch(l){X(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Av(e,t,n){A=e,pp(e)}function pp(e,t,n){for(var r=(e.mode&1)!==0;A!==null;){var i=A,s=i.child;if(i.tag===22&&r){var o=i.memoizedState!==null||Di;if(!o){var a=i.alternate,l=a!==null&&a.memoizedState!==null||ye;a=Di;var u=ye;if(Di=o,(ye=l)&&!u)for(A=i;A!==null;)o=A,l=o.child,o.tag===22&&o.memoizedState!==null?Oc(i):l!==null?(l.return=o,A=l):Oc(i);for(;s!==null;)A=s,pp(s),s=s.sibling;A=i,Di=a,ye=u}_c(e)}else i.subtreeFlags&8772&&s!==null?(s.return=i,A=s):_c(e)}}function _c(e){for(;A!==null;){var t=A;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ye||Ws(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!ye)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:Ge(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&xc(t,s,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}xc(t,o,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var d=c.dehydrated;d!==null&&Br(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(E(163))}ye||t.flags&512&&Ma(t)}catch(h){X(t,t.return,h)}}if(t===e){A=null;break}if(n=t.sibling,n!==null){n.return=t.return,A=n;break}A=t.return}}function Ic(e){for(;A!==null;){var t=A;if(t===e){A=null;break}var n=t.sibling;if(n!==null){n.return=t.return,A=n;break}A=t.return}}function Oc(e){for(;A!==null;){var t=A;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ws(4,t)}catch(l){X(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(l){X(t,i,l)}}var s=t.return;try{Ma(t)}catch(l){X(t,s,l)}break;case 5:var o=t.return;try{Ma(t)}catch(l){X(t,o,l)}}}catch(l){X(t,t.return,l)}if(t===e){A=null;break}var a=t.sibling;if(a!==null){a.return=t.return,A=a;break}A=t.return}}var Mv=Math.ceil,ws=xt.ReactCurrentDispatcher,Bl=xt.ReactCurrentOwner,$e=xt.ReactCurrentBatchConfig,I=0,ue=null,te=null,fe=0,Ae=0,Rn=bt(0),se=0,Zr=null,fn=0,bs=0,Ul=0,Ar=null,Pe=null,$l=0,Yn=1/0,at=null,Ss=!1,Da=null,_t=null,Vi=!1,Nt=null,ks=0,Mr=0,Va=null,Yi=-1,Xi=0;function Se(){return I&6?J():Yi!==-1?Yi:Yi=J()}function It(e){return e.mode&1?I&2&&fe!==0?fe&-fe:hv.transition!==null?(Xi===0&&(Xi=qf()),Xi):(e=O,e!==0||(e=window.event,e=e===void 0?16:sh(e.type)),e):1}function Ze(e,t,n,r){if(50<Mr)throw Mr=0,Va=null,Error(E(185));oi(e,n,r),(!(I&2)||e!==ue)&&(e===ue&&(!(I&2)&&(bs|=n),se===4&&Tt(e,fe)),Ne(e,r),n===1&&I===0&&!(t.mode&1)&&(Yn=J()+500,Bs&&Ht()))}function Ne(e,t){var n=e.callbackNode;hy(e,t);var r=ss(e,e===ue?fe:0);if(r===0)n!==null&&Ku(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Ku(n),t===1)e.tag===0?fv(Fc.bind(null,e)):Ph(Fc.bind(null,e)),lv(function(){!(I&6)&&Ht()}),n=null;else{switch(Jf(r)){case 1:n=pl;break;case 4:n=Xf;break;case 16:n=is;break;case 536870912:n=Zf;break;default:n=is}n=kp(n,mp.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function mp(e,t){if(Yi=-1,Xi=0,I&6)throw Error(E(327));var n=e.callbackNode;if($n()&&e.callbackNode!==n)return null;var r=ss(e,e===ue?fe:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Cs(e,r);else{t=r;var i=I;I|=2;var s=yp();(ue!==e||fe!==t)&&(at=null,Yn=J()+500,on(e,t));do try{Dv();break}catch(a){gp(e,a)}while(!0);jl(),ws.current=s,I=i,te!==null?t=0:(ue=null,fe=0,t=se)}if(t!==0){if(t===2&&(i=aa(e),i!==0&&(r=i,t=_a(e,i))),t===1)throw n=Zr,on(e,0),Tt(e,r),Ne(e,J()),n;if(t===6)Tt(e,r);else{if(i=e.current.alternate,!(r&30)&&!Rv(i)&&(t=Cs(e,r),t===2&&(s=aa(e),s!==0&&(r=s,t=_a(e,s))),t===1))throw n=Zr,on(e,0),Tt(e,r),Ne(e,J()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(E(345));case 2:qt(e,Pe,at);break;case 3:if(Tt(e,r),(r&130023424)===r&&(t=$l+500-J(),10<t)){if(ss(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){Se(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=ma(qt.bind(null,e,Pe,at),t);break}qt(e,Pe,at);break;case 4:if(Tt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var o=31-Xe(r);s=1<<o,o=t[o],o>i&&(i=o),r&=~s}if(r=i,r=J()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Mv(r/1960))-r,10<r){e.timeoutHandle=ma(qt.bind(null,e,Pe,at),r);break}qt(e,Pe,at);break;case 5:qt(e,Pe,at);break;default:throw Error(E(329))}}}return Ne(e,J()),e.callbackNode===n?mp.bind(null,e):null}function _a(e,t){var n=Ar;return e.current.memoizedState.isDehydrated&&(on(e,t).flags|=256),e=Cs(e,t),e!==2&&(t=Pe,Pe=n,t!==null&&Ia(t)),e}function Ia(e){Pe===null?Pe=e:Pe.push.apply(Pe,e)}function Rv(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],s=i.getSnapshot;i=i.value;try{if(!qe(s(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Tt(e,t){for(t&=~Ul,t&=~bs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Xe(t),r=1<<n;e[n]=-1,t&=~r}}function Fc(e){if(I&6)throw Error(E(327));$n();var t=ss(e,0);if(!(t&1))return Ne(e,J()),null;var n=Cs(e,t);if(e.tag!==0&&n===2){var r=aa(e);r!==0&&(t=r,n=_a(e,r))}if(n===1)throw n=Zr,on(e,0),Tt(e,t),Ne(e,J()),n;if(n===6)throw Error(E(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,qt(e,Pe,at),Ne(e,J()),null}function Wl(e,t){var n=I;I|=1;try{return e(t)}finally{I=n,I===0&&(Yn=J()+500,Bs&&Ht())}}function hn(e){Nt!==null&&Nt.tag===0&&!(I&6)&&$n();var t=I;I|=1;var n=$e.transition,r=O;try{if($e.transition=null,O=1,e)return e()}finally{O=r,$e.transition=n,I=t,!(I&6)&&Ht()}}function bl(){Ae=Rn.current,U(Rn)}function on(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,av(n)),te!==null)for(n=te.return;n!==null;){var r=n;switch(Pl(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&cs();break;case 3:Gn(),U(Te),U(xe),Dl();break;case 5:Ll(r);break;case 4:Gn();break;case 13:U(H);break;case 19:U(H);break;case 10:Nl(r.type._context);break;case 22:case 23:bl()}n=n.return}if(ue=e,te=e=Ot(e.current,null),fe=Ae=t,se=0,Zr=null,Ul=bs=fn=0,Pe=Ar=null,nn!==null){for(t=0;t<nn.length;t++)if(n=nn[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,s=n.pending;if(s!==null){var o=s.next;s.next=i,r.next=o}n.pending=r}nn=null}return e}function gp(e,t){do{var n=te;try{if(jl(),Ki.current=xs,vs){for(var r=G.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}vs=!1}if(dn=0,le=ie=G=null,jr=!1,Qr=0,Bl.current=null,n===null||n.return===null){se=1,Zr=t,te=null;break}e:{var s=e,o=n.return,a=n,l=t;if(t=fe,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,c=a,d=c.tag;if(!(c.mode&1)&&(d===0||d===11||d===15)){var h=c.alternate;h?(c.updateQueue=h.updateQueue,c.memoizedState=h.memoizedState,c.lanes=h.lanes):(c.updateQueue=null,c.memoizedState=null)}var y=Ec(o);if(y!==null){y.flags&=-257,Tc(y,o,a,s,t),y.mode&1&&Pc(s,u,t),t=y,l=u;var v=t.updateQueue;if(v===null){var x=new Set;x.add(l),t.updateQueue=x}else v.add(l);break e}else{if(!(t&1)){Pc(s,u,t),Hl();break e}l=Error(E(426))}}else if(W&&a.mode&1){var C=Ec(o);if(C!==null){!(C.flags&65536)&&(C.flags|=256),Tc(C,o,a,s,t),El(Qn(l,a));break e}}s=l=Qn(l,a),se!==4&&(se=2),Ar===null?Ar=[s]:Ar.push(s),s=o;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var p=Jh(s,l,t);vc(s,p);break e;case 1:a=l;var m=s.type,g=s.stateNode;if(!(s.flags&128)&&(typeof m.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(_t===null||!_t.has(g)))){s.flags|=65536,t&=-t,s.lanes|=t;var w=ep(s,a,t);vc(s,w);break e}}s=s.return}while(s!==null)}xp(n)}catch(k){t=k,te===n&&n!==null&&(te=n=n.return);continue}break}while(!0)}function yp(){var e=ws.current;return ws.current=xs,e===null?xs:e}function Hl(){(se===0||se===3||se===2)&&(se=4),ue===null||!(fn&268435455)&&!(bs&268435455)||Tt(ue,fe)}function Cs(e,t){var n=I;I|=2;var r=yp();(ue!==e||fe!==t)&&(at=null,on(e,t));do try{Lv();break}catch(i){gp(e,i)}while(!0);if(jl(),I=n,ws.current=r,te!==null)throw Error(E(261));return ue=null,fe=0,se}function Lv(){for(;te!==null;)vp(te)}function Dv(){for(;te!==null&&!iy();)vp(te)}function vp(e){var t=Sp(e.alternate,e,Ae);e.memoizedProps=e.pendingProps,t===null?xp(e):te=t,Bl.current=null}function xp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Tv(n,t),n!==null){n.flags&=32767,te=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{se=6,te=null;return}}else if(n=Ev(n,t,Ae),n!==null){te=n;return}if(t=t.sibling,t!==null){te=t;return}te=t=e}while(t!==null);se===0&&(se=5)}function qt(e,t,n){var r=O,i=$e.transition;try{$e.transition=null,O=1,Vv(e,t,n,r)}finally{$e.transition=i,O=r}return null}function Vv(e,t,n,r){do $n();while(Nt!==null);if(I&6)throw Error(E(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(E(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(py(e,s),e===ue&&(te=ue=null,fe=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Vi||(Vi=!0,kp(is,function(){return $n(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=$e.transition,$e.transition=null;var o=O;O=1;var a=I;I|=4,Bl.current=null,Nv(e,n),hp(n,e),ev(ha),os=!!fa,ha=fa=null,e.current=n,Av(n),sy(),I=a,O=o,$e.transition=s}else e.current=n;if(Vi&&(Vi=!1,Nt=e,ks=i),s=e.pendingLanes,s===0&&(_t=null),ly(n.stateNode),Ne(e,J()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Ss)throw Ss=!1,e=Da,Da=null,e;return ks&1&&e.tag!==0&&$n(),s=e.pendingLanes,s&1?e===Va?Mr++:(Mr=0,Va=e):Mr=0,Ht(),null}function $n(){if(Nt!==null){var e=Jf(ks),t=$e.transition,n=O;try{if($e.transition=null,O=16>e?16:e,Nt===null)var r=!1;else{if(e=Nt,Nt=null,ks=0,I&6)throw Error(E(331));var i=I;for(I|=4,A=e.current;A!==null;){var s=A,o=s.child;if(A.flags&16){var a=s.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(A=u;A!==null;){var c=A;switch(c.tag){case 0:case 11:case 15:Nr(8,c,s)}var d=c.child;if(d!==null)d.return=c,A=d;else for(;A!==null;){c=A;var h=c.sibling,y=c.return;if(cp(c),c===u){A=null;break}if(h!==null){h.return=y,A=h;break}A=y}}}var v=s.alternate;if(v!==null){var x=v.child;if(x!==null){v.child=null;do{var C=x.sibling;x.sibling=null,x=C}while(x!==null)}}A=s}}if(s.subtreeFlags&2064&&o!==null)o.return=s,A=o;else e:for(;A!==null;){if(s=A,s.flags&2048)switch(s.tag){case 0:case 11:case 15:Nr(9,s,s.return)}var p=s.sibling;if(p!==null){p.return=s.return,A=p;break e}A=s.return}}var m=e.current;for(A=m;A!==null;){o=A;var g=o.child;if(o.subtreeFlags&2064&&g!==null)g.return=o,A=g;else e:for(o=m;A!==null;){if(a=A,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Ws(9,a)}}catch(k){X(a,a.return,k)}if(a===o){A=null;break e}var w=a.sibling;if(w!==null){w.return=a.return,A=w;break e}A=a.return}}if(I=i,Ht(),nt&&typeof nt.onPostCommitFiberRoot=="function")try{nt.onPostCommitFiberRoot(_s,e)}catch{}r=!0}return r}finally{O=n,$e.transition=t}}return!1}function zc(e,t,n){t=Qn(n,t),t=Jh(e,t,1),e=Vt(e,t,1),t=Se(),e!==null&&(oi(e,1,t),Ne(e,t))}function X(e,t,n){if(e.tag===3)zc(e,e,n);else for(;t!==null;){if(t.tag===3){zc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(_t===null||!_t.has(r))){e=Qn(n,e),e=ep(t,e,1),t=Vt(t,e,1),e=Se(),t!==null&&(oi(t,1,e),Ne(t,e));break}}t=t.return}}function _v(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Se(),e.pingedLanes|=e.suspendedLanes&n,ue===e&&(fe&n)===n&&(se===4||se===3&&(fe&130023424)===fe&&500>J()-$l?on(e,0):Ul|=n),Ne(e,t)}function wp(e,t){t===0&&(e.mode&1?(t=Pi,Pi<<=1,!(Pi&130023424)&&(Pi=4194304)):t=1);var n=Se();e=gt(e,t),e!==null&&(oi(e,t,n),Ne(e,n))}function Iv(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),wp(e,n)}function Ov(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(E(314))}r!==null&&r.delete(t),wp(e,n)}var Sp;Sp=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Te.current)Ee=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ee=!1,Pv(e,t,n);Ee=!!(e.flags&131072)}else Ee=!1,W&&t.flags&1048576&&Eh(t,hs,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Qi(e,t),e=t.pendingProps;var i=bn(t,xe.current);Un(t,n),i=_l(null,t,r,e,i,n);var s=Il();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,je(r)?(s=!0,ds(t)):s=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Ml(t),i.updater=$s,t.stateNode=i,i._reactInternals=t,ka(t,r,e,n),t=Ea(null,t,r,!0,s,n)):(t.tag=0,W&&s&&Cl(t),we(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Qi(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=zv(r),e=Ge(r,e),i){case 0:t=Pa(null,t,r,e,n);break e;case 1:t=Ac(null,t,r,e,n);break e;case 11:t=jc(null,t,r,e,n);break e;case 14:t=Nc(null,t,r,Ge(r.type,e),n);break e}throw Error(E(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ge(r,i),Pa(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ge(r,i),Ac(e,t,r,i,n);case 3:e:{if(ip(t),e===null)throw Error(E(387));r=t.pendingProps,s=t.memoizedState,i=s.element,Rh(e,t),gs(t,r,null,n);var o=t.memoizedState;if(r=o.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){i=Qn(Error(E(423)),t),t=Mc(e,t,r,n,i);break e}else if(r!==i){i=Qn(Error(E(424)),t),t=Mc(e,t,r,n,i);break e}else for(Me=Dt(t.stateNode.containerInfo.firstChild),Re=t,W=!0,Ye=null,n=Ah(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Hn(),r===i){t=yt(e,t,n);break e}we(e,t,r,n)}t=t.child}return t;case 5:return Lh(t),e===null&&xa(t),r=t.type,i=t.pendingProps,s=e!==null?e.memoizedProps:null,o=i.children,pa(r,i)?o=null:s!==null&&pa(r,s)&&(t.flags|=32),rp(e,t),we(e,t,o,n),t.child;case 6:return e===null&&xa(t),null;case 13:return sp(e,t,n);case 4:return Rl(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Kn(t,null,r,n):we(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ge(r,i),jc(e,t,r,i,n);case 7:return we(e,t,t.pendingProps,n),t.child;case 8:return we(e,t,t.pendingProps.children,n),t.child;case 12:return we(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,s=t.memoizedProps,o=i.value,z(ps,r._currentValue),r._currentValue=o,s!==null)if(qe(s.value,o)){if(s.children===i.children&&!Te.current){t=yt(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var a=s.dependencies;if(a!==null){o=s.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(s.tag===1){l=dt(-1,n&-n),l.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?l.next=l:(l.next=c.next,c.next=l),u.pending=l}}s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),wa(s.return,n,t),a.lanes|=n;break}l=l.next}}else if(s.tag===10)o=s.type===t.type?null:s.child;else if(s.tag===18){if(o=s.return,o===null)throw Error(E(341));o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),wa(o,n,t),o=s.sibling}else o=s.child;if(o!==null)o.return=s;else for(o=s;o!==null;){if(o===t){o=null;break}if(s=o.sibling,s!==null){s.return=o.return,o=s;break}o=o.return}s=o}we(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Un(t,n),i=We(i),r=r(i),t.flags|=1,we(e,t,r,n),t.child;case 14:return r=t.type,i=Ge(r,t.pendingProps),i=Ge(r.type,i),Nc(e,t,r,i,n);case 15:return tp(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ge(r,i),Qi(e,t),t.tag=1,je(r)?(e=!0,ds(t)):e=!1,Un(t,n),qh(t,r,i),ka(t,r,i,n),Ea(null,t,r,!0,e,n);case 19:return op(e,t,n);case 22:return np(e,t,n)}throw Error(E(156,t.tag))};function kp(e,t){return Yf(e,t)}function Fv(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ue(e,t,n,r){return new Fv(e,t,n,r)}function Kl(e){return e=e.prototype,!(!e||!e.isReactComponent)}function zv(e){if(typeof e=="function")return Kl(e)?1:0;if(e!=null){if(e=e.$$typeof,e===dl)return 11;if(e===fl)return 14}return 2}function Ot(e,t){var n=e.alternate;return n===null?(n=Ue(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Zi(e,t,n,r,i,s){var o=2;if(r=e,typeof e=="function")Kl(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case Sn:return an(n.children,i,s,t);case cl:o=8,i|=8;break;case Ko:return e=Ue(12,n,t,i|2),e.elementType=Ko,e.lanes=s,e;case Go:return e=Ue(13,n,t,i),e.elementType=Go,e.lanes=s,e;case Qo:return e=Ue(19,n,t,i),e.elementType=Qo,e.lanes=s,e;case Lf:return Hs(n,i,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Mf:o=10;break e;case Rf:o=9;break e;case dl:o=11;break e;case fl:o=14;break e;case Ct:o=16,r=null;break e}throw Error(E(130,e==null?e:typeof e,""))}return t=Ue(o,n,t,i),t.elementType=e,t.type=r,t.lanes=s,t}function an(e,t,n,r){return e=Ue(7,e,r,t),e.lanes=n,e}function Hs(e,t,n,r){return e=Ue(22,e,r,t),e.elementType=Lf,e.lanes=n,e.stateNode={isHidden:!1},e}function Ao(e,t,n){return e=Ue(6,e,null,t),e.lanes=n,e}function Mo(e,t,n){return t=Ue(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Bv(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=uo(0),this.expirationTimes=uo(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=uo(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Gl(e,t,n,r,i,s,o,a,l){return e=new Bv(e,t,n,a,l),t===1?(t=1,s===!0&&(t|=8)):t=0,s=Ue(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ml(s),e}function Uv(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:wn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Cp(e){if(!e)return Bt;e=e._reactInternals;e:{if(mn(e)!==e||e.tag!==1)throw Error(E(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(je(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(E(171))}if(e.tag===1){var n=e.type;if(je(n))return Ch(e,n,t)}return t}function Pp(e,t,n,r,i,s,o,a,l){return e=Gl(n,r,!0,e,i,s,o,a,l),e.context=Cp(null),n=e.current,r=Se(),i=It(n),s=dt(r,i),s.callback=t??null,Vt(n,s,i),e.current.lanes=i,oi(e,i,r),Ne(e,r),e}function Ks(e,t,n,r){var i=t.current,s=Se(),o=It(i);return n=Cp(n),t.context===null?t.context=n:t.pendingContext=n,t=dt(s,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Vt(i,t,o),e!==null&&(Ze(e,i,o,s),Hi(e,i,o)),o}function Ps(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Bc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Ql(e,t){Bc(e,t),(e=e.alternate)&&Bc(e,t)}function $v(){return null}var Ep=typeof reportError=="function"?reportError:function(e){console.error(e)};function Yl(e){this._internalRoot=e}Gs.prototype.render=Yl.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(E(409));Ks(e,t,null,null)};Gs.prototype.unmount=Yl.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;hn(function(){Ks(null,e,null,null)}),t[mt]=null}};function Gs(e){this._internalRoot=e}Gs.prototype.unstable_scheduleHydration=function(e){if(e){var t=nh();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Et.length&&t!==0&&t<Et[n].priority;n++);Et.splice(n,0,e),n===0&&ih(e)}};function Xl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Qs(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Uc(){}function Wv(e,t,n,r,i){if(i){if(typeof r=="function"){var s=r;r=function(){var u=Ps(o);s.call(u)}}var o=Pp(t,r,e,0,null,!1,!1,"",Uc);return e._reactRootContainer=o,e[mt]=o.current,Wr(e.nodeType===8?e.parentNode:e),hn(),o}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var a=r;r=function(){var u=Ps(l);a.call(u)}}var l=Gl(e,0,!1,null,null,!1,!1,"",Uc);return e._reactRootContainer=l,e[mt]=l.current,Wr(e.nodeType===8?e.parentNode:e),hn(function(){Ks(t,l,n,r)}),l}function Ys(e,t,n,r,i){var s=n._reactRootContainer;if(s){var o=s;if(typeof i=="function"){var a=i;i=function(){var l=Ps(o);a.call(l)}}Ks(t,o,e,i)}else o=Wv(n,t,e,i,r);return Ps(o)}eh=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=yr(t.pendingLanes);n!==0&&(ml(t,n|1),Ne(t,J()),!(I&6)&&(Yn=J()+500,Ht()))}break;case 13:hn(function(){var r=gt(e,1);if(r!==null){var i=Se();Ze(r,e,1,i)}}),Ql(e,1)}};gl=function(e){if(e.tag===13){var t=gt(e,134217728);if(t!==null){var n=Se();Ze(t,e,134217728,n)}Ql(e,134217728)}};th=function(e){if(e.tag===13){var t=It(e),n=gt(e,t);if(n!==null){var r=Se();Ze(n,e,t,r)}Ql(e,t)}};nh=function(){return O};rh=function(e,t){var n=O;try{return O=e,t()}finally{O=n}};ia=function(e,t,n){switch(t){case"input":if(Zo(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=zs(r);if(!i)throw Error(E(90));Vf(r),Zo(r,i)}}}break;case"textarea":If(e,n);break;case"select":t=n.value,t!=null&&On(e,!!n.multiple,t,!1)}};Wf=Wl;bf=hn;var bv={usingClientEntryPoint:!1,Events:[li,En,zs,Uf,$f,Wl]},hr={findFiberByHostInstance:tn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Hv={bundleType:hr.bundleType,version:hr.version,rendererPackageName:hr.rendererPackageName,rendererConfig:hr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:xt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Gf(e),e===null?null:e.stateNode},findFiberByHostInstance:hr.findFiberByHostInstance||$v,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var _i=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!_i.isDisabled&&_i.supportsFiber)try{_s=_i.inject(Hv),nt=_i}catch{}}_e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=bv;_e.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Xl(t))throw Error(E(200));return Uv(e,t,null,n)};_e.createRoot=function(e,t){if(!Xl(e))throw Error(E(299));var n=!1,r="",i=Ep;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=Gl(e,1,!1,null,null,n,!1,r,i),e[mt]=t.current,Wr(e.nodeType===8?e.parentNode:e),new Yl(t)};_e.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(E(188)):(e=Object.keys(e).join(","),Error(E(268,e)));return e=Gf(t),e=e===null?null:e.stateNode,e};_e.flushSync=function(e){return hn(e)};_e.hydrate=function(e,t,n){if(!Qs(t))throw Error(E(200));return Ys(null,e,t,!0,n)};_e.hydrateRoot=function(e,t,n){if(!Xl(e))throw Error(E(405));var r=n!=null&&n.hydratedSources||null,i=!1,s="",o=Ep;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=Pp(t,null,e,1,n??null,i,!1,s,o),e[mt]=t.current,Wr(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Gs(t)};_e.render=function(e,t,n){if(!Qs(t))throw Error(E(200));return Ys(null,e,t,!1,n)};_e.unmountComponentAtNode=function(e){if(!Qs(e))throw Error(E(40));return e._reactRootContainer?(hn(function(){Ys(null,null,e,!1,function(){e._reactRootContainer=null,e[mt]=null})}),!0):!1};_e.unstable_batchedUpdates=Wl;_e.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Qs(n))throw Error(E(200));if(e==null||e._reactInternals===void 0)throw Error(E(38));return Ys(e,t,n,!1,r)};_e.version="18.3.1-next-f1338f8080-20240426";function Tp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Tp)}catch(e){console.error(e)}}Tp(),Tf.exports=_e;var Kv=Tf.exports,jp,$c=Kv;jp=$c.createRoot,$c.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function qr(){return qr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},qr.apply(this,arguments)}var At;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(At||(At={}));const Wc="popstate";function Gv(e){e===void 0&&(e={});function t(r,i){let{pathname:s,search:o,hash:a}=r.location;return Oa("",{pathname:s,search:o,hash:a},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(r,i){return typeof i=="string"?i:Es(i)}return Yv(t,n,null,e)}function ne(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Np(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Qv(){return Math.random().toString(36).substr(2,8)}function bc(e,t){return{usr:e.state,key:e.key,idx:t}}function Oa(e,t,n,r){return n===void 0&&(n=null),qr({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?nr(t):t,{state:n,key:t&&t.key||r||Qv()})}function Es(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function nr(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function Yv(e,t,n,r){r===void 0&&(r={});let{window:i=document.defaultView,v5Compat:s=!1}=r,o=i.history,a=At.Pop,l=null,u=c();u==null&&(u=0,o.replaceState(qr({},o.state,{idx:u}),""));function c(){return(o.state||{idx:null}).idx}function d(){a=At.Pop;let C=c(),p=C==null?null:C-u;u=C,l&&l({action:a,location:x.location,delta:p})}function h(C,p){a=At.Push;let m=Oa(x.location,C,p);u=c()+1;let g=bc(m,u),w=x.createHref(m);try{o.pushState(g,"",w)}catch(k){if(k instanceof DOMException&&k.name==="DataCloneError")throw k;i.location.assign(w)}s&&l&&l({action:a,location:x.location,delta:1})}function y(C,p){a=At.Replace;let m=Oa(x.location,C,p);u=c();let g=bc(m,u),w=x.createHref(m);o.replaceState(g,"",w),s&&l&&l({action:a,location:x.location,delta:0})}function v(C){let p=i.location.origin!=="null"?i.location.origin:i.location.href,m=typeof C=="string"?C:Es(C);return m=m.replace(/ $/,"%20"),ne(p,"No window.location.(origin|href) available to create URL for href: "+m),new URL(m,p)}let x={get action(){return a},get location(){return e(i,o)},listen(C){if(l)throw new Error("A history only accepts one active listener");return i.addEventListener(Wc,d),l=C,()=>{i.removeEventListener(Wc,d),l=null}},createHref(C){return t(i,C)},createURL:v,encodeLocation(C){let p=v(C);return{pathname:p.pathname,search:p.search,hash:p.hash}},push:h,replace:y,go(C){return o.go(C)}};return x}var Hc;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Hc||(Hc={}));function Xv(e,t,n){return n===void 0&&(n="/"),Zv(e,t,n,!1)}function Zv(e,t,n,r){let i=typeof t=="string"?nr(t):t,s=Zl(i.pathname||"/",n);if(s==null)return null;let o=Ap(e);qv(o);let a=null;for(let l=0;a==null&&l<o.length;++l){let u=u0(s);a=a0(o[l],u,r)}return a}function Ap(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let i=(s,o,a)=>{let l={relativePath:a===void 0?s.path||"":a,caseSensitive:s.caseSensitive===!0,childrenIndex:o,route:s};l.relativePath.startsWith("/")&&(ne(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=Ft([r,l.relativePath]),c=n.concat(l);s.children&&s.children.length>0&&(ne(s.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),Ap(s.children,t,c,u)),!(s.path==null&&!s.index)&&t.push({path:u,score:s0(u,s.index),routesMeta:c})};return e.forEach((s,o)=>{var a;if(s.path===""||!((a=s.path)!=null&&a.includes("?")))i(s,o);else for(let l of Mp(s.path))i(s,o,l)}),t}function Mp(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,i=n.endsWith("?"),s=n.replace(/\?$/,"");if(r.length===0)return i?[s,""]:[s];let o=Mp(r.join("/")),a=[];return a.push(...o.map(l=>l===""?s:[s,l].join("/"))),i&&a.push(...o),a.map(l=>e.startsWith("/")&&l===""?"/":l)}function qv(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:o0(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const Jv=/^:[\w-]+$/,e0=3,t0=2,n0=1,r0=10,i0=-2,Kc=e=>e==="*";function s0(e,t){let n=e.split("/"),r=n.length;return n.some(Kc)&&(r+=i0),t&&(r+=t0),n.filter(i=>!Kc(i)).reduce((i,s)=>i+(Jv.test(s)?e0:s===""?n0:r0),r)}function o0(e,t){return e.length===t.length&&e.slice(0,-1).every((r,i)=>r===t[i])?e[e.length-1]-t[t.length-1]:0}function a0(e,t,n){let{routesMeta:r}=e,i={},s="/",o=[];for(let a=0;a<r.length;++a){let l=r[a],u=a===r.length-1,c=s==="/"?t:t.slice(s.length)||"/",d=Gc({path:l.relativePath,caseSensitive:l.caseSensitive,end:u},c),h=l.route;if(!d&&u&&n&&!r[r.length-1].route.index&&(d=Gc({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},c)),!d)return null;Object.assign(i,d.params),o.push({params:i,pathname:Ft([s,d.pathname]),pathnameBase:h0(Ft([s,d.pathnameBase])),route:h}),d.pathnameBase!=="/"&&(s=Ft([s,d.pathnameBase]))}return o}function Gc(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=l0(e.path,e.caseSensitive,e.end),i=t.match(n);if(!i)return null;let s=i[0],o=s.replace(/(.)\/+$/,"$1"),a=i.slice(1);return{params:r.reduce((u,c,d)=>{let{paramName:h,isOptional:y}=c;if(h==="*"){let x=a[d]||"";o=s.slice(0,s.length-x.length).replace(/(.)\/+$/,"$1")}const v=a[d];return y&&!v?u[h]=void 0:u[h]=(v||"").replace(/%2F/g,"/"),u},{}),pathname:s,pathnameBase:o,pattern:e}}function l0(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Np(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],i="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,a,l)=>(r.push({paramName:a,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),i+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":e!==""&&e!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,t?void 0:"i"),r]}function u0(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Np(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Zl(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function c0(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:i=""}=typeof e=="string"?nr(e):e;return{pathname:n?n.startsWith("/")?n:d0(n,t):t,search:p0(r),hash:m0(i)}}function d0(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(i=>{i===".."?n.length>1&&n.pop():i!=="."&&n.push(i)}),n.length>1?n.join("/"):"/"}function Ro(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function f0(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function Rp(e,t){let n=f0(e);return t?n.map((r,i)=>i===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function Lp(e,t,n,r){r===void 0&&(r=!1);let i;typeof e=="string"?i=nr(e):(i=qr({},e),ne(!i.pathname||!i.pathname.includes("?"),Ro("?","pathname","search",i)),ne(!i.pathname||!i.pathname.includes("#"),Ro("#","pathname","hash",i)),ne(!i.search||!i.search.includes("#"),Ro("#","search","hash",i)));let s=e===""||i.pathname==="",o=s?"/":i.pathname,a;if(o==null)a=n;else{let d=t.length-1;if(!r&&o.startsWith("..")){let h=o.split("/");for(;h[0]==="..";)h.shift(),d-=1;i.pathname=h.join("/")}a=d>=0?t[d]:"/"}let l=c0(i,a),u=o&&o!=="/"&&o.endsWith("/"),c=(s||o===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(u||c)&&(l.pathname+="/"),l}const Ft=e=>e.join("/").replace(/\/\/+/g,"/"),h0=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),p0=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,m0=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function g0(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Dp=["post","put","patch","delete"];new Set(Dp);const y0=["get",...Dp];new Set(y0);/**
 * React Router v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Jr(){return Jr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Jr.apply(this,arguments)}const ql=S.createContext(null),v0=S.createContext(null),gn=S.createContext(null),Xs=S.createContext(null),yn=S.createContext({outlet:null,matches:[],isDataRoute:!1}),Vp=S.createContext(null);function x0(e,t){let{relative:n}=t===void 0?{}:t;ci()||ne(!1);let{basename:r,navigator:i}=S.useContext(gn),{hash:s,pathname:o,search:a}=Ip(e,{relative:n}),l=o;return r!=="/"&&(l=o==="/"?r:Ft([r,o])),i.createHref({pathname:l,search:a,hash:s})}function ci(){return S.useContext(Xs)!=null}function di(){return ci()||ne(!1),S.useContext(Xs).location}function _p(e){S.useContext(gn).static||S.useLayoutEffect(e)}function w0(){let{isDataRoute:e}=S.useContext(yn);return e?D0():S0()}function S0(){ci()||ne(!1);let e=S.useContext(ql),{basename:t,future:n,navigator:r}=S.useContext(gn),{matches:i}=S.useContext(yn),{pathname:s}=di(),o=JSON.stringify(Rp(i,n.v7_relativeSplatPath)),a=S.useRef(!1);return _p(()=>{a.current=!0}),S.useCallback(function(u,c){if(c===void 0&&(c={}),!a.current)return;if(typeof u=="number"){r.go(u);return}let d=Lp(u,JSON.parse(o),s,c.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:Ft([t,d.pathname])),(c.replace?r.replace:r.push)(d,c.state,c)},[t,r,o,s,e])}function Ip(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=S.useContext(gn),{matches:i}=S.useContext(yn),{pathname:s}=di(),o=JSON.stringify(Rp(i,r.v7_relativeSplatPath));return S.useMemo(()=>Lp(e,JSON.parse(o),s,n==="path"),[e,o,s,n])}function k0(e,t){return C0(e,t)}function C0(e,t,n,r){ci()||ne(!1);let{navigator:i,static:s}=S.useContext(gn),{matches:o}=S.useContext(yn),a=o[o.length-1],l=a?a.params:{};a&&a.pathname;let u=a?a.pathnameBase:"/";a&&a.route;let c=di(),d;if(t){var h;let p=typeof t=="string"?nr(t):t;u==="/"||(h=p.pathname)!=null&&h.startsWith(u)||ne(!1),d=p}else d=c;let y=d.pathname||"/",v=y;if(u!=="/"){let p=u.replace(/^\//,"").split("/");v="/"+y.replace(/^\//,"").split("/").slice(p.length).join("/")}let x=!s&&n&&n.matches&&n.matches.length>0?n.matches:Xv(e,{pathname:v}),C=N0(x&&x.map(p=>Object.assign({},p,{params:Object.assign({},l,p.params),pathname:Ft([u,i.encodeLocation?i.encodeLocation(p.pathname).pathname:p.pathname]),pathnameBase:p.pathnameBase==="/"?u:Ft([u,i.encodeLocation?i.encodeLocation(p.pathnameBase).pathname:p.pathnameBase])})),o,n,r);return t&&C?S.createElement(Xs.Provider,{value:{location:Jr({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:At.Pop}},C):C}function P0(){let e=L0(),t=g0(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return S.createElement(S.Fragment,null,S.createElement("h2",null,"Unexpected Application Error!"),S.createElement("h3",{style:{fontStyle:"italic"}},t),n?S.createElement("pre",{style:i},n):null,null)}const E0=S.createElement(P0,null);class T0 extends S.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?S.createElement(yn.Provider,{value:this.props.routeContext},S.createElement(Vp.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function j0(e){let{routeContext:t,match:n,children:r}=e,i=S.useContext(ql);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),S.createElement(yn.Provider,{value:t},r)}function N0(e,t,n,r){var i;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var s;if(!n)return null;if(n.errors)e=n.matches;else if((s=r)!=null&&s.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let o=e,a=(i=n)==null?void 0:i.errors;if(a!=null){let c=o.findIndex(d=>d.route.id&&(a==null?void 0:a[d.route.id])!==void 0);c>=0||ne(!1),o=o.slice(0,Math.min(o.length,c+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let c=0;c<o.length;c++){let d=o[c];if((d.route.HydrateFallback||d.route.hydrateFallbackElement)&&(u=c),d.route.id){let{loaderData:h,errors:y}=n,v=d.route.loader&&h[d.route.id]===void 0&&(!y||y[d.route.id]===void 0);if(d.route.lazy||v){l=!0,u>=0?o=o.slice(0,u+1):o=[o[0]];break}}}return o.reduceRight((c,d,h)=>{let y,v=!1,x=null,C=null;n&&(y=a&&d.route.id?a[d.route.id]:void 0,x=d.route.errorElement||E0,l&&(u<0&&h===0?(v=!0,C=null):u===h&&(v=!0,C=d.route.hydrateFallbackElement||null)));let p=t.concat(o.slice(0,h+1)),m=()=>{let g;return y?g=x:v?g=C:d.route.Component?g=S.createElement(d.route.Component,null):d.route.element?g=d.route.element:g=c,S.createElement(j0,{match:d,routeContext:{outlet:c,matches:p,isDataRoute:n!=null},children:g})};return n&&(d.route.ErrorBoundary||d.route.errorElement||h===0)?S.createElement(T0,{location:n.location,revalidation:n.revalidation,component:x,error:y,children:m(),routeContext:{outlet:null,matches:p,isDataRoute:!0}}):m()},null)}var Op=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Op||{}),Ts=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Ts||{});function A0(e){let t=S.useContext(ql);return t||ne(!1),t}function M0(e){let t=S.useContext(v0);return t||ne(!1),t}function R0(e){let t=S.useContext(yn);return t||ne(!1),t}function Fp(e){let t=R0(),n=t.matches[t.matches.length-1];return n.route.id||ne(!1),n.route.id}function L0(){var e;let t=S.useContext(Vp),n=M0(Ts.UseRouteError),r=Fp(Ts.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function D0(){let{router:e}=A0(Op.UseNavigateStable),t=Fp(Ts.UseNavigateStable),n=S.useRef(!1);return _p(()=>{n.current=!0}),S.useCallback(function(i,s){s===void 0&&(s={}),n.current&&(typeof i=="number"?e.navigate(i):e.navigate(i,Jr({fromRouteId:t},s)))},[e,t])}function V0(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function ot(e){ne(!1)}function _0(e){let{basename:t="/",children:n=null,location:r,navigationType:i=At.Pop,navigator:s,static:o=!1,future:a}=e;ci()&&ne(!1);let l=t.replace(/^\/*/,"/"),u=S.useMemo(()=>({basename:l,navigator:s,static:o,future:Jr({v7_relativeSplatPath:!1},a)}),[l,a,s,o]);typeof r=="string"&&(r=nr(r));let{pathname:c="/",search:d="",hash:h="",state:y=null,key:v="default"}=r,x=S.useMemo(()=>{let C=Zl(c,l);return C==null?null:{location:{pathname:C,search:d,hash:h,state:y,key:v},navigationType:i}},[l,c,d,h,y,v,i]);return x==null?null:S.createElement(gn.Provider,{value:u},S.createElement(Xs.Provider,{children:n,value:x}))}function I0(e){let{children:t,location:n}=e;return k0(Fa(t),n)}new Promise(()=>{});function Fa(e,t){t===void 0&&(t=[]);let n=[];return S.Children.forEach(e,(r,i)=>{if(!S.isValidElement(r))return;let s=[...t,i];if(r.type===S.Fragment){n.push.apply(n,Fa(r.props.children,s));return}r.type!==ot&&ne(!1),!r.props.index||!r.props.children||ne(!1);let o={id:r.props.id||s.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(o.children=Fa(r.props.children,s)),n.push(o)}),n}/**
 * React Router DOM v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function za(){return za=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},za.apply(this,arguments)}function O0(e,t){if(e==null)return{};var n={},r=Object.keys(e),i,s;for(s=0;s<r.length;s++)i=r[s],!(t.indexOf(i)>=0)&&(n[i]=e[i]);return n}function F0(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function z0(e,t){return e.button===0&&(!t||t==="_self")&&!F0(e)}const B0=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],U0="6";try{window.__reactRouterVersion=U0}catch{}const $0="startTransition",Qc=_g[$0];function W0(e){let{basename:t,children:n,future:r,window:i}=e,s=S.useRef();s.current==null&&(s.current=Gv({window:i,v5Compat:!0}));let o=s.current,[a,l]=S.useState({action:o.action,location:o.location}),{v7_startTransition:u}=r||{},c=S.useCallback(d=>{u&&Qc?Qc(()=>l(d)):l(d)},[l,u]);return S.useLayoutEffect(()=>o.listen(c),[o,c]),S.useEffect(()=>V0(r),[r]),S.createElement(_0,{basename:t,children:n,location:a.location,navigationType:a.action,navigator:o,future:r})}const b0=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",H0=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,b=S.forwardRef(function(t,n){let{onClick:r,relative:i,reloadDocument:s,replace:o,state:a,target:l,to:u,preventScrollReset:c,viewTransition:d}=t,h=O0(t,B0),{basename:y}=S.useContext(gn),v,x=!1;if(typeof u=="string"&&H0.test(u)&&(v=u,b0))try{let g=new URL(window.location.href),w=u.startsWith("//")?new URL(g.protocol+u):new URL(u),k=Zl(w.pathname,y);w.origin===g.origin&&k!=null?u=k+w.search+w.hash:x=!0}catch{}let C=x0(u,{relative:i}),p=K0(u,{replace:o,state:a,target:l,preventScrollReset:c,relative:i,viewTransition:d});function m(g){r&&r(g),g.defaultPrevented||p(g)}return S.createElement("a",za({},h,{href:v||C,onClick:x||s?r:m,ref:n,target:l}))});var Yc;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Yc||(Yc={}));var Xc;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Xc||(Xc={}));function K0(e,t){let{target:n,replace:r,state:i,preventScrollReset:s,relative:o,viewTransition:a}=t===void 0?{}:t,l=w0(),u=di(),c=Ip(e,{relative:o});return S.useCallback(d=>{if(z0(d,n)){d.preventDefault();let h=r!==void 0?r:Es(u)===Es(c);l(e,{replace:h,state:i,preventScrollReset:s,relative:o,viewTransition:a})}},[u,l,c,r,i,n,e,s,o,a])}const Jl=S.createContext({});function eu(e){const t=S.useRef(null);return t.current===null&&(t.current=e()),t.current}const Zs=S.createContext(null),tu=S.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});class G0 extends S.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function Q0({children:e,isPresent:t}){const n=S.useId(),r=S.useRef(null),i=S.useRef({width:0,height:0,top:0,left:0}),{nonce:s}=S.useContext(tu);return S.useInsertionEffect(()=>{const{width:o,height:a,top:l,left:u}=i.current;if(t||!r.current||!o||!a)return;r.current.dataset.motionPopId=n;const c=document.createElement("style");return s&&(c.nonce=s),document.head.appendChild(c),c.sheet&&c.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${o}px !important;
            height: ${a}px !important;
            top: ${l}px !important;
            left: ${u}px !important;
          }
        `),()=>{document.head.removeChild(c)}},[t]),f.jsx(G0,{isPresent:t,childRef:r,sizeRef:i,children:S.cloneElement(e,{ref:r})})}const Y0=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:i,presenceAffectsLayout:s,mode:o})=>{const a=eu(X0),l=S.useId(),u=S.useCallback(d=>{a.set(d,!0);for(const h of a.values())if(!h)return;r&&r()},[a,r]),c=S.useMemo(()=>({id:l,initial:t,isPresent:n,custom:i,onExitComplete:u,register:d=>(a.set(d,!1),()=>a.delete(d))}),s?[Math.random(),u]:[n,u]);return S.useMemo(()=>{a.forEach((d,h)=>a.set(h,!1))},[n]),S.useEffect(()=>{!n&&!a.size&&r&&r()},[n]),o==="popLayout"&&(e=f.jsx(Q0,{isPresent:n,children:e})),f.jsx(Zs.Provider,{value:c,children:e})};function X0(){return new Map}function zp(e=!0){const t=S.useContext(Zs);if(t===null)return[!0,null];const{isPresent:n,onExitComplete:r,register:i}=t,s=S.useId();S.useEffect(()=>{e&&i(s)},[e]);const o=S.useCallback(()=>e&&r&&r(s),[s,r,e]);return!n&&r?[!1,o]:[!0]}const Ii=e=>e.key||"";function Zc(e){const t=[];return S.Children.forEach(e,n=>{S.isValidElement(n)&&t.push(n)}),t}const nu=typeof window<"u",Bp=nu?S.useLayoutEffect:S.useEffect,Z0=({children:e,custom:t,initial:n=!0,onExitComplete:r,presenceAffectsLayout:i=!0,mode:s="sync",propagate:o=!1})=>{const[a,l]=zp(o),u=S.useMemo(()=>Zc(e),[e]),c=o&&!a?[]:u.map(Ii),d=S.useRef(!0),h=S.useRef(u),y=eu(()=>new Map),[v,x]=S.useState(u),[C,p]=S.useState(u);Bp(()=>{d.current=!1,h.current=u;for(let w=0;w<C.length;w++){const k=Ii(C[w]);c.includes(k)?y.delete(k):y.get(k)!==!0&&y.set(k,!1)}},[C,c.length,c.join("-")]);const m=[];if(u!==v){let w=[...u];for(let k=0;k<C.length;k++){const T=C[k],j=Ii(T);c.includes(j)||(w.splice(k,0,T),m.push(T))}s==="wait"&&m.length&&(w=m),p(Zc(w)),x(u);return}const{forceRender:g}=S.useContext(Jl);return f.jsx(f.Fragment,{children:C.map(w=>{const k=Ii(w),T=o&&!a?!1:u===C||c.includes(k),j=()=>{if(y.has(k))y.set(k,!0);else return;let P=!0;y.forEach(V=>{V||(P=!1)}),P&&(g==null||g(),p(h.current),o&&(l==null||l()),r&&r())};return f.jsx(Y0,{isPresent:T,initial:!d.current||n?void 0:!1,custom:T?void 0:t,presenceAffectsLayout:i,mode:s,onExitComplete:T?void 0:j,children:w},k)})})},Le=e=>e;let Ba=Le;function ru(e){let t;return()=>(t===void 0&&(t=e()),t)}const Xn=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},ft=e=>e*1e3,ht=e=>e/1e3,q0={skipAnimations:!1,useManualTiming:!1};function J0(e){let t=new Set,n=new Set,r=!1,i=!1;const s=new WeakSet;let o={delta:0,timestamp:0,isProcessing:!1};function a(u){s.has(u)&&(l.schedule(u),e()),u(o)}const l={schedule:(u,c=!1,d=!1)=>{const y=d&&r?t:n;return c&&s.add(u),y.has(u)||y.add(u),u},cancel:u=>{n.delete(u),s.delete(u)},process:u=>{if(o=u,r){i=!0;return}r=!0,[t,n]=[n,t],t.forEach(a),t.clear(),r=!1,i&&(i=!1,l.process(u))}};return l}const Oi=["read","resolveKeyframes","update","preRender","render","postRender"],e1=40;function Up(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},s=()=>n=!0,o=Oi.reduce((p,m)=>(p[m]=J0(s),p),{}),{read:a,resolveKeyframes:l,update:u,preRender:c,render:d,postRender:h}=o,y=()=>{const p=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(p-i.timestamp,e1),1),i.timestamp=p,i.isProcessing=!0,a.process(i),l.process(i),u.process(i),c.process(i),d.process(i),h.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(y))},v=()=>{n=!0,r=!0,i.isProcessing||e(y)};return{schedule:Oi.reduce((p,m)=>{const g=o[m];return p[m]=(w,k=!1,T=!1)=>(n||v(),g.schedule(w,k,T)),p},{}),cancel:p=>{for(let m=0;m<Oi.length;m++)o[Oi[m]].cancel(p)},state:i,steps:o}}const{schedule:$,cancel:Ut,state:de,steps:Lo}=Up(typeof requestAnimationFrame<"u"?requestAnimationFrame:Le,!0),$p=S.createContext({strict:!1}),qc={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Zn={};for(const e in qc)Zn[e]={isEnabled:t=>qc[e].some(n=>!!t[n])};function t1(e){for(const t in e)Zn[t]={...Zn[t],...e[t]}}const n1=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function js(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||n1.has(e)}let Wp=e=>!js(e);function r1(e){e&&(Wp=t=>t.startsWith("on")?!js(t):e(t))}try{r1(require("@emotion/is-prop-valid").default)}catch{}function i1(e,t,n){const r={};for(const i in e)i==="values"&&typeof e.values=="object"||(Wp(i)||n===!0&&js(i)||!t&&!js(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function s1(e){if(typeof Proxy>"u")return e;const t=new Map,n=(...r)=>e(...r);return new Proxy(n,{get:(r,i)=>i==="create"?e:(t.has(i)||t.set(i,e(i)),t.get(i))})}const qs=S.createContext({});function ei(e){return typeof e=="string"||Array.isArray(e)}function Js(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const iu=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],su=["initial",...iu];function eo(e){return Js(e.animate)||su.some(t=>ei(e[t]))}function bp(e){return!!(eo(e)||e.variants)}function o1(e,t){if(eo(e)){const{initial:n,animate:r}=e;return{initial:n===!1||ei(n)?n:void 0,animate:ei(r)?r:void 0}}return e.inherit!==!1?t:{}}function a1(e){const{initial:t,animate:n}=o1(e,S.useContext(qs));return S.useMemo(()=>({initial:t,animate:n}),[Jc(t),Jc(n)])}function Jc(e){return Array.isArray(e)?e.join(" "):e}const l1=Symbol.for("motionComponentSymbol");function Ln(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function u1(e,t,n){return S.useCallback(r=>{r&&e.onMount&&e.onMount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):Ln(n)&&(n.current=r))},[t])}const ou=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),c1="framerAppearId",Hp="data-"+ou(c1),{schedule:au,cancel:J2}=Up(queueMicrotask,!1),Kp=S.createContext({});function d1(e,t,n,r,i){var s,o;const{visualElement:a}=S.useContext(qs),l=S.useContext($p),u=S.useContext(Zs),c=S.useContext(tu).reducedMotion,d=S.useRef(null);r=r||l.renderer,!d.current&&r&&(d.current=r(e,{visualState:t,parent:a,props:n,presenceContext:u,blockInitialAnimation:u?u.initial===!1:!1,reducedMotionConfig:c}));const h=d.current,y=S.useContext(Kp);h&&!h.projection&&i&&(h.type==="html"||h.type==="svg")&&f1(d.current,n,i,y);const v=S.useRef(!1);S.useInsertionEffect(()=>{h&&v.current&&h.update(n,u)});const x=n[Hp],C=S.useRef(!!x&&!(!((s=window.MotionHandoffIsComplete)===null||s===void 0)&&s.call(window,x))&&((o=window.MotionHasOptimisedAnimation)===null||o===void 0?void 0:o.call(window,x)));return Bp(()=>{h&&(v.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),au.render(h.render),C.current&&h.animationState&&h.animationState.animateChanges())}),S.useEffect(()=>{h&&(!C.current&&h.animationState&&h.animationState.animateChanges(),C.current&&(queueMicrotask(()=>{var p;(p=window.MotionHandoffMarkAsComplete)===null||p===void 0||p.call(window,x)}),C.current=!1))}),h}function f1(e,t,n,r){const{layoutId:i,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:Gp(e.parent)),e.projection.setOptions({layoutId:i,layout:s,alwaysMeasureLayout:!!o||a&&Ln(a),visualElement:e,animationType:typeof s=="string"?s:"both",initialPromotionConfig:r,layoutScroll:l,layoutRoot:u})}function Gp(e){if(e)return e.options.allowProjection!==!1?e.projection:Gp(e.parent)}function h1({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){var s,o;e&&t1(e);function a(u,c){let d;const h={...S.useContext(tu),...u,layoutId:p1(u)},{isStatic:y}=h,v=a1(u),x=r(u,y);if(!y&&nu){m1();const C=g1(h);d=C.MeasureLayout,v.visualElement=d1(i,x,h,t,C.ProjectionNode)}return f.jsxs(qs.Provider,{value:v,children:[d&&v.visualElement?f.jsx(d,{visualElement:v.visualElement,...h}):null,n(i,u,u1(x,v.visualElement,c),x,y,v.visualElement)]})}a.displayName=`motion.${typeof i=="string"?i:`create(${(o=(s=i.displayName)!==null&&s!==void 0?s:i.name)!==null&&o!==void 0?o:""})`}`;const l=S.forwardRef(a);return l[l1]=i,l}function p1({layoutId:e}){const t=S.useContext(Jl).id;return t&&e!==void 0?t+"-"+e:e}function m1(e,t){S.useContext($p).strict}function g1(e){const{drag:t,layout:n}=Zn;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t!=null&&t.isEnabled(e)||n!=null&&n.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const y1=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function lu(e){return typeof e!="string"||e.includes("-")?!1:!!(y1.indexOf(e)>-1||/[A-Z]/u.test(e))}function ed(e){const t=[{},{}];return e==null||e.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function uu(e,t,n,r){if(typeof t=="function"){const[i,s]=ed(r);t=t(n!==void 0?n:e.custom,i,s)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[i,s]=ed(r);t=t(n!==void 0?n:e.custom,i,s)}return t}const Ua=e=>Array.isArray(e),v1=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),x1=e=>Ua(e)?e[e.length-1]||0:e,ve=e=>!!(e&&e.getVelocity);function qi(e){const t=ve(e)?e.get():e;return v1(t)?t.toValue():t}function w1({scrapeMotionValuesFromProps:e,createRenderState:t,onUpdate:n},r,i,s){const o={latestValues:S1(r,i,s,e),renderState:t()};return n&&(o.onMount=a=>n({props:r,current:a,...o}),o.onUpdate=a=>n(a)),o}const Qp=e=>(t,n)=>{const r=S.useContext(qs),i=S.useContext(Zs),s=()=>w1(e,t,r,i);return n?s():eu(s)};function S1(e,t,n,r){const i={},s=r(e,{});for(const h in s)i[h]=qi(s[h]);let{initial:o,animate:a}=e;const l=eo(e),u=bp(e);t&&u&&!l&&e.inherit!==!1&&(o===void 0&&(o=t.initial),a===void 0&&(a=t.animate));let c=n?n.initial===!1:!1;c=c||o===!1;const d=c?a:o;if(d&&typeof d!="boolean"&&!Js(d)){const h=Array.isArray(d)?d:[d];for(let y=0;y<h.length;y++){const v=uu(e,h[y]);if(v){const{transitionEnd:x,transition:C,...p}=v;for(const m in p){let g=p[m];if(Array.isArray(g)){const w=c?g.length-1:0;g=g[w]}g!==null&&(i[m]=g)}for(const m in x)i[m]=x[m]}}}return i}const rr=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],vn=new Set(rr),Yp=e=>t=>typeof t=="string"&&t.startsWith(e),Xp=Yp("--"),k1=Yp("var(--"),cu=e=>k1(e)?C1.test(e.split("/*")[0].trim()):!1,C1=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Zp=(e,t)=>t&&typeof e=="number"?t.transform(e):e,vt=(e,t,n)=>n>t?t:n<e?e:n,ir={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},ti={...ir,transform:e=>vt(0,1,e)},Fi={...ir,default:1},fi=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),kt=fi("deg"),it=fi("%"),M=fi("px"),P1=fi("vh"),E1=fi("vw"),td={...it,parse:e=>it.parse(e)/100,transform:e=>it.transform(e*100)},T1={borderWidth:M,borderTopWidth:M,borderRightWidth:M,borderBottomWidth:M,borderLeftWidth:M,borderRadius:M,radius:M,borderTopLeftRadius:M,borderTopRightRadius:M,borderBottomRightRadius:M,borderBottomLeftRadius:M,width:M,maxWidth:M,height:M,maxHeight:M,top:M,right:M,bottom:M,left:M,padding:M,paddingTop:M,paddingRight:M,paddingBottom:M,paddingLeft:M,margin:M,marginTop:M,marginRight:M,marginBottom:M,marginLeft:M,backgroundPositionX:M,backgroundPositionY:M},j1={rotate:kt,rotateX:kt,rotateY:kt,rotateZ:kt,scale:Fi,scaleX:Fi,scaleY:Fi,scaleZ:Fi,skew:kt,skewX:kt,skewY:kt,distance:M,translateX:M,translateY:M,translateZ:M,x:M,y:M,z:M,perspective:M,transformPerspective:M,opacity:ti,originX:td,originY:td,originZ:M},nd={...ir,transform:Math.round},du={...T1,...j1,zIndex:nd,size:M,fillOpacity:ti,strokeOpacity:ti,numOctaves:nd},N1={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},A1=rr.length;function M1(e,t,n){let r="",i=!0;for(let s=0;s<A1;s++){const o=rr[s],a=e[o];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(o.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||n){const u=Zp(a,du[o]);if(!l){i=!1;const c=N1[o]||o;r+=`${c}(${u}) `}n&&(t[o]=u)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}function fu(e,t,n){const{style:r,vars:i,transformOrigin:s}=e;let o=!1,a=!1;for(const l in t){const u=t[l];if(vn.has(l)){o=!0;continue}else if(Xp(l)){i[l]=u;continue}else{const c=Zp(u,du[l]);l.startsWith("origin")?(a=!0,s[l]=c):r[l]=c}}if(t.transform||(o||n?r.transform=M1(t,e.transform,n):r.transform&&(r.transform="none")),a){const{originX:l="50%",originY:u="50%",originZ:c=0}=s;r.transformOrigin=`${l} ${u} ${c}`}}const R1={offset:"stroke-dashoffset",array:"stroke-dasharray"},L1={offset:"strokeDashoffset",array:"strokeDasharray"};function D1(e,t,n=1,r=0,i=!0){e.pathLength=1;const s=i?R1:L1;e[s.offset]=M.transform(-r);const o=M.transform(t),a=M.transform(n);e[s.array]=`${o} ${a}`}function rd(e,t,n){return typeof e=="string"?e:M.transform(t+n*e)}function V1(e,t,n){const r=rd(t,e.x,e.width),i=rd(n,e.y,e.height);return`${r} ${i}`}function hu(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:s,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},c,d){if(fu(e,u,d),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:h,style:y,dimensions:v}=e;h.transform&&(v&&(y.transform=h.transform),delete h.transform),v&&(i!==void 0||s!==void 0||y.transform)&&(y.transformOrigin=V1(v,i!==void 0?i:.5,s!==void 0?s:.5)),t!==void 0&&(h.x=t),n!==void 0&&(h.y=n),r!==void 0&&(h.scale=r),o!==void 0&&D1(h,o,a,l,!1)}const pu=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),qp=()=>({...pu(),attrs:{}}),mu=e=>typeof e=="string"&&e.toLowerCase()==="svg";function Jp(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const s in n)e.style.setProperty(s,n[s])}const em=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function tm(e,t,n,r){Jp(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(em.has(i)?i:ou(i),t.attrs[i])}const Ns={};function _1(e){Object.assign(Ns,e)}function nm(e,{layout:t,layoutId:n}){return vn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!Ns[e]||e==="opacity")}function gu(e,t,n){var r;const{style:i}=e,s={};for(const o in i)(ve(i[o])||t.style&&ve(t.style[o])||nm(o,e)||((r=n==null?void 0:n.getValue(o))===null||r===void 0?void 0:r.liveStyle)!==void 0)&&(s[o]=i[o]);return s}function rm(e,t,n){const r=gu(e,t,n);for(const i in e)if(ve(e[i])||ve(t[i])){const s=rr.indexOf(i)!==-1?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i;r[s]=e[i]}return r}function I1(e,t){try{t.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{t.dimensions={x:0,y:0,width:0,height:0}}}const id=["x","y","width","height","cx","cy","r"],O1={useVisualState:Qp({scrapeMotionValuesFromProps:rm,createRenderState:qp,onUpdate:({props:e,prevProps:t,current:n,renderState:r,latestValues:i})=>{if(!n)return;let s=!!e.drag;if(!s){for(const a in i)if(vn.has(a)){s=!0;break}}if(!s)return;let o=!t;if(t)for(let a=0;a<id.length;a++){const l=id[a];e[l]!==t[l]&&(o=!0)}o&&$.read(()=>{I1(n,r),$.render(()=>{hu(r,i,mu(n.tagName),e.transformTemplate),tm(n,r)})})}})},F1={useVisualState:Qp({scrapeMotionValuesFromProps:gu,createRenderState:pu})};function im(e,t,n){for(const r in t)!ve(t[r])&&!nm(r,n)&&(e[r]=t[r])}function z1({transformTemplate:e},t){return S.useMemo(()=>{const n=pu();return fu(n,t,e),Object.assign({},n.vars,n.style)},[t])}function B1(e,t){const n=e.style||{},r={};return im(r,n,e),Object.assign(r,z1(e,t)),r}function U1(e,t){const n={},r=B1(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}function $1(e,t,n,r){const i=S.useMemo(()=>{const s=qp();return hu(s,t,mu(r),e.transformTemplate),{...s.attrs,style:{...s.style}}},[t]);if(e.style){const s={};im(s,e.style,e),i.style={...s,...i.style}}return i}function W1(e=!1){return(n,r,i,{latestValues:s},o)=>{const l=(lu(n)?$1:U1)(r,s,o,n),u=i1(r,typeof n=="string",e),c=n!==S.Fragment?{...u,...l,ref:i}:{},{children:d}=r,h=S.useMemo(()=>ve(d)?d.get():d,[d]);return S.createElement(n,{...c,children:h})}}function b1(e,t){return function(r,{forwardMotionProps:i}={forwardMotionProps:!1}){const o={...lu(r)?O1:F1,preloadedFeatures:e,useRender:W1(i),createVisualElement:t,Component:r};return h1(o)}}function sm(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function to(e,t,n){const r=e.getProps();return uu(r,t,n!==void 0?n:r.custom,e)}const H1=ru(()=>window.ScrollTimeline!==void 0);class K1{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,n){for(let r=0;r<this.animations.length;r++)this.animations[r][t]=n}attachTimeline(t,n){const r=this.animations.map(i=>{if(H1()&&i.attachTimeline)return i.attachTimeline(t);if(typeof n=="function")return n(i)});return()=>{r.forEach((i,s)=>{i&&i(),this.animations[s].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let n=0;n<this.animations.length;n++)t=Math.max(t,this.animations[n].duration);return t}runAll(t){this.animations.forEach(n=>n[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class G1 extends K1{then(t,n){return Promise.all(this.animations).then(t).catch(n)}}function yu(e,t){return e?e[t]||e.default||e:void 0}const $a=2e4;function om(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<$a;)t+=n,r=e.next(t);return t>=$a?1/0:t}function vu(e){return typeof e=="function"}function sd(e,t){e.timeline=t,e.onfinish=null}const xu=e=>Array.isArray(e)&&typeof e[0]=="number",Q1={linearEasing:void 0};function Y1(e,t){const n=ru(e);return()=>{var r;return(r=Q1[t])!==null&&r!==void 0?r:n()}}const As=Y1(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),am=(e,t,n=10)=>{let r="";const i=Math.max(Math.round(t/n),2);for(let s=0;s<i;s++)r+=e(Xn(0,i-1,s))+", ";return`linear(${r.substring(0,r.length-2)})`};function lm(e){return!!(typeof e=="function"&&As()||!e||typeof e=="string"&&(e in Wa||As())||xu(e)||Array.isArray(e)&&e.every(lm))}const xr=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Wa={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:xr([0,.65,.55,1]),circOut:xr([.55,0,1,.45]),backIn:xr([.31,.01,.66,-.59]),backOut:xr([.33,1.53,.69,.99])};function um(e,t){if(e)return typeof e=="function"&&As()?am(e,t):xu(e)?xr(e):Array.isArray(e)?e.map(n=>um(n,t)||Wa.easeOut):Wa[e]}const Ke={x:!1,y:!1};function cm(){return Ke.x||Ke.y}function X1(e,t,n){var r;if(e instanceof Element)return[e];if(typeof e=="string"){let i=document;const s=(r=void 0)!==null&&r!==void 0?r:i.querySelectorAll(e);return s?Array.from(s):[]}return Array.from(e)}function dm(e,t){const n=X1(e),r=new AbortController,i={passive:!0,...t,signal:r.signal};return[n,i,()=>r.abort()]}function od(e){return t=>{t.pointerType==="touch"||cm()||e(t)}}function Z1(e,t,n={}){const[r,i,s]=dm(e,n),o=od(a=>{const{target:l}=a,u=t(a);if(typeof u!="function"||!l)return;const c=od(d=>{u(d),l.removeEventListener("pointerleave",c)});l.addEventListener("pointerleave",c,i)});return r.forEach(a=>{a.addEventListener("pointerenter",o,i)}),s}const fm=(e,t)=>t?e===t?!0:fm(e,t.parentElement):!1,wu=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1,q1=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function J1(e){return q1.has(e.tagName)||e.tabIndex!==-1}const wr=new WeakSet;function ad(e){return t=>{t.key==="Enter"&&e(t)}}function Do(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}const ex=(e,t)=>{const n=e.currentTarget;if(!n)return;const r=ad(()=>{if(wr.has(n))return;Do(n,"down");const i=ad(()=>{Do(n,"up")}),s=()=>Do(n,"cancel");n.addEventListener("keyup",i,t),n.addEventListener("blur",s,t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function ld(e){return wu(e)&&!cm()}function tx(e,t,n={}){const[r,i,s]=dm(e,n),o=a=>{const l=a.currentTarget;if(!ld(a)||wr.has(l))return;wr.add(l);const u=t(a),c=(y,v)=>{window.removeEventListener("pointerup",d),window.removeEventListener("pointercancel",h),!(!ld(y)||!wr.has(l))&&(wr.delete(l),typeof u=="function"&&u(y,{success:v}))},d=y=>{c(y,n.useGlobalTarget||fm(l,y.target))},h=y=>{c(y,!1)};window.addEventListener("pointerup",d,i),window.addEventListener("pointercancel",h,i)};return r.forEach(a=>{!J1(a)&&a.getAttribute("tabindex")===null&&(a.tabIndex=0),(n.useGlobalTarget?window:a).addEventListener("pointerdown",o,i),a.addEventListener("focus",u=>ex(u,i),i)}),s}function nx(e){return e==="x"||e==="y"?Ke[e]?null:(Ke[e]=!0,()=>{Ke[e]=!1}):Ke.x||Ke.y?null:(Ke.x=Ke.y=!0,()=>{Ke.x=Ke.y=!1})}const hm=new Set(["width","height","top","left","right","bottom",...rr]);let Ji;function rx(){Ji=void 0}const st={now:()=>(Ji===void 0&&st.set(de.isProcessing||q0.useManualTiming?de.timestamp:performance.now()),Ji),set:e=>{Ji=e,queueMicrotask(rx)}};function Su(e,t){e.indexOf(t)===-1&&e.push(t)}function ku(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class Cu{constructor(){this.subscriptions=[]}add(t){return Su(this.subscriptions,t),()=>ku(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let s=0;s<i;s++){const o=this.subscriptions[s];o&&o(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function pm(e,t){return t?e*(1e3/t):0}const ud=30,ix=e=>!isNaN(parseFloat(e));class sx{constructor(t,n={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,i=!0)=>{const s=st.now();this.updatedAt!==s&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=st.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=ix(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new Cu);const r=this.events[t].add(n);return t==="change"?()=>{r(),$.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=st.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>ud)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,ud);return pm(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ni(e,t){return new sx(e,t)}function ox(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,ni(n))}function ax(e,t){const n=to(e,t);let{transitionEnd:r={},transition:i={},...s}=n||{};s={...s,...r};for(const o in s){const a=x1(s[o]);ox(e,o,a)}}function lx(e){return!!(ve(e)&&e.add)}function ba(e,t){const n=e.getValue("willChange");if(lx(n))return n.add(t)}function mm(e){return e.props[Hp]}const gm=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,ux=1e-7,cx=12;function dx(e,t,n,r,i){let s,o,a=0;do o=t+(n-t)/2,s=gm(o,r,i)-e,s>0?n=o:t=o;while(Math.abs(s)>ux&&++a<cx);return o}function hi(e,t,n,r){if(e===t&&n===r)return Le;const i=s=>dx(s,0,1,e,n);return s=>s===0||s===1?s:gm(i(s),t,r)}const ym=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,vm=e=>t=>1-e(1-t),xm=hi(.33,1.53,.69,.99),Pu=vm(xm),wm=ym(Pu),Sm=e=>(e*=2)<1?.5*Pu(e):.5*(2-Math.pow(2,-10*(e-1))),Eu=e=>1-Math.sin(Math.acos(e)),km=vm(Eu),Cm=ym(Eu),Pm=e=>/^0[^.\s]+$/u.test(e);function fx(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||Pm(e):!0}const Rr=e=>Math.round(e*1e5)/1e5,Tu=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function hx(e){return e==null}const px=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ju=(e,t)=>n=>!!(typeof n=="string"&&px.test(n)&&n.startsWith(e)||t&&!hx(n)&&Object.prototype.hasOwnProperty.call(n,t)),Em=(e,t,n)=>r=>{if(typeof r!="string")return r;const[i,s,o,a]=r.match(Tu);return{[e]:parseFloat(i),[t]:parseFloat(s),[n]:parseFloat(o),alpha:a!==void 0?parseFloat(a):1}},mx=e=>vt(0,255,e),Vo={...ir,transform:e=>Math.round(mx(e))},sn={test:ju("rgb","red"),parse:Em("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+Vo.transform(e)+", "+Vo.transform(t)+", "+Vo.transform(n)+", "+Rr(ti.transform(r))+")"};function gx(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const Ha={test:ju("#"),parse:gx,transform:sn.transform},Dn={test:ju("hsl","hue"),parse:Em("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+it.transform(Rr(t))+", "+it.transform(Rr(n))+", "+Rr(ti.transform(r))+")"},ge={test:e=>sn.test(e)||Ha.test(e)||Dn.test(e),parse:e=>sn.test(e)?sn.parse(e):Dn.test(e)?Dn.parse(e):Ha.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?sn.transform(e):Dn.transform(e)},yx=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function vx(e){var t,n;return isNaN(e)&&typeof e=="string"&&(((t=e.match(Tu))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(yx))===null||n===void 0?void 0:n.length)||0)>0}const Tm="number",jm="color",xx="var",wx="var(",cd="${}",Sx=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ri(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[];let s=0;const a=t.replace(Sx,l=>(ge.test(l)?(r.color.push(s),i.push(jm),n.push(ge.parse(l))):l.startsWith(wx)?(r.var.push(s),i.push(xx),n.push(l)):(r.number.push(s),i.push(Tm),n.push(parseFloat(l))),++s,cd)).split(cd);return{values:n,split:a,indexes:r,types:i}}function Nm(e){return ri(e).values}function Am(e){const{split:t,types:n}=ri(e),r=t.length;return i=>{let s="";for(let o=0;o<r;o++)if(s+=t[o],i[o]!==void 0){const a=n[o];a===Tm?s+=Rr(i[o]):a===jm?s+=ge.transform(i[o]):s+=i[o]}return s}}const kx=e=>typeof e=="number"?0:e;function Cx(e){const t=Nm(e);return Am(e)(t.map(kx))}const $t={test:vx,parse:Nm,createTransformer:Am,getAnimatableNone:Cx},Px=new Set(["brightness","contrast","saturate","opacity"]);function Ex(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Tu)||[];if(!r)return e;const i=n.replace(r,"");let s=Px.has(t)?1:0;return r!==n&&(s*=100),t+"("+s+i+")"}const Tx=/\b([a-z-]*)\(.*?\)/gu,Ka={...$t,getAnimatableNone:e=>{const t=e.match(Tx);return t?t.map(Ex).join(" "):e}},jx={...du,color:ge,backgroundColor:ge,outlineColor:ge,fill:ge,stroke:ge,borderColor:ge,borderTopColor:ge,borderRightColor:ge,borderBottomColor:ge,borderLeftColor:ge,filter:Ka,WebkitFilter:Ka},Nu=e=>jx[e];function Mm(e,t){let n=Nu(e);return n!==Ka&&(n=$t),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const Nx=new Set(["auto","none","0"]);function Ax(e,t,n){let r=0,i;for(;r<e.length&&!i;){const s=e[r];typeof s=="string"&&!Nx.has(s)&&ri(s).values.length&&(i=e[r]),r++}if(i&&n)for(const s of t)e[s]=Mm(n,i)}const dd=e=>e===ir||e===M,fd=(e,t)=>parseFloat(e.split(", ")[t]),hd=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/u);if(i)return fd(i[1],t);{const s=r.match(/^matrix\((.+)\)$/u);return s?fd(s[1],e):0}},Mx=new Set(["x","y","z"]),Rx=rr.filter(e=>!Mx.has(e));function Lx(e){const t=[];return Rx.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}const qn={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:hd(4,13),y:hd(5,14)};qn.translateX=qn.x;qn.translateY=qn.y;const ln=new Set;let Ga=!1,Qa=!1;function Rm(){if(Qa){const e=Array.from(ln).filter(r=>r.needsMeasurement),t=new Set(e.map(r=>r.element)),n=new Map;t.forEach(r=>{const i=Lx(r);i.length&&(n.set(r,i),r.render())}),e.forEach(r=>r.measureInitialState()),t.forEach(r=>{r.render();const i=n.get(r);i&&i.forEach(([s,o])=>{var a;(a=r.getValue(s))===null||a===void 0||a.set(o)})}),e.forEach(r=>r.measureEndState()),e.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}Qa=!1,Ga=!1,ln.forEach(e=>e.complete()),ln.clear()}function Lm(){ln.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(Qa=!0)})}function Dx(){Lm(),Rm()}class Au{constructor(t,n,r,i,s,o=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=r,this.motionValue=i,this.element=s,this.isAsync=o}scheduleResolve(){this.isScheduled=!0,this.isAsync?(ln.add(this),Ga||(Ga=!0,$.read(Lm),$.resolveKeyframes(Rm))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:r,motionValue:i}=this;for(let s=0;s<t.length;s++)if(t[s]===null)if(s===0){const o=i==null?void 0:i.get(),a=t[t.length-1];if(o!==void 0)t[0]=o;else if(r&&n){const l=r.readValue(n,a);l!=null&&(t[0]=l)}t[0]===void 0&&(t[0]=a),i&&o===void 0&&i.set(t[0])}else t[s]=t[s-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),ln.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,ln.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const Dm=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),Vx=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function _x(e){const t=Vx.exec(e);if(!t)return[,];const[,n,r,i]=t;return[`--${n??r}`,i]}function Vm(e,t,n=1){const[r,i]=_x(e);if(!r)return;const s=window.getComputedStyle(t).getPropertyValue(r);if(s){const o=s.trim();return Dm(o)?parseFloat(o):o}return cu(i)?Vm(i,t,n+1):i}const _m=e=>t=>t.test(e),Ix={test:e=>e==="auto",parse:e=>e},Im=[ir,M,it,kt,E1,P1,Ix],pd=e=>Im.find(_m(e));class Om extends Au{constructor(t,n,r,i,s){super(t,n,r,i,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:r}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<t.length;l++){let u=t[l];if(typeof u=="string"&&(u=u.trim(),cu(u))){const c=Vm(u,n.current);c!==void 0&&(t[l]=c),l===t.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!hm.has(r)||t.length!==2)return;const[i,s]=t,o=pd(i),a=pd(s);if(o!==a)if(dd(o)&&dd(a))for(let l=0;l<t.length;l++){const u=t[l];typeof u=="string"&&(t[l]=parseFloat(u))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,r=[];for(let i=0;i<t.length;i++)fx(t[i])&&r.push(i);r.length&&Ax(t,r,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:r}=this;if(!t||!t.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=qn[r](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&t.getValue(r,i).jump(i,!1)}measureEndState(){var t;const{element:n,name:r,unresolvedKeyframes:i}=this;if(!n||!n.current)return;const s=n.getValue(r);s&&s.jump(this.measuredOrigin,!1);const o=i.length-1,a=i[o];i[o]=qn[r](n.measureViewportBox(),window.getComputedStyle(n.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),!((t=this.removedTransforms)===null||t===void 0)&&t.length&&this.removedTransforms.forEach(([l,u])=>{n.getValue(l).set(u)}),this.resolveNoneKeyframes()}}const md=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&($t.test(e)||e==="0")&&!e.startsWith("url("));function Ox(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function Fx(e,t,n,r){const i=e[0];if(i===null)return!1;if(t==="display"||t==="visibility")return!0;const s=e[e.length-1],o=md(i,t),a=md(s,t);return!o||!a?!1:Ox(e)||(n==="spring"||vu(n))&&r}const zx=e=>e!==null;function no(e,{repeat:t,repeatType:n="loop"},r){const i=e.filter(zx),s=t&&n!=="loop"&&t%2===1?0:i.length-1;return!s||r===void 0?i[s]:r}const Bx=40;class Fm{constructor({autoplay:t=!0,delay:n=0,type:r="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:o="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=st.now(),this.options={autoplay:t,delay:n,type:r,repeat:i,repeatDelay:s,repeatType:o,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>Bx?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&Dx(),this._resolved}onKeyframesResolved(t,n){this.resolvedAt=st.now(),this.hasAttemptedResolve=!0;const{name:r,type:i,velocity:s,delay:o,onComplete:a,onUpdate:l,isGenerator:u}=this.options;if(!u&&!Fx(t,r,i,s))if(o)this.options.duration=0;else{l&&l(no(t,this.options,n)),a&&a(),this.resolveFinishedPromise();return}const c=this.initPlayback(t,n);c!==!1&&(this._resolved={keyframes:t,finalKeyframe:n,...c},this.onPostResolved())}onPostResolved(){}then(t,n){return this.currentFinishedPromise.then(t,n)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}const K=(e,t,n)=>e+(t-e)*n;function _o(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function Ux({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,s=0,o=0;if(!t)i=s=o=n;else{const a=n<.5?n*(1+t):n+t-n*t,l=2*n-a;i=_o(l,a,e+1/3),s=_o(l,a,e),o=_o(l,a,e-1/3)}return{red:Math.round(i*255),green:Math.round(s*255),blue:Math.round(o*255),alpha:r}}function Ms(e,t){return n=>n>0?t:e}const Io=(e,t,n)=>{const r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},$x=[Ha,sn,Dn],Wx=e=>$x.find(t=>t.test(e));function gd(e){const t=Wx(e);if(!t)return!1;let n=t.parse(e);return t===Dn&&(n=Ux(n)),n}const yd=(e,t)=>{const n=gd(e),r=gd(t);if(!n||!r)return Ms(e,t);const i={...n};return s=>(i.red=Io(n.red,r.red,s),i.green=Io(n.green,r.green,s),i.blue=Io(n.blue,r.blue,s),i.alpha=K(n.alpha,r.alpha,s),sn.transform(i))},bx=(e,t)=>n=>t(e(n)),pi=(...e)=>e.reduce(bx),Ya=new Set(["none","hidden"]);function Hx(e,t){return Ya.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function Kx(e,t){return n=>K(e,t,n)}function Mu(e){return typeof e=="number"?Kx:typeof e=="string"?cu(e)?Ms:ge.test(e)?yd:Yx:Array.isArray(e)?zm:typeof e=="object"?ge.test(e)?yd:Gx:Ms}function zm(e,t){const n=[...e],r=n.length,i=e.map((s,o)=>Mu(s)(s,t[o]));return s=>{for(let o=0;o<r;o++)n[o]=i[o](s);return n}}function Gx(e,t){const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=Mu(e[i])(e[i],t[i]));return i=>{for(const s in r)n[s]=r[s](i);return n}}function Qx(e,t){var n;const r=[],i={color:0,var:0,number:0};for(let s=0;s<t.values.length;s++){const o=t.types[s],a=e.indexes[o][i[o]],l=(n=e.values[a])!==null&&n!==void 0?n:0;r[s]=l,i[o]++}return r}const Yx=(e,t)=>{const n=$t.createTransformer(t),r=ri(e),i=ri(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?Ya.has(e)&&!i.values.length||Ya.has(t)&&!r.values.length?Hx(e,t):pi(zm(Qx(r,i),i.values),n):Ms(e,t)};function Bm(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?K(e,t,n):Mu(e)(e,t)}const Xx=5;function Um(e,t,n){const r=Math.max(t-Xx,0);return pm(n-e(r),t-r)}const Y={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Oo=.001;function Zx({duration:e=Y.duration,bounce:t=Y.bounce,velocity:n=Y.velocity,mass:r=Y.mass}){let i,s,o=1-t;o=vt(Y.minDamping,Y.maxDamping,o),e=vt(Y.minDuration,Y.maxDuration,ht(e)),o<1?(i=u=>{const c=u*o,d=c*e,h=c-n,y=Xa(u,o),v=Math.exp(-d);return Oo-h/y*v},s=u=>{const d=u*o*e,h=d*n+n,y=Math.pow(o,2)*Math.pow(u,2)*e,v=Math.exp(-d),x=Xa(Math.pow(u,2),o);return(-i(u)+Oo>0?-1:1)*((h-y)*v)/x}):(i=u=>{const c=Math.exp(-u*e),d=(u-n)*e+1;return-Oo+c*d},s=u=>{const c=Math.exp(-u*e),d=(n-u)*(e*e);return c*d});const a=5/e,l=Jx(i,s,a);if(e=ft(e),isNaN(l))return{stiffness:Y.stiffness,damping:Y.damping,duration:e};{const u=Math.pow(l,2)*r;return{stiffness:u,damping:o*2*Math.sqrt(r*u),duration:e}}}const qx=12;function Jx(e,t,n){let r=n;for(let i=1;i<qx;i++)r=r-e(r)/t(r);return r}function Xa(e,t){return e*Math.sqrt(1-t*t)}const ew=["duration","bounce"],tw=["stiffness","damping","mass"];function vd(e,t){return t.some(n=>e[n]!==void 0)}function nw(e){let t={velocity:Y.velocity,stiffness:Y.stiffness,damping:Y.damping,mass:Y.mass,isResolvedFromDuration:!1,...e};if(!vd(e,tw)&&vd(e,ew))if(e.visualDuration){const n=e.visualDuration,r=2*Math.PI/(n*1.2),i=r*r,s=2*vt(.05,1,1-(e.bounce||0))*Math.sqrt(i);t={...t,mass:Y.mass,stiffness:i,damping:s}}else{const n=Zx(e);t={...t,...n,mass:Y.mass},t.isResolvedFromDuration=!0}return t}function $m(e=Y.visualDuration,t=Y.bounce){const n=typeof e!="object"?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:r,restDelta:i}=n;const s=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],a={done:!1,value:s},{stiffness:l,damping:u,mass:c,duration:d,velocity:h,isResolvedFromDuration:y}=nw({...n,velocity:-ht(n.velocity||0)}),v=h||0,x=u/(2*Math.sqrt(l*c)),C=o-s,p=ht(Math.sqrt(l/c)),m=Math.abs(C)<5;r||(r=m?Y.restSpeed.granular:Y.restSpeed.default),i||(i=m?Y.restDelta.granular:Y.restDelta.default);let g;if(x<1){const k=Xa(p,x);g=T=>{const j=Math.exp(-x*p*T);return o-j*((v+x*p*C)/k*Math.sin(k*T)+C*Math.cos(k*T))}}else if(x===1)g=k=>o-Math.exp(-p*k)*(C+(v+p*C)*k);else{const k=p*Math.sqrt(x*x-1);g=T=>{const j=Math.exp(-x*p*T),P=Math.min(k*T,300);return o-j*((v+x*p*C)*Math.sinh(P)+k*C*Math.cosh(P))/k}}const w={calculatedDuration:y&&d||null,next:k=>{const T=g(k);if(y)a.done=k>=d;else{let j=0;x<1&&(j=k===0?ft(v):Um(g,k,T));const P=Math.abs(j)<=r,V=Math.abs(o-T)<=i;a.done=P&&V}return a.value=a.done?o:T,a},toString:()=>{const k=Math.min(om(w),$a),T=am(j=>w.next(k*j).value,k,30);return k+"ms "+T}};return w}function xd({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:c}){const d=e[0],h={done:!1,value:d},y=P=>a!==void 0&&P<a||l!==void 0&&P>l,v=P=>a===void 0?l:l===void 0||Math.abs(a-P)<Math.abs(l-P)?a:l;let x=n*t;const C=d+x,p=o===void 0?C:o(C);p!==C&&(x=p-d);const m=P=>-x*Math.exp(-P/r),g=P=>p+m(P),w=P=>{const V=m(P),R=g(P);h.done=Math.abs(V)<=u,h.value=h.done?p:R};let k,T;const j=P=>{y(h.value)&&(k=P,T=$m({keyframes:[h.value,v(h.value)],velocity:Um(g,P,h.value),damping:i,stiffness:s,restDelta:u,restSpeed:c}))};return j(0),{calculatedDuration:null,next:P=>{let V=!1;return!T&&k===void 0&&(V=!0,w(P),j(P)),k!==void 0&&P>=k?T.next(P-k):(!V&&w(P),h)}}}const rw=hi(.42,0,1,1),iw=hi(0,0,.58,1),Wm=hi(.42,0,.58,1),sw=e=>Array.isArray(e)&&typeof e[0]!="number",wd={linear:Le,easeIn:rw,easeInOut:Wm,easeOut:iw,circIn:Eu,circInOut:Cm,circOut:km,backIn:Pu,backInOut:wm,backOut:xm,anticipate:Sm},Sd=e=>{if(xu(e)){Ba(e.length===4);const[t,n,r,i]=e;return hi(t,n,r,i)}else if(typeof e=="string")return Ba(wd[e]!==void 0),wd[e];return e};function ow(e,t,n){const r=[],i=n||Bm,s=e.length-1;for(let o=0;o<s;o++){let a=i(e[o],e[o+1]);if(t){const l=Array.isArray(t)?t[o]||Le:t;a=pi(l,a)}r.push(a)}return r}function aw(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const s=e.length;if(Ba(s===t.length),s===1)return()=>t[0];if(s===2&&t[0]===t[1])return()=>t[1];const o=e[0]===e[1];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());const a=ow(t,r,i),l=a.length,u=c=>{if(o&&c<e[0])return t[0];let d=0;if(l>1)for(;d<e.length-2&&!(c<e[d+1]);d++);const h=Xn(e[d],e[d+1],c);return a[d](h)};return n?c=>u(vt(e[0],e[s-1],c)):u}function lw(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=Xn(0,t,r);e.push(K(n,1,i))}}function uw(e){const t=[0];return lw(t,e.length-1),t}function cw(e,t){return e.map(n=>n*t)}function dw(e,t){return e.map(()=>t||Wm).splice(0,e.length-1)}function Rs({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const i=sw(r)?r.map(Sd):Sd(r),s={done:!1,value:t[0]},o=cw(n&&n.length===t.length?n:uw(t),e),a=aw(o,t,{ease:Array.isArray(i)?i:dw(t,i)});return{calculatedDuration:e,next:l=>(s.value=a(l),s.done=l>=e,s)}}const fw=e=>{const t=({timestamp:n})=>e(n);return{start:()=>$.update(t,!0),stop:()=>Ut(t),now:()=>de.isProcessing?de.timestamp:st.now()}},hw={decay:xd,inertia:xd,tween:Rs,keyframes:Rs,spring:$m},pw=e=>e/100;class Ru extends Fm{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:l}=this.options;l&&l()};const{name:n,motionValue:r,element:i,keyframes:s}=this.options,o=(i==null?void 0:i.KeyframeResolver)||Au,a=(l,u)=>this.onKeyframesResolved(l,u);this.resolver=new o(s,a,n,r,i),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){const{type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:s,velocity:o=0}=this.options,a=vu(n)?n:hw[n]||Rs;let l,u;a!==Rs&&typeof t[0]!="number"&&(l=pi(pw,Bm(t[0],t[1])),t=[0,100]);const c=a({...this.options,keyframes:t});s==="mirror"&&(u=a({...this.options,keyframes:[...t].reverse(),velocity:-o})),c.calculatedDuration===null&&(c.calculatedDuration=om(c));const{calculatedDuration:d}=c,h=d+i,y=h*(r+1)-i;return{generator:c,mirroredGenerator:u,mapPercentToKeyframes:l,calculatedDuration:d,resolvedDuration:h,totalDuration:y}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!t?this.pause():this.state=this.pendingPlayState}tick(t,n=!1){const{resolved:r}=this;if(!r){const{keyframes:P}=this.options;return{done:!0,value:P[P.length-1]}}const{finalKeyframe:i,generator:s,mirroredGenerator:o,mapPercentToKeyframes:a,keyframes:l,calculatedDuration:u,totalDuration:c,resolvedDuration:d}=r;if(this.startTime===null)return s.next(0);const{delay:h,repeat:y,repeatType:v,repeatDelay:x,onUpdate:C}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-c/this.speed,this.startTime)),n?this.currentTime=t:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const p=this.currentTime-h*(this.speed>=0?1:-1),m=this.speed>=0?p<0:p>c;this.currentTime=Math.max(p,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=c);let g=this.currentTime,w=s;if(y){const P=Math.min(this.currentTime,c)/d;let V=Math.floor(P),R=P%1;!R&&P>=1&&(R=1),R===1&&V--,V=Math.min(V,y+1),!!(V%2)&&(v==="reverse"?(R=1-R,x&&(R-=x/d)):v==="mirror"&&(w=o)),g=vt(0,1,R)*d}const k=m?{done:!1,value:l[0]}:w.next(g);a&&(k.value=a(k.value));let{done:T}=k;!m&&u!==null&&(T=this.speed>=0?this.currentTime>=c:this.currentTime<=0);const j=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&T);return j&&i!==void 0&&(k.value=no(l,this.options,i)),C&&C(k.value),j&&this.finish(),k}get duration(){const{resolved:t}=this;return t?ht(t.calculatedDuration):0}get time(){return ht(this.currentTime)}set time(t){t=ft(t),this.currentTime=t,this.holdTime!==null||this.speed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=ht(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:t=fw,onPlay:n,startTime:r}=this.options;this.driver||(this.driver=t(s=>this.tick(s))),n&&n();const i=this.driver.now();this.holdTime!==null?this.startTime=i-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=i):this.startTime=r??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(t=this.currentTime)!==null&&t!==void 0?t:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}const mw=new Set(["opacity","clipPath","filter","transform"]);function gw(e,t,n,{delay:r=0,duration:i=300,repeat:s=0,repeatType:o="loop",ease:a="easeInOut",times:l}={}){const u={[t]:n};l&&(u.offset=l);const c=um(a,i);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:o==="reverse"?"alternate":"normal"})}const yw=ru(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Ls=10,vw=2e4;function xw(e){return vu(e.type)||e.type==="spring"||!lm(e.ease)}function ww(e,t){const n=new Ru({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0});let r={done:!1,value:e[0]};const i=[];let s=0;for(;!r.done&&s<vw;)r=n.sample(s),i.push(r.value),s+=Ls;return{times:void 0,keyframes:i,duration:s-Ls,ease:"linear"}}const bm={anticipate:Sm,backInOut:wm,circInOut:Cm};function Sw(e){return e in bm}class kd extends Fm{constructor(t){super(t);const{name:n,motionValue:r,element:i,keyframes:s}=this.options;this.resolver=new Om(s,(o,a)=>this.onKeyframesResolved(o,a),n,r,i),this.resolver.scheduleResolve()}initPlayback(t,n){let{duration:r=300,times:i,ease:s,type:o,motionValue:a,name:l,startTime:u}=this.options;if(!a.owner||!a.owner.current)return!1;if(typeof s=="string"&&As()&&Sw(s)&&(s=bm[s]),xw(this.options)){const{onComplete:d,onUpdate:h,motionValue:y,element:v,...x}=this.options,C=ww(t,x);t=C.keyframes,t.length===1&&(t[1]=t[0]),r=C.duration,i=C.times,s=C.ease,o="keyframes"}const c=gw(a.owner.current,l,t,{...this.options,duration:r,times:i,ease:s});return c.startTime=u??this.calcStartTime(),this.pendingTimeline?(sd(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{const{onComplete:d}=this.options;a.set(no(t,this.options,n)),d&&d(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:r,times:i,type:o,ease:s,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:n}=t;return ht(n)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:n}=t;return ht(n.currentTime||0)}set time(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.currentTime=ft(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:n}=t;return n.playbackRate}set speed(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:n}=t;return n.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:n}=t;return n.startTime}attachTimeline(t){if(!this._resolved)this.pendingTimeline=t;else{const{resolved:n}=this;if(!n)return Le;const{animation:r}=n;sd(r,t)}return Le}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:n,keyframes:r,duration:i,type:s,ease:o,times:a}=t;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:u,onUpdate:c,onComplete:d,element:h,...y}=this.options,v=new Ru({...y,keyframes:r,duration:i,type:s,ease:o,times:a,isGenerator:!0}),x=ft(this.time);u.setWithVelocity(v.sample(x-Ls).value,v.sample(x).value,Ls)}const{onStop:l}=this.options;l&&l(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:n,name:r,repeatDelay:i,repeatType:s,damping:o,type:a}=t;if(!n||!n.owner||!(n.owner.current instanceof HTMLElement))return!1;const{onUpdate:l,transformTemplate:u}=n.owner.getProps();return yw()&&r&&mw.has(r)&&!l&&!u&&!i&&s!=="mirror"&&o!==0&&a!=="inertia"}}const kw={type:"spring",stiffness:500,damping:25,restSpeed:10},Cw=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),Pw={type:"keyframes",duration:.8},Ew={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Tw=(e,{keyframes:t})=>t.length>2?Pw:vn.has(e)?e.startsWith("scale")?Cw(t[1]):kw:Ew;function jw({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}const Lu=(e,t,n,r={},i,s)=>o=>{const a=yu(r,e)||{},l=a.delay||r.delay||0;let{elapsed:u=0}=r;u=u-ft(l);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-u,onUpdate:h=>{t.set(h),a.onUpdate&&a.onUpdate(h)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:s?void 0:i};jw(a)||(c={...c,...Tw(e,c)}),c.duration&&(c.duration=ft(c.duration)),c.repeatDelay&&(c.repeatDelay=ft(c.repeatDelay)),c.from!==void 0&&(c.keyframes[0]=c.from);let d=!1;if((c.type===!1||c.duration===0&&!c.repeatDelay)&&(c.duration=0,c.delay===0&&(d=!0)),d&&!s&&t.get()!==void 0){const h=no(c.keyframes,a);if(h!==void 0)return $.update(()=>{c.onUpdate(h),c.onComplete()}),new G1([])}return!s&&kd.supports(c)?new kd(c):new Ru(c)};function Nw({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function Hm(e,t,{delay:n=0,transitionOverride:r,type:i}={}){var s;let{transition:o=e.getDefaultTransition(),transitionEnd:a,...l}=t;r&&(o=r);const u=[],c=i&&e.animationState&&e.animationState.getState()[i];for(const d in l){const h=e.getValue(d,(s=e.latestValues[d])!==null&&s!==void 0?s:null),y=l[d];if(y===void 0||c&&Nw(c,d))continue;const v={delay:n,...yu(o||{},d)};let x=!1;if(window.MotionHandoffAnimation){const p=mm(e);if(p){const m=window.MotionHandoffAnimation(p,d,$);m!==null&&(v.startTime=m,x=!0)}}ba(e,d),h.start(Lu(d,h,y,e.shouldReduceMotion&&hm.has(d)?{type:!1}:v,e,x));const C=h.animation;C&&u.push(C)}return a&&Promise.all(u).then(()=>{$.update(()=>{a&&ax(e,a)})}),u}function Za(e,t,n={}){var r;const i=to(e,t,n.type==="exit"?(r=e.presenceContext)===null||r===void 0?void 0:r.custom:void 0);let{transition:s=e.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(s=n.transitionOverride);const o=i?()=>Promise.all(Hm(e,i,n)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(u=0)=>{const{delayChildren:c=0,staggerChildren:d,staggerDirection:h}=s;return Aw(e,t,c+u,d,h,n)}:()=>Promise.resolve(),{when:l}=s;if(l){const[u,c]=l==="beforeChildren"?[o,a]:[a,o];return u().then(()=>c())}else return Promise.all([o(),a(n.delay)])}function Aw(e,t,n=0,r=0,i=1,s){const o=[],a=(e.variantChildren.size-1)*r,l=i===1?(u=0)=>u*r:(u=0)=>a-u*r;return Array.from(e.variantChildren).sort(Mw).forEach((u,c)=>{u.notify("AnimationStart",t),o.push(Za(u,t,{...s,delay:n+l(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(o)}function Mw(e,t){return e.sortNodePosition(t)}function Rw(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(s=>Za(e,s,n));r=Promise.all(i)}else if(typeof t=="string")r=Za(e,t,n);else{const i=typeof t=="function"?to(e,t,n.custom):t;r=Promise.all(Hm(e,i,n))}return r.then(()=>{e.notify("AnimationComplete",t)})}const Lw=su.length;function Km(e){if(!e)return;if(!e.isControllingVariants){const n=e.parent?Km(e.parent)||{}:{};return e.props.initial!==void 0&&(n.initial=e.props.initial),n}const t={};for(let n=0;n<Lw;n++){const r=su[n],i=e.props[r];(ei(i)||i===!1)&&(t[r]=i)}return t}const Dw=[...iu].reverse(),Vw=iu.length;function _w(e){return t=>Promise.all(t.map(({animation:n,options:r})=>Rw(e,n,r)))}function Iw(e){let t=_w(e),n=Cd(),r=!0;const i=l=>(u,c)=>{var d;const h=to(e,c,l==="exit"?(d=e.presenceContext)===null||d===void 0?void 0:d.custom:void 0);if(h){const{transition:y,transitionEnd:v,...x}=h;u={...u,...x,...v}}return u};function s(l){t=l(e)}function o(l){const{props:u}=e,c=Km(e.parent)||{},d=[],h=new Set;let y={},v=1/0;for(let C=0;C<Vw;C++){const p=Dw[C],m=n[p],g=u[p]!==void 0?u[p]:c[p],w=ei(g),k=p===l?m.isActive:null;k===!1&&(v=C);let T=g===c[p]&&g!==u[p]&&w;if(T&&r&&e.manuallyAnimateOnMount&&(T=!1),m.protectedKeys={...y},!m.isActive&&k===null||!g&&!m.prevProp||Js(g)||typeof g=="boolean")continue;const j=Ow(m.prevProp,g);let P=j||p===l&&m.isActive&&!T&&w||C>v&&w,V=!1;const R=Array.isArray(g)?g:[g];let oe=R.reduce(i(p),{});k===!1&&(oe={});const{prevResolvedValues:wt={}}=m,Gt={...wt,...oe},sr=re=>{P=!0,h.has(re)&&(V=!0,h.delete(re)),m.needsAnimating[re]=!0;const N=e.getValue(re);N&&(N.liveStyle=!1)};for(const re in Gt){const N=oe[re],L=wt[re];if(y.hasOwnProperty(re))continue;let D=!1;Ua(N)&&Ua(L)?D=!sm(N,L):D=N!==L,D?N!=null?sr(re):h.add(re):N!==void 0&&h.has(re)?sr(re):m.protectedKeys[re]=!0}m.prevProp=g,m.prevResolvedValues=oe,m.isActive&&(y={...y,...oe}),r&&e.blockInitialAnimation&&(P=!1),P&&(!(T&&j)||V)&&d.push(...R.map(re=>({animation:re,options:{type:p}})))}if(h.size){const C={};h.forEach(p=>{const m=e.getBaseTarget(p),g=e.getValue(p);g&&(g.liveStyle=!0),C[p]=m??null}),d.push({animation:C})}let x=!!d.length;return r&&(u.initial===!1||u.initial===u.animate)&&!e.manuallyAnimateOnMount&&(x=!1),r=!1,x?t(d):Promise.resolve()}function a(l,u){var c;if(n[l].isActive===u)return Promise.resolve();(c=e.variantChildren)===null||c===void 0||c.forEach(h=>{var y;return(y=h.animationState)===null||y===void 0?void 0:y.setActive(l,u)}),n[l].isActive=u;const d=o(l);for(const h in n)n[h].protectedKeys={};return d}return{animateChanges:o,setActive:a,setAnimateFunction:s,getState:()=>n,reset:()=>{n=Cd(),r=!0}}}function Ow(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!sm(t,e):!1}function Xt(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Cd(){return{animate:Xt(!0),whileInView:Xt(),whileHover:Xt(),whileTap:Xt(),whileDrag:Xt(),whileFocus:Xt(),exit:Xt()}}class Kt{constructor(t){this.isMounted=!1,this.node=t}update(){}}class Fw extends Kt{constructor(t){super(t),t.animationState||(t.animationState=Iw(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();Js(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)===null||t===void 0||t.call(this)}}let zw=0;class Bw extends Kt{constructor(){super(...arguments),this.id=zw++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;const i=this.node.animationState.setActive("exit",!t);n&&!t&&i.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const Uw={animation:{Feature:Fw},exit:{Feature:Bw}};function ii(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function mi(e){return{point:{x:e.pageX,y:e.pageY}}}const $w=e=>t=>wu(t)&&e(t,mi(t));function Lr(e,t,n,r){return ii(e,t,$w(n),r)}const Pd=(e,t)=>Math.abs(e-t);function Ww(e,t){const n=Pd(e.x,t.x),r=Pd(e.y,t.y);return Math.sqrt(n**2+r**2)}class Gm{constructor(t,n,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const d=zo(this.lastMoveEventInfo,this.history),h=this.startEvent!==null,y=Ww(d.offset,{x:0,y:0})>=3;if(!h&&!y)return;const{point:v}=d,{timestamp:x}=de;this.history.push({...v,timestamp:x});const{onStart:C,onMove:p}=this.handlers;h||(C&&C(this.lastMoveEvent,d),this.startEvent=this.lastMoveEvent),p&&p(this.lastMoveEvent,d)},this.handlePointerMove=(d,h)=>{this.lastMoveEvent=d,this.lastMoveEventInfo=Fo(h,this.transformPagePoint),$.update(this.updatePoint,!0)},this.handlePointerUp=(d,h)=>{this.end();const{onEnd:y,onSessionEnd:v,resumeAnimation:x}=this.handlers;if(this.dragSnapToOrigin&&x&&x(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const C=zo(d.type==="pointercancel"?this.lastMoveEventInfo:Fo(h,this.transformPagePoint),this.history);this.startEvent&&y&&y(d,C),v&&v(d,C)},!wu(t))return;this.dragSnapToOrigin=s,this.handlers=n,this.transformPagePoint=r,this.contextWindow=i||window;const o=mi(t),a=Fo(o,this.transformPagePoint),{point:l}=a,{timestamp:u}=de;this.history=[{...l,timestamp:u}];const{onSessionStart:c}=n;c&&c(t,zo(a,this.history)),this.removeListeners=pi(Lr(this.contextWindow,"pointermove",this.handlePointerMove),Lr(this.contextWindow,"pointerup",this.handlePointerUp),Lr(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Ut(this.updatePoint)}}function Fo(e,t){return t?{point:t(e.point)}:e}function Ed(e,t){return{x:e.x-t.x,y:e.y-t.y}}function zo({point:e},t){return{point:e,delta:Ed(e,Qm(t)),offset:Ed(e,bw(t)),velocity:Hw(t,.1)}}function bw(e){return e[0]}function Qm(e){return e[e.length-1]}function Hw(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=Qm(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>ft(t)));)n--;if(!r)return{x:0,y:0};const s=ht(i.timestamp-r.timestamp);if(s===0)return{x:0,y:0};const o={x:(i.x-r.x)/s,y:(i.y-r.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}const Ym=1e-4,Kw=1-Ym,Gw=1+Ym,Xm=.01,Qw=0-Xm,Yw=0+Xm;function Ve(e){return e.max-e.min}function Xw(e,t,n){return Math.abs(e-t)<=n}function Td(e,t,n,r=.5){e.origin=r,e.originPoint=K(t.min,t.max,e.origin),e.scale=Ve(n)/Ve(t),e.translate=K(n.min,n.max,e.origin)-e.originPoint,(e.scale>=Kw&&e.scale<=Gw||isNaN(e.scale))&&(e.scale=1),(e.translate>=Qw&&e.translate<=Yw||isNaN(e.translate))&&(e.translate=0)}function Dr(e,t,n,r){Td(e.x,t.x,n.x,r?r.originX:void 0),Td(e.y,t.y,n.y,r?r.originY:void 0)}function jd(e,t,n){e.min=n.min+t.min,e.max=e.min+Ve(t)}function Zw(e,t,n){jd(e.x,t.x,n.x),jd(e.y,t.y,n.y)}function Nd(e,t,n){e.min=t.min-n.min,e.max=e.min+Ve(t)}function Vr(e,t,n){Nd(e.x,t.x,n.x),Nd(e.y,t.y,n.y)}function qw(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?K(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?K(n,e,r.max):Math.min(e,n)),e}function Ad(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function Jw(e,{top:t,left:n,bottom:r,right:i}){return{x:Ad(e.x,n,i),y:Ad(e.y,t,r)}}function Md(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function eS(e,t){return{x:Md(e.x,t.x),y:Md(e.y,t.y)}}function tS(e,t){let n=.5;const r=Ve(e),i=Ve(t);return i>r?n=Xn(t.min,t.max-r,e.min):r>i&&(n=Xn(e.min,e.max-i,t.min)),vt(0,1,n)}function nS(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const qa=.35;function rS(e=qa){return e===!1?e=0:e===!0&&(e=qa),{x:Rd(e,"left","right"),y:Rd(e,"top","bottom")}}function Rd(e,t,n){return{min:Ld(e,t),max:Ld(e,n)}}function Ld(e,t){return typeof e=="number"?e:e[t]||0}const Dd=()=>({translate:0,scale:1,origin:0,originPoint:0}),Vn=()=>({x:Dd(),y:Dd()}),Vd=()=>({min:0,max:0}),q=()=>({x:Vd(),y:Vd()});function Fe(e){return[e("x"),e("y")]}function Zm({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function iS({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function sS(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function Bo(e){return e===void 0||e===1}function Ja({scale:e,scaleX:t,scaleY:n}){return!Bo(e)||!Bo(t)||!Bo(n)}function Jt(e){return Ja(e)||qm(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function qm(e){return _d(e.x)||_d(e.y)}function _d(e){return e&&e!=="0%"}function Ds(e,t,n){const r=e-n,i=t*r;return n+i}function Id(e,t,n,r,i){return i!==void 0&&(e=Ds(e,i,r)),Ds(e,n,r)+t}function el(e,t=0,n=1,r,i){e.min=Id(e.min,t,n,r,i),e.max=Id(e.max,t,n,r,i)}function Jm(e,{x:t,y:n}){el(e.x,t.translate,t.scale,t.originPoint),el(e.y,n.translate,n.scale,n.originPoint)}const Od=.999999999999,Fd=1.0000000000001;function oS(e,t,n,r=!1){const i=n.length;if(!i)return;t.x=t.y=1;let s,o;for(let a=0;a<i;a++){s=n[a],o=s.projectionDelta;const{visualElement:l}=s.options;l&&l.props.style&&l.props.style.display==="contents"||(r&&s.options.layoutScroll&&s.scroll&&s!==s.root&&In(e,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,Jm(e,o)),r&&Jt(s.latestValues)&&In(e,s.latestValues))}t.x<Fd&&t.x>Od&&(t.x=1),t.y<Fd&&t.y>Od&&(t.y=1)}function _n(e,t){e.min=e.min+t,e.max=e.max+t}function zd(e,t,n,r,i=.5){const s=K(e.min,e.max,i);el(e,t,n,s,r)}function In(e,t){zd(e.x,t.x,t.scaleX,t.scale,t.originX),zd(e.y,t.y,t.scaleY,t.scale,t.originY)}function eg(e,t){return Zm(sS(e.getBoundingClientRect(),t))}function aS(e,t,n){const r=eg(e,n),{scroll:i}=t;return i&&(_n(r.x,i.offset.x),_n(r.y,i.offset.y)),r}const tg=({current:e})=>e?e.ownerDocument.defaultView:null,lS=new WeakMap;class uS{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=q(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const i=c=>{const{dragSnapToOrigin:d}=this.getProps();d?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(mi(c).point)},s=(c,d)=>{const{drag:h,dragPropagation:y,onDragStart:v}=this.getProps();if(h&&!y&&(this.openDragLock&&this.openDragLock(),this.openDragLock=nx(h),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Fe(C=>{let p=this.getAxisMotionValue(C).get()||0;if(it.test(p)){const{projection:m}=this.visualElement;if(m&&m.layout){const g=m.layout.layoutBox[C];g&&(p=Ve(g)*(parseFloat(p)/100))}}this.originPoint[C]=p}),v&&$.postRender(()=>v(c,d)),ba(this.visualElement,"transform");const{animationState:x}=this.visualElement;x&&x.setActive("whileDrag",!0)},o=(c,d)=>{const{dragPropagation:h,dragDirectionLock:y,onDirectionLock:v,onDrag:x}=this.getProps();if(!h&&!this.openDragLock)return;const{offset:C}=d;if(y&&this.currentDirection===null){this.currentDirection=cS(C),this.currentDirection!==null&&v&&v(this.currentDirection);return}this.updateAxis("x",d.point,C),this.updateAxis("y",d.point,C),this.visualElement.render(),x&&x(c,d)},a=(c,d)=>this.stop(c,d),l=()=>Fe(c=>{var d;return this.getAnimationState(c)==="paused"&&((d=this.getAxisMotionValue(c).animation)===null||d===void 0?void 0:d.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new Gm(t,{onSessionStart:i,onStart:s,onMove:o,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:tg(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&$.postRender(()=>s(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!zi(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(o=qw(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,s=this.constraints;n&&Ln(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=Jw(i.layoutBox,n):this.constraints=!1,this.elastic=rS(r),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&Fe(o=>{this.constraints!==!1&&this.getAxisMotionValue(o)&&(this.constraints[o]=nS(i.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!Ln(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=aS(r,i.root,this.visualElement.getTransformPagePoint());let o=eS(i.layout.layoutBox,s);if(n){const a=n(iS(o));this.hasMutatedConstraints=!!a,a&&(o=Zm(a))}return o}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=Fe(c=>{if(!zi(c,n,this.currentDirection))return;let d=l&&l[c]||{};o&&(d={min:0,max:0});const h=i?200:1e6,y=i?40:1e7,v={type:"inertia",velocity:r?t[c]:0,bounceStiffness:h,bounceDamping:y,timeConstant:750,restDelta:1,restSpeed:10,...s,...d};return this.startAxisValueAnimation(c,v)});return Promise.all(u).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return ba(this.visualElement,t),r.start(Lu(t,r,0,n,this.visualElement,!1))}stopAnimation(){Fe(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Fe(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps(),i=r[n];return i||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){Fe(n=>{const{drag:r}=this.getProps();if(!zi(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(n);if(i&&i.layout){const{min:o,max:a}=i.layout.layoutBox[n];s.set(t[n]-K(o,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!Ln(n)||!r||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Fe(o=>{const a=this.getAxisMotionValue(o);if(a&&this.constraints!==!1){const l=a.get();i[o]=tS({min:l,max:l},this.constraints[o])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Fe(o=>{if(!zi(o,t,null))return;const a=this.getAxisMotionValue(o),{min:l,max:u}=this.constraints[o];a.set(K(l,u,i[o]))})}addListeners(){if(!this.visualElement.current)return;lS.set(this.visualElement,this);const t=this.visualElement.current,n=Lr(t,"pointerdown",l=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(l)}),r=()=>{const{dragConstraints:l}=this.getProps();Ln(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),$.read(r);const o=ii(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(Fe(c=>{const d=this.getAxisMotionValue(c);d&&(this.originPoint[c]+=l[c].translate,d.set(d.get()+l[c].translate))}),this.visualElement.render())});return()=>{o(),n(),s(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=qa,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:a}}}function zi(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function cS(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class dS extends Kt{constructor(t){super(t),this.removeGroupControls=Le,this.removeListeners=Le,this.controls=new uS(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Le}unmount(){this.removeGroupControls(),this.removeListeners()}}const Bd=e=>(t,n)=>{e&&$.postRender(()=>e(t,n))};class fS extends Kt{constructor(){super(...arguments),this.removePointerDownListener=Le}onPointerDown(t){this.session=new Gm(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:tg(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:Bd(t),onStart:Bd(n),onMove:r,onEnd:(s,o)=>{delete this.session,i&&$.postRender(()=>i(s,o))}}}mount(){this.removePointerDownListener=Lr(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const es={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Ud(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const pr={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(M.test(e))e=parseFloat(e);else return e;const n=Ud(e,t.target.x),r=Ud(e,t.target.y);return`${n}% ${r}%`}},hS={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=$t.parse(e);if(i.length>5)return r;const s=$t.createTransformer(e),o=typeof i[0]!="number"?1:0,a=n.x.scale*t.x,l=n.y.scale*t.y;i[0+o]/=a,i[1+o]/=l;const u=K(a,l,.5);return typeof i[2+o]=="number"&&(i[2+o]/=u),typeof i[3+o]=="number"&&(i[3+o]/=u),s(i)}};class pS extends S.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:s}=t;_1(mS),s&&(n.group&&n.group.add(s),r&&r.register&&i&&r.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),es.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:s}=this.props,o=r.projection;return o&&(o.isPresent=s,i||t.layoutDependency!==n||n===void 0?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||$.postRender(()=>{const a=o.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),au.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function ng(e){const[t,n]=zp(),r=S.useContext(Jl);return f.jsx(pS,{...e,layoutGroup:r,switchLayoutGroup:S.useContext(Kp),isPresent:t,safeToRemove:n})}const mS={borderRadius:{...pr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:pr,borderTopRightRadius:pr,borderBottomLeftRadius:pr,borderBottomRightRadius:pr,boxShadow:hS};function gS(e,t,n){const r=ve(e)?e:ni(e);return r.start(Lu("",r,t,n)),r.animation}function yS(e){return e instanceof SVGElement&&e.tagName!=="svg"}const vS=(e,t)=>e.depth-t.depth;class xS{constructor(){this.children=[],this.isDirty=!1}add(t){Su(this.children,t),this.isDirty=!0}remove(t){ku(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(vS),this.isDirty=!1,this.children.forEach(t)}}function wS(e,t){const n=st.now(),r=({timestamp:i})=>{const s=i-n;s>=t&&(Ut(r),e(s-t))};return $.read(r,!0),()=>Ut(r)}const rg=["TopLeft","TopRight","BottomLeft","BottomRight"],SS=rg.length,$d=e=>typeof e=="string"?parseFloat(e):e,Wd=e=>typeof e=="number"||M.test(e);function kS(e,t,n,r,i,s){i?(e.opacity=K(0,n.opacity!==void 0?n.opacity:1,CS(r)),e.opacityExit=K(t.opacity!==void 0?t.opacity:1,0,PS(r))):s&&(e.opacity=K(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let o=0;o<SS;o++){const a=`border${rg[o]}Radius`;let l=bd(t,a),u=bd(n,a);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||Wd(l)===Wd(u)?(e[a]=Math.max(K($d(l),$d(u),r),0),(it.test(u)||it.test(l))&&(e[a]+="%")):e[a]=u}(t.rotate||n.rotate)&&(e.rotate=K(t.rotate||0,n.rotate||0,r))}function bd(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const CS=ig(0,.5,km),PS=ig(.5,.95,Le);function ig(e,t,n){return r=>r<e?0:r>t?1:n(Xn(e,t,r))}function Hd(e,t){e.min=t.min,e.max=t.max}function Oe(e,t){Hd(e.x,t.x),Hd(e.y,t.y)}function Kd(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function Gd(e,t,n,r,i){return e-=t,e=Ds(e,1/n,r),i!==void 0&&(e=Ds(e,1/i,r)),e}function ES(e,t=0,n=1,r=.5,i,s=e,o=e){if(it.test(t)&&(t=parseFloat(t),t=K(o.min,o.max,t/100)-o.min),typeof t!="number")return;let a=K(s.min,s.max,r);e===s&&(a-=t),e.min=Gd(e.min,t,n,a,i),e.max=Gd(e.max,t,n,a,i)}function Qd(e,t,[n,r,i],s,o){ES(e,t[n],t[r],t[i],t.scale,s,o)}const TS=["x","scaleX","originX"],jS=["y","scaleY","originY"];function Yd(e,t,n,r){Qd(e.x,t,TS,n?n.x:void 0,r?r.x:void 0),Qd(e.y,t,jS,n?n.y:void 0,r?r.y:void 0)}function Xd(e){return e.translate===0&&e.scale===1}function sg(e){return Xd(e.x)&&Xd(e.y)}function Zd(e,t){return e.min===t.min&&e.max===t.max}function NS(e,t){return Zd(e.x,t.x)&&Zd(e.y,t.y)}function qd(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function og(e,t){return qd(e.x,t.x)&&qd(e.y,t.y)}function Jd(e){return Ve(e.x)/Ve(e.y)}function ef(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class AS{constructor(){this.members=[]}add(t){Su(this.members,t),t.scheduleRender()}remove(t){if(ku(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const s=this.members[i];if(s.isPresent!==!1){r=s;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;i===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function MS(e,t,n){let r="";const i=e.x.translate/t.x,s=e.y.translate/t.y,o=(n==null?void 0:n.z)||0;if((i||s||o)&&(r=`translate3d(${i}px, ${s}px, ${o}px) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{transformPerspective:u,rotate:c,rotateX:d,rotateY:h,skewX:y,skewY:v}=n;u&&(r=`perspective(${u}px) ${r}`),c&&(r+=`rotate(${c}deg) `),d&&(r+=`rotateX(${d}deg) `),h&&(r+=`rotateY(${h}deg) `),y&&(r+=`skewX(${y}deg) `),v&&(r+=`skewY(${v}deg) `)}const a=e.x.scale*t.x,l=e.y.scale*t.y;return(a!==1||l!==1)&&(r+=`scale(${a}, ${l})`),r||"none"}const en={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},Sr=typeof window<"u"&&window.MotionDebug!==void 0,Uo=["","X","Y","Z"],RS={visibility:"hidden"},tf=1e3;let LS=0;function $o(e,t,n,r){const{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function ag(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=mm(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:i,layoutId:s}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",$,!(i||s))}const{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&ag(r)}function lg({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(o={},a=t==null?void 0:t()){this.id=LS++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,Sr&&(en.totalNodes=en.resolvedTargetDeltas=en.recalculatedProjection=0),this.nodes.forEach(_S),this.nodes.forEach(BS),this.nodes.forEach(US),this.nodes.forEach(IS),Sr&&window.MotionDebug.record(en)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new xS)}addEventListener(o,a){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new Cu),this.eventHandlers.get(o).add(a)}notifyListeners(o,...a){const l=this.eventHandlers.get(o);l&&l.notify(...a)}hasListeners(o){return this.eventHandlers.has(o)}mount(o,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=yS(o),this.instance=o;const{layoutId:l,layout:u,visualElement:c}=this.options;if(c&&!c.current&&c.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(u||l)&&(this.isLayoutDirty=!0),e){let d;const h=()=>this.root.updateBlockedByResize=!1;e(o,()=>{this.root.updateBlockedByResize=!0,d&&d(),d=wS(h,250),es.hasAnimatedSinceResize&&(es.hasAnimatedSinceResize=!1,this.nodes.forEach(rf))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&c&&(l||u)&&this.addEventListener("didUpdate",({delta:d,hasLayoutChanged:h,hasRelativeTargetChanged:y,layout:v})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const x=this.options.transition||c.getDefaultTransition()||KS,{onLayoutAnimationStart:C,onLayoutAnimationComplete:p}=c.getProps(),m=!this.targetLayout||!og(this.targetLayout,v)||y,g=!h&&y;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||g||h&&(m||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(d,g);const w={...yu(x,"layout"),onPlay:C,onComplete:p};(c.shouldReduceMotion||this.options.layoutRoot)&&(w.delay=0,w.type=!1),this.startAnimation(w)}else h||rf(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=v})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Ut(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach($S),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&ag(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const d=this.path[c];d.shouldResetTransform=!0,d.updateScroll("snapshot"),d.options.layoutRoot&&d.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nf);return}this.isUpdating||this.nodes.forEach(FS),this.isUpdating=!1,this.nodes.forEach(zS),this.nodes.forEach(DS),this.nodes.forEach(VS),this.clearAllSnapshots();const a=st.now();de.delta=vt(0,1e3/60,a-de.timestamp),de.timestamp=a,de.isProcessing=!0,Lo.update.process(de),Lo.preRender.process(de),Lo.render.process(de),de.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,au.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(OS),this.sharedNodes.forEach(WS)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,$.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){$.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=q(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(a=!1),a){const l=r(this.instance);this.scroll={animationId:this.root.animationId,phase:o,isRoot:l,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!i)return;const o=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!sg(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;o&&(a||Jt(this.latestValues)||c)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return o&&(l=this.removeTransform(l)),GS(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var o;const{visualElement:a}=this.options;if(!a)return q();const l=a.measureViewportBox();if(!(((o=this.scroll)===null||o===void 0?void 0:o.wasRoot)||this.path.some(QS))){const{scroll:c}=this.root;c&&(_n(l.x,c.offset.x),_n(l.y,c.offset.y))}return l}removeElementScroll(o){var a;const l=q();if(Oe(l,o),!((a=this.scroll)===null||a===void 0)&&a.wasRoot)return l;for(let u=0;u<this.path.length;u++){const c=this.path[u],{scroll:d,options:h}=c;c!==this.root&&d&&h.layoutScroll&&(d.wasRoot&&Oe(l,o),_n(l.x,d.offset.x),_n(l.y,d.offset.y))}return l}applyTransform(o,a=!1){const l=q();Oe(l,o);for(let u=0;u<this.path.length;u++){const c=this.path[u];!a&&c.options.layoutScroll&&c.scroll&&c!==c.root&&In(l,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),Jt(c.latestValues)&&In(l,c.latestValues)}return Jt(this.latestValues)&&In(l,this.latestValues),l}removeTransform(o){const a=q();Oe(a,o);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!Jt(u.latestValues))continue;Ja(u.latestValues)&&u.updateSnapshot();const c=q(),d=u.measurePageBox();Oe(c,d),Yd(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return Jt(this.latestValues)&&Yd(a,this.latestValues),a}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==de.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==l;if(!(o||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:d,layoutId:h}=this.options;if(!(!this.layout||!(d||h))){if(this.resolvedRelativeTargetAt=de.timestamp,!this.targetDelta&&!this.relativeTarget){const y=this.getClosestProjectingParent();y&&y.layout&&this.animationProgress!==1?(this.relativeParent=y,this.forceRelativeParentToResolveTarget(),this.relativeTarget=q(),this.relativeTargetOrigin=q(),Vr(this.relativeTargetOrigin,this.layout.layoutBox,y.layout.layoutBox),Oe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=q(),this.targetWithTransforms=q()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Zw(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Oe(this.target,this.layout.layoutBox),Jm(this.target,this.targetDelta)):Oe(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const y=this.getClosestProjectingParent();y&&!!y.resumingFrom==!!this.resumingFrom&&!y.options.layoutScroll&&y.target&&this.animationProgress!==1?(this.relativeParent=y,this.forceRelativeParentToResolveTarget(),this.relativeTarget=q(),this.relativeTargetOrigin=q(),Vr(this.relativeTargetOrigin,this.target,y.target),Oe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Sr&&en.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Ja(this.parent.latestValues)||qm(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var o;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let u=!0;if((this.isProjectionDirty||!((o=this.parent)===null||o===void 0)&&o.isProjectionDirty)&&(u=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===de.timestamp&&(u=!1),u)return;const{layout:c,layoutId:d}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||d))return;Oe(this.layoutCorrected,this.layout.layoutBox);const h=this.treeScale.x,y=this.treeScale.y;oS(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=q());const{target:v}=a;if(!v){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Kd(this.prevProjectionDelta.x,this.projectionDelta.x),Kd(this.prevProjectionDelta.y,this.projectionDelta.y)),Dr(this.projectionDelta,this.layoutCorrected,v,this.latestValues),(this.treeScale.x!==h||this.treeScale.y!==y||!ef(this.projectionDelta.x,this.prevProjectionDelta.x)||!ef(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",v)),Sr&&en.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){var a;if((a=this.options.visualElement)===null||a===void 0||a.scheduleRender(),o){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=Vn(),this.projectionDelta=Vn(),this.projectionDeltaWithTransform=Vn()}setAnimationOrigin(o,a=!1){const l=this.snapshot,u=l?l.latestValues:{},c={...this.latestValues},d=Vn();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const h=q(),y=l?l.source:void 0,v=this.layout?this.layout.source:void 0,x=y!==v,C=this.getStack(),p=!C||C.members.length<=1,m=!!(x&&!p&&this.options.crossfade===!0&&!this.path.some(HS));this.animationProgress=0;let g;this.mixTargetDelta=w=>{const k=w/1e3;sf(d.x,o.x,k),sf(d.y,o.y,k),this.setTargetDelta(d),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Vr(h,this.layout.layoutBox,this.relativeParent.layout.layoutBox),bS(this.relativeTarget,this.relativeTargetOrigin,h,k),g&&NS(this.relativeTarget,g)&&(this.isProjectionDirty=!1),g||(g=q()),Oe(g,this.relativeTarget)),x&&(this.animationValues=c,kS(c,u,this.latestValues,k,m,p)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=k},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Ut(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=$.update(()=>{es.hasAnimatedSinceResize=!0,this.currentAnimation=gS(0,tf,{...o,onUpdate:a=>{this.mixTargetDelta(a),o.onUpdate&&o.onUpdate(a)},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(tf),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:a,target:l,layout:u,latestValues:c}=o;if(!(!a||!l||!u)){if(this!==o&&this.layout&&u&&ug(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||q();const d=Ve(this.layout.layoutBox.x);l.x.min=o.target.x.min,l.x.max=l.x.min+d;const h=Ve(this.layout.layoutBox.y);l.y.min=o.target.y.min,l.y.max=l.y.min+h}Oe(a,l),In(a,c),Dr(this.projectionDeltaWithTransform,this.layoutCorrected,a,c)}}registerSharedNode(o,a){this.sharedNodes.has(o)||this.sharedNodes.set(o,new AS),this.sharedNodes.get(o).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var o;const{layoutId:a}=this.options;return a?((o=this.getStack())===null||o===void 0?void 0:o.lead)||this:this}getPrevLead(){var o;const{layoutId:a}=this.options;return a?(o=this.getStack())===null||o===void 0?void 0:o.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:a,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),o&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetSkewAndRotation(){const{visualElement:o}=this.options;if(!o)return;let a=!1;const{latestValues:l}=o;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const u={};l.z&&$o("z",o,u,this.animationValues);for(let c=0;c<Uo.length;c++)$o(`rotate${Uo[c]}`,o,u,this.animationValues),$o(`skew${Uo[c]}`,o,u,this.animationValues);o.render();for(const c in u)o.setStaticValue(c,u[c]),this.animationValues&&(this.animationValues[c]=u[c]);o.scheduleRender()}getProjectionStyles(o){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return RS;const u={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=qi(o==null?void 0:o.pointerEvents)||"",u.transform=c?c(this.latestValues,""):"none",u;const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){const x={};return this.options.layoutId&&(x.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,x.pointerEvents=qi(o==null?void 0:o.pointerEvents)||""),this.hasProjected&&!Jt(this.latestValues)&&(x.transform=c?c({},""):"none",this.hasProjected=!1),x}const h=d.animationValues||d.latestValues;this.applyTransformsToTarget(),u.transform=MS(this.projectionDeltaWithTransform,this.treeScale,h),c&&(u.transform=c(h,u.transform));const{x:y,y:v}=this.projectionDelta;u.transformOrigin=`${y.origin*100}% ${v.origin*100}% 0`,d.animationValues?u.opacity=d===this?(l=(a=h.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:h.opacityExit:u.opacity=d===this?h.opacity!==void 0?h.opacity:"":h.opacityExit!==void 0?h.opacityExit:0;for(const x in Ns){if(h[x]===void 0)continue;const{correct:C,applyTo:p}=Ns[x],m=u.transform==="none"?h[x]:C(h[x],d);if(p){const g=p.length;for(let w=0;w<g;w++)u[p[w]]=m}else u[x]=m}return this.options.layoutId&&(u.pointerEvents=d===this?qi(o==null?void 0:o.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var a;return(a=o.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(nf),this.root.sharedNodes.clear()}}}function DS(e){e.updateLayout()}function VS(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:i}=e.layout,{animationType:s}=e.options,o=n.source!==e.layout.source;s==="size"?Fe(d=>{const h=o?n.measuredBox[d]:n.layoutBox[d],y=Ve(h);h.min=r[d].min,h.max=h.min+y}):ug(s,n.layoutBox,r)&&Fe(d=>{const h=o?n.measuredBox[d]:n.layoutBox[d],y=Ve(r[d]);h.max=h.min+y,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[d].max=e.relativeTarget[d].min+y)});const a=Vn();Dr(a,r,n.layoutBox);const l=Vn();o?Dr(l,e.applyTransform(i,!0),n.measuredBox):Dr(l,r,n.layoutBox);const u=!sg(a);let c=!1;if(!e.resumeFrom){const d=e.getClosestProjectingParent();if(d&&!d.resumeFrom){const{snapshot:h,layout:y}=d;if(h&&y){const v=q();Vr(v,n.layoutBox,h.layoutBox);const x=q();Vr(x,r,y.layoutBox),og(v,x)||(c=!0),d.options.layoutRoot&&(e.relativeTarget=x,e.relativeTargetOrigin=v,e.relativeParent=d)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:u,hasRelativeTargetChanged:c})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function _S(e){Sr&&en.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function IS(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function OS(e){e.clearSnapshot()}function nf(e){e.clearMeasurements()}function FS(e){e.isLayoutDirty=!1}function zS(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function rf(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function BS(e){e.resolveTargetDelta()}function US(e){e.calcProjection()}function $S(e){e.resetSkewAndRotation()}function WS(e){e.removeLeadSnapshot()}function sf(e,t,n){e.translate=K(t.translate,0,n),e.scale=K(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function of(e,t,n,r){e.min=K(t.min,n.min,r),e.max=K(t.max,n.max,r)}function bS(e,t,n,r){of(e.x,t.x,n.x,r),of(e.y,t.y,n.y,r)}function HS(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const KS={duration:.45,ease:[.4,0,.1,1]},af=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),lf=af("applewebkit/")&&!af("chrome/")?Math.round:Le;function uf(e){e.min=lf(e.min),e.max=lf(e.max)}function GS(e){uf(e.x),uf(e.y)}function ug(e,t,n){return e==="position"||e==="preserve-aspect"&&!Xw(Jd(t),Jd(n),.2)}function QS(e){var t;return e!==e.root&&((t=e.scroll)===null||t===void 0?void 0:t.wasRoot)}const YS=lg({attachResizeListener:(e,t)=>ii(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Wo={current:void 0},cg=lg({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Wo.current){const e=new YS({});e.mount(window),e.setOptions({layoutScroll:!0}),Wo.current=e}return Wo.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),XS={pan:{Feature:fS},drag:{Feature:dS,ProjectionNode:cg,MeasureLayout:ng}};function cf(e,t,n){const{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover",n==="Start");const i="onHover"+n,s=r[i];s&&$.postRender(()=>s(t,mi(t)))}class ZS extends Kt{mount(){const{current:t}=this.node;t&&(this.unmount=Z1(t,n=>(cf(this.node,n,"Start"),r=>cf(this.node,r,"End"))))}unmount(){}}class qS extends Kt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=pi(ii(this.node.current,"focus",()=>this.onFocus()),ii(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function df(e,t,n){const{props:r}=e;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap",n==="Start");const i="onTap"+(n==="End"?"":n),s=r[i];s&&$.postRender(()=>s(t,mi(t)))}class JS extends Kt{mount(){const{current:t}=this.node;t&&(this.unmount=tx(t,n=>(df(this.node,n,"Start"),(r,{success:i})=>df(this.node,r,i?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const tl=new WeakMap,bo=new WeakMap,e2=e=>{const t=tl.get(e.target);t&&t(e)},t2=e=>{e.forEach(e2)};function n2({root:e,...t}){const n=e||document;bo.has(n)||bo.set(n,{});const r=bo.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(t2,{root:e,...t})),r[i]}function r2(e,t,n){const r=n2(t);return tl.set(e,n),r.observe(e),()=>{tl.delete(e),r.unobserve(e)}}const i2={some:0,all:1};class s2 extends Kt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:i="some",once:s}=t,o={root:n?n.current:void 0,rootMargin:r,threshold:typeof i=="number"?i:i2[i]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,s&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:d}=this.node.getProps(),h=u?c:d;h&&h(l)};return r2(this.node.current,o,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(o2(t,n))&&this.startObserver()}unmount(){}}function o2({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const a2={inView:{Feature:s2},tap:{Feature:JS},focus:{Feature:qS},hover:{Feature:ZS}},l2={layout:{ProjectionNode:cg,MeasureLayout:ng}},nl={current:null},dg={current:!1};function u2(){if(dg.current=!0,!!nu)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>nl.current=e.matches;e.addListener(t),t()}else nl.current=!1}const c2=[...Im,ge,$t],d2=e=>c2.find(_m(e)),ff=new WeakMap;function f2(e,t,n){for(const r in t){const i=t[r],s=n[r];if(ve(i))e.addValue(r,i);else if(ve(s))e.addValue(r,ni(i,{owner:e}));else if(s!==i)if(e.hasValue(r)){const o=e.getValue(r);o.liveStyle===!0?o.jump(i):o.hasAnimated||o.set(i)}else{const o=e.getStaticValue(r);e.addValue(r,ni(o!==void 0?o:i,{owner:e}))}}for(const r in n)t[r]===void 0&&e.removeValue(r);return t}const hf=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class h2{scrapeMotionValuesFromProps(t,n,r){return{}}constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:i,blockInitialAnimation:s,visualState:o},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Au,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const y=st.now();this.renderScheduledAt<y&&(this.renderScheduledAt=y,$.render(this.render,!1,!0))};const{latestValues:l,renderState:u,onUpdate:c}=o;this.onUpdate=c,this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=u,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=a,this.blockInitialAnimation=!!s,this.isControllingVariants=eo(n),this.isVariantNode=bp(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:d,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const y in h){const v=h[y];l[y]!==void 0&&ve(v)&&v.set(l[y],!1)}}mount(t){this.current=t,ff.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),dg.current||u2(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:nl.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){ff.delete(this.current),this.projection&&this.projection.unmount(),Ut(this.notifyUpdate),Ut(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(t,n){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const r=vn.has(t),i=n.on("change",a=>{this.latestValues[t]=a,this.props.onUpdate&&$.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),s=n.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,n)),this.valueSubscriptions.set(t,()=>{i(),s(),o&&o(),n.owner&&n.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in Zn){const n=Zn[t];if(!n)continue;const{isEnabled:r,Feature:i}=n;if(!this.features[t]&&i&&r(this.props)&&(this.features[t]=new i(this)),this.features[t]){const s=this.features[t];s.isMounted?s.update():(s.mount(),s.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):q()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<hf.length;r++){const i=hf[r];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const s="on"+i,o=t[s];o&&(this.propEventSubscriptions[i]=this.on(i,o))}this.prevMotionValues=f2(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){const r=this.values.get(t);n!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,n),this.values.set(t,n),this.latestValues[t]=n.get())}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=ni(n===null?void 0:n,{owner:this}),this.addValue(t,r)),r}readValue(t,n){var r;let i=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(r=this.getBaseTargetFromProps(this.props,t))!==null&&r!==void 0?r:this.readValueFromInstance(this.current,t,this.options);return i!=null&&(typeof i=="string"&&(Dm(i)||Pm(i))?i=parseFloat(i):!d2(i)&&$t.test(n)&&(i=Mm(t,n)),this.setBaseTarget(t,ve(i)?i.get():i)),ve(i)?i.get():i}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props;let i;if(typeof r=="string"||typeof r=="object"){const o=uu(this.props,r,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);o&&(i=o[t])}if(r&&i!==void 0)return i;const s=this.getBaseTargetFromProps(this.props,t);return s!==void 0&&!ve(s)?s:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new Cu),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class fg extends h2{constructor(){super(...arguments),this.KeyframeResolver=Om}sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;ve(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function p2(e){return window.getComputedStyle(e)}class m2 extends fg{constructor(){super(...arguments),this.type="html",this.renderInstance=Jp}readValueFromInstance(t,n){if(vn.has(n)){const r=Nu(n);return r&&r.default||0}else{const r=p2(t),i=(Xp(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return eg(t,n)}build(t,n,r){fu(t,n,r.transformTemplate)}scrapeMotionValuesFromProps(t,n,r){return gu(t,n,r)}}class g2 extends fg{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=q}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(vn.has(n)){const r=Nu(n);return r&&r.default||0}return n=em.has(n)?n:ou(n),t.getAttribute(n)}scrapeMotionValuesFromProps(t,n,r){return rm(t,n,r)}build(t,n,r){hu(t,n,this.isSVGTag,r.transformTemplate)}renderInstance(t,n,r,i){tm(t,n,r,i)}mount(t){this.isSVGTag=mu(t.tagName),super.mount(t)}}const y2=(e,t)=>lu(e)?new g2(t):new m2(t,{allowProjection:e!==S.Fragment}),v2=b1({...Uw,...a2,...XS,...l2},y2),F=s1(v2);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var x2={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w2=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),ee=(e,t)=>{const n=S.forwardRef(({color:r="currentColor",size:i=24,strokeWidth:s=2,absoluteStrokeWidth:o,className:a="",children:l,...u},c)=>S.createElement("svg",{ref:c,...x2,width:i,height:i,stroke:r,strokeWidth:o?Number(s)*24/Number(i):s,className:["lucide",`lucide-${w2(e)}`,a].join(" "),...u},[...t.map(([d,h])=>S.createElement(d,h)),...Array.isArray(l)?l:[l]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S2=ee("BarChart2",[["line",{x1:"18",x2:"18",y1:"20",y2:"10",key:"1xfpm4"}],["line",{x1:"12",x2:"12",y1:"20",y2:"4",key:"be30l9"}],["line",{x1:"6",x2:"6",y1:"20",y2:"14",key:"1r4le6"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hg=ee("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const k2=ee("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C2=ee("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const P2=ee("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E2=ee("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const T2=ee("FileHeart",[["path",{d:"M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v2",key:"17k7jt"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10.29 10.7a2.43 2.43 0 0 0-2.66-.52c-.29.12-.56.3-.78.53l-.35.34-.35-.34a2.43 2.43 0 0 0-2.65-.53c-.3.12-.56.3-.79.53-.95.94-1 2.53.2 3.74L6.5 18l3.6-3.55c1.2-1.21 1.14-2.8.19-3.74Z",key:"1c1fso"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const j2=ee("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pg=ee("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N2=ee("MailCheck",[["path",{d:"M22 13V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v12c0 1.1.9 2 2 2h8",key:"12jkf8"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}],["path",{d:"m16 19 2 2 4-4",key:"1b14m6"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const A2=ee("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M2=ee("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R2=ee("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L2=ee("ReceiptText",[["path",{d:"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z",key:"q3az6g"}],["path",{d:"M14 8H8",key:"1l3xfs"}],["path",{d:"M16 12H8",key:"1fr5h0"}],["path",{d:"M13 16H8",key:"wsln4y"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D2=ee("SearchCheck",[["path",{d:"m8 11 2 2 4-4",key:"1sed1v"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const V2=ee("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _2=ee("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mg=ee("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const I2=ee("Waypoints",[["circle",{cx:"12",cy:"4.5",r:"2.5",key:"r5ysbb"}],["path",{d:"m10.2 6.3-3.9 3.9",key:"1nzqf6"}],["circle",{cx:"4.5",cy:"12",r:"2.5",key:"jydg6v"}],["path",{d:"M7 12h10",key:"b7w52i"}],["circle",{cx:"19.5",cy:"12",r:"2.5",key:"1piiel"}],["path",{d:"m13.8 17.7 3.9-3.9",key:"1wyg1y"}],["circle",{cx:"12",cy:"19.5",r:"2.5",key:"13o1pw"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gg=ee("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const O2=ee("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),F2=()=>{const[e,t]=S.useState(!1),[n,r]=S.useState(!1),i=di();S.useEffect(()=>{const o=()=>{r(window.scrollY>20)};return window.addEventListener("scroll",o),()=>window.removeEventListener("scroll",o)},[]);const s=[{name:"Features",path:"/features"},{name:"AI Agents",path:"/agents"},{name:"Advantages",path:"/advantages"},{name:"Pricing",path:"/pricing"},{name:"About",path:"/about"},{name:"Contact Us",path:"/contact"},{name:"Blog",path:"/blog"}];return f.jsx(F.nav,{initial:{y:-100},animate:{y:0},className:`fixed w-full z-50 transition-all duration-300 ${n?"bg-[var(--background)]/95 backdrop-blur-sm shadow-lg":""}`,children:f.jsxs("div",{className:"container mx-auto px-4",children:[f.jsxs("div",{className:"flex items-center justify-between h-20",children:[f.jsx(b,{to:"/",className:"text-2xl font-bold gradient-text",children:f.jsxs("div",{className:"flex flex-row items-center",children:[f.jsx(_2,{className:"mr-3 h-6 w-6 flex items-center text-purple-500 animate-spin"}),"siliconagent.ai"]})}),f.jsx("div",{className:"hidden md:flex items-center space-x-8",children:s.map(o=>f.jsx(b,{to:o.path,className:`text-sm font-medium transition-colors hover:text-[var(--primary)] ${i.pathname===o.path?"text-[var(--primary)]":"text-gray-300"}`,children:o.name},o.name))}),f.jsx("button",{className:"md:hidden text-gray-300 hover:text-white",onClick:()=>t(!e),children:e?f.jsx(gg,{size:24}):f.jsx(M2,{size:24})})]}),e&&f.jsx(F.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"md:hidden pb-6",children:s.map(o=>f.jsx(b,{to:o.path,className:`block py-2 text-sm font-medium ${i.pathname===o.path?"text-[var(--primary)]":"text-gray-300"}`,onClick:()=>t(!1),children:o.name},o.name))})]})})},z2=()=>{const e=new Date().getFullYear();return f.jsx("footer",{className:"bg-[var(--card)] mt-auto",children:f.jsxs("div",{className:"container py-12",children:[f.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[f.jsxs("div",{children:[f.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Product"}),f.jsxs("ul",{className:"space-y-2",children:[f.jsx("li",{children:f.jsx(b,{to:"/features",className:"text-gray-400 hover:text-white",children:"Features"})}),f.jsx("li",{children:f.jsx(b,{to:"/agents",className:"text-gray-400 hover:text-white",children:"AI Agents"})}),f.jsx("li",{children:f.jsx(b,{to:"/pricing",className:"text-gray-400 hover:text-white",children:"Pricing"})}),f.jsx("li",{children:f.jsx(b,{to:"/documentation",className:"text-gray-400 hover:text-white",children:"Documentation"})})]})]}),f.jsxs("div",{children:[f.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Company"}),f.jsxs("ul",{className:"space-y-2",children:[f.jsx("li",{children:f.jsx(b,{to:"/about",className:"text-gray-400 hover:text-white",children:"About"})}),f.jsx("li",{children:f.jsx(b,{to:"/blog",className:"text-gray-400 hover:text-white",children:"Blog"})}),f.jsx("li",{children:f.jsx(b,{to:"/careers",className:"text-gray-400 hover:text-white",children:"Careers"})}),f.jsx("li",{children:f.jsx(b,{to:"/contact",className:"text-gray-400 hover:text-white",children:"Contact"})})]})]}),f.jsxs("div",{children:[f.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Resources"}),f.jsxs("ul",{className:"space-y-2",children:[f.jsx("li",{children:f.jsx(b,{to:"/community",className:"text-gray-400 hover:text-white",children:"Community"})}),f.jsx("li",{children:f.jsx(b,{to:"/help-center",className:"text-gray-400 hover:text-white",children:"Help Center"})}),f.jsx("li",{children:f.jsx(b,{to:"/api-status",className:"text-gray-400 hover:text-white",children:"API Status"})}),f.jsx("li",{children:f.jsx(b,{to:"/partners",className:"text-gray-400 hover:text-white",children:"Partners"})})]})]}),f.jsxs("div",{children:[f.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Legal"}),f.jsxs("ul",{className:"space-y-2",children:[f.jsx("li",{children:f.jsx(b,{to:"/privacy",className:"text-gray-400 hover:text-white",children:"Privacy"})}),f.jsx("li",{children:f.jsx(b,{to:"/terms",className:"text-gray-400 hover:text-white",children:"Terms"})}),f.jsx("li",{children:f.jsx(b,{to:"/security",className:"text-gray-400 hover:text-white",children:"Security"})}),f.jsx("li",{children:f.jsx(b,{to:"/cookies",className:"text-gray-400 hover:text-white",children:"Cookies"})})]})]})]}),f.jsxs("div",{className:"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center",children:[f.jsxs("p",{className:"text-gray-400",children:["© ",e," siliconagent.ai. All rights reserved."]}),f.jsxs("div",{className:"flex space-x-4 mt-4 md:mt-0",children:[f.jsx("a",{href:"https://twitter.com",target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-white",children:f.jsx(mg,{size:20})}),f.jsx("a",{href:"https://github.com",target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-white",children:f.jsx(j2,{size:20})}),f.jsx("a",{href:"https://linkedin.com",target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-white",children:f.jsx(pg,{size:20})})]})]})]})})},B2=()=>f.jsxs(F.main,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:[f.jsxs("section",{className:"min-h-screen relative overflow-hidden flex items-center",children:[f.jsx("div",{className:"absolute inset-0 bg-gradient-to-b from-blue-500/10 to-purple-500/10",style:{backgroundImage:"url('https://images.unsplash.com/photo-1639762681485-074b7f938ba0?auto=format&fit=crop&q=80&w=2832')",backgroundSize:"cover",backgroundPosition:"center",backgroundBlendMode:"overlay",opacity:.2}}),f.jsx("div",{className:"container relative z-10",children:f.jsxs(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"max-w-6xl mx-auto text-center",children:[f.jsxs("h1",{className:"text-4xl md:text-6xl font-bold py mb-6",children:[f.jsx("p",{className:"mb-3",children:"Build, Deploy, and Automate"}),f.jsx("span",{className:"gradient-text",children:"with AI Agents"})]}),f.jsx("p",{className:"text-lg md:text-xl text-gray-300 mb-8",children:"Empower your workflow with our intelligent AI agents. Automate tasks, optimize processes, and integrate cutting-edge AI solutions seamlessly into your projects."}),f.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[f.jsx(b,{to:"/agents",className:"btn-primary",children:"Explore AI Agents"}),f.jsx(b,{to:"/pricing",className:"btn-secondary",children:"Get Started"})]})]})})]}),f.jsx("section",{className:"py-20 bg-[var(--card)]/50",children:f.jsx("div",{className:"container",children:f.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-8 text-center",children:[f.jsxs(F.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},className:"p-6",children:[f.jsx("h3",{className:"text-3xl md:text-4xl font-bold gradient-text mb-2",children:"100+"}),f.jsx("p",{className:"text-gray-400",children:"Available Agents"})]}),f.jsxs(F.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{delay:.1},className:"p-6",children:[f.jsx("h3",{className:"text-3xl md:text-4xl font-bold gradient-text mb-2",children:"<1s"}),f.jsx("p",{className:"text-gray-400",children:"Response Time"})]}),f.jsxs(F.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{delay:.2},className:"p-6",children:[f.jsx("h3",{className:"text-3xl md:text-4xl font-bold gradient-text mb-2",children:"10x"}),f.jsx("p",{className:"text-gray-400",children:"Faster Automation"})]}),f.jsxs(F.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{delay:.3},className:"p-6",children:[f.jsx("h3",{className:"text-3xl md:text-4xl font-bold gradient-text mb-2",children:"24/7"}),f.jsx("p",{className:"text-gray-400",children:"Availability"})]})]})})}),f.jsx("section",{className:"py-20 bg-[var(--card)]/50",children:f.jsx("div",{className:"container",children:f.jsxs(F.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},className:"text-center max-w-3xl mx-auto",children:[f.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Ready to Transform Your Workflow?"}),f.jsx("p",{className:"text-gray-400 mb-8",children:"Join thousands of developers and teams who are already using siliconagent.ai to supercharge their productivity."}),f.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[f.jsx(b,{to:"/signup",className:"btn-primary",children:"Get Started Now"}),f.jsx(b,{to:"/contact",className:"btn-secondary",children:"Schedule Demo"})]})]})})})]}),pf=[{id:1,name:"Email Automation Agent",description:"Automate your email campaigns and follow-ups with our intelligent Email Automation Agent. Schedule, send, and track emails effortlessly.",icon:f.jsx(N2,{className:"w-8 h-8 text-[var(--primary)]"}),category:"Development",rating:4.9,features:["Real-time code suggestions","Multi-language support","Code refactoring","Bug detection","Performance optimization"],useCases:["Web development","Mobile app development","Code review automation"],pricing:"Starting at $29/month"},{id:2,name:"Data Extraction Agent",description:"Extract valuable data from various sources including websites, documents, and databases with our Data Extraction Agent. Simplify your data collection process.",icon:f.jsx(T2,{className:"w-8 h-8 text-[var(--secondary)]"}),category:"Analytics",rating:4.8,features:["Automated data processing","Custom visualizations","Predictive analytics","Report generation","Data cleaning"],useCases:["Business intelligence","Market analysis","Performance tracking"],pricing:"Starting at $49/month"},{id:3,name:"SEO Optimization Agent",description:"Boost your website’s search engine ranking with our SEO Optimization Agent. Analyze keywords, optimize content, and track performance.",icon:f.jsx(D2,{className:"w-8 h-8 text-blue-500"}),category:"Automation",rating:4.9,features:["Task automation","Schedule management","Email processing","Document generation","Workflow optimization"],useCases:["Personal productivity","Team collaboration","Project management"],pricing:"Starting at $19/month"},{id:4,name:"API Connector Agent",description:"Seamlessly integrate and connect different APIs with our API Connector Agent. Facilitate data exchange and automate workflows between systems.",icon:f.jsx(I2,{className:"w-8 h-8 text-green-500"}),category:"Security",rating:4.9,features:["Real-time monitoring","Threat detection","Automated response","Security reporting","Compliance checking"],useCases:["Network security","Application security","Data protection"],pricing:"Starting at $79/month"},{id:5,name:"AI Chatbot Agent",description:"Enhance customer engagement with our AI Chatbot Agent. Provide instant responses, handle queries, and improve customer satisfaction.",icon:f.jsx(hg,{className:"w-8 h-8 text-purple-500"}),category:"Machine Learning",rating:4.8,features:["Automated model training","Hyperparameter optimization","Model deployment","Performance monitoring","Version control"],useCases:["Custom ML models","AutoML pipelines","Model optimization"],pricing:"Starting at $99/month"},{id:6,name:"Invoice Processing Agent",description:"Automate your invoice processing with our Invoice Processing Agent. Extract data, validate information, and streamline your accounting processes.",icon:f.jsx(L2,{className:"w-8 h-8 text-orange-500"}),category:"Database",rating:4.7,features:["Query optimization","Index management","Backup automation","Performance tuning","Schema optimization"],useCases:["Database administration","Performance optimization","Data management"],pricing:"Starting at $59/month"}],U2=({agent:e})=>{const[t,n]=S.useState(!1);return f.jsx("div",{className:"flip-card h-[400px]",children:f.jsxs("div",{className:`flip-card-inner ${t?"flipped":""}`,children:[f.jsxs(F.div,{className:"flip-card-front card card-hover p-6 flex flex-col",whileHover:{scale:1.02},transition:{type:"spring",stiffness:300},children:[f.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[e.icon,f.jsxs("div",{children:[f.jsx("h3",{className:"text-xl font-semibold",children:e.name}),f.jsxs("div",{className:"flex items-center gap-2",children:[f.jsx("span",{className:"text-sm text-gray-400",children:e.category}),f.jsxs("span",{className:"text-sm text-yellow-500",children:["★ ",e.rating]})]})]})]}),f.jsx("p",{className:"text-gray-400 mb-4 flex-grow",children:e.description}),f.jsx("button",{onClick:()=>n(!0),className:"text-[var(--primary)] hover:text-[var(--secondary)] transition-colors inline-flex items-center gap-2",children:"Learn More →"})]}),f.jsxs("div",{className:"flip-card-back card p-6 overflow-y-auto",children:[f.jsxs("div",{className:"flex justify-between items-start mb-4",children:[f.jsx("h3",{className:"text-xl font-semibold",children:e.name}),f.jsx("button",{onClick:()=>n(!1),className:"text-gray-400 hover:text-white transition-colors",children:f.jsx(gg,{size:20})})]}),f.jsxs("div",{className:"space-y-4",children:[f.jsxs("div",{children:[f.jsx("h4",{className:"text-lg font-semibold mb-2",children:"Key Features"}),f.jsx("ul",{className:"list-disc list-inside text-gray-400 space-y-1",children:e.features.map((r,i)=>f.jsx("li",{children:r},i))})]}),f.jsxs("div",{children:[f.jsx("h4",{className:"text-lg font-semibold mb-2",children:"Use Cases"}),f.jsx("ul",{className:"list-disc list-inside text-gray-400 space-y-1",children:e.useCases.map((r,i)=>f.jsx("li",{children:r},i))})]}),f.jsxs("div",{children:[f.jsx("h4",{className:"text-lg font-semibold mb-2",children:"Pricing"}),f.jsx("p",{className:"text-gray-400",children:e.pricing})]}),f.jsx("button",{className:"w-full btn-primary mt-4",children:"Get Started"})]})]})]})})},$2=()=>{const[e,t]=S.useState(""),[n,r]=S.useState("All Categories"),i=pf.filter(o=>{const a=o.name.toLowerCase().includes(e.toLowerCase())||o.description.toLowerCase().includes(e.toLowerCase()),l=n==="All Categories"||o.category===n;return a&&l}),s=["All Categories",...new Set(pf.map(o=>o.category))];return f.jsx("main",{className:"py-24",children:f.jsxs("div",{className:"container",children:[f.jsxs(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center mb-12",children:[f.jsx("h1",{className:"text-4xl md:text-6xl font-bold mb-6",children:"Discover Our AI Agents"}),f.jsx("p",{className:"text-gray-400 text-lg max-w-2xl mx-auto",children:"Explore our collection of specialized AI agents designed to enhance your workflow and boost productivity."})]}),f.jsxs("div",{className:"flex flex-col md:flex-row gap-4 mb-12",children:[f.jsx("div",{className:"flex-1",children:f.jsx("input",{type:"text",placeholder:"Search AI agents...",value:e,onChange:o=>t(o.target.value),className:"w-full px-4 py-2 rounded-lg bg-[var(--card)] text-white border border-gray-700 focus:border-[var(--primary)] focus:outline-none"})}),f.jsx("select",{value:n,onChange:o=>r(o.target.value),className:"px-4 py-2 rounded-lg bg-[var(--card)] text-white border border-gray-700 focus:border-[var(--primary)] focus:outline-none",children:s.map(o=>f.jsx("option",{value:o,children:o},o))})]}),f.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:i.map((o,a)=>f.jsx(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:a*.1},children:f.jsx(U2,{agent:o})},o.id))})]})})},W2=()=>f.jsx("main",{className:"py-24",children:f.jsxs("div",{className:"container",children:[f.jsx(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center mb-20"}),f.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-20",children:[f.jsxs(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center",children:[f.jsx("h2",{className:"text-2xl md:text-4xl gradient-text font-bold mb-6",children:"Our Mission"}),f.jsx("p",{className:"text-gray-400 text-lg max-w-2xl mx-auto",children:"To empower businesses and individuals with the power of AI automation, making complex tasks simple and efficient."})]}),f.jsxs(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center",children:[f.jsx("h2",{className:"gradient-text text-2xl md:text-4xl font-bold mb-6",children:"Our Vision"}),f.jsx("p",{className:"text-gray-400 text-lg max-w-2xl mx-auto",children:"To be the leading platform for AI agent creation and execution, driving innovation and productivity worldwide."})]})]}),f.jsx("div",{className:"text-center items-center mb-20",children:f.jsxs(F.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.2},className:"text-center",children:[f.jsx("h2",{className:"gradient-text text-2xl md:text-4xl font-bold mb-6",children:"Our Team"}),f.jsx("p",{className:"text-gray-400 text-lg max-w-2xl mx-auto",children:"We are a team of passionate AI experts, developers, and innovators dedicated to building the future of AI automation."})]})}),f.jsx("div",{className:"text-center items-center mb-6",children:f.jsxs(F.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.4},children:[f.jsxs("h2",{className:"text-3xl font-bold mb-6",children:["Transforming Businesses through ",f.jsx("span",{className:"gradient-text",children:"AI Innovation"})]}),f.jsx("p",{className:"text-gray-400 mb-4",children:"We believe in the power of AI to revolutionize how businesses operate, making complex tasks simpler and more efficient."}),f.jsxs("ul",{className:"flex flex-col mx-auto items-center text-center space-y-4",children:[f.jsxs("li",{className:"flex items-start",children:[f.jsx("div",{className:"h-2 w-2 mt-2 rounded-full bg-[var(--primary)] mr-3"}),f.jsx("p",{className:"text-gray-400",children:"Pioneering AI solutions that solve real-world challenges"})]}),f.jsxs("li",{className:"flex items-start",children:[f.jsx("div",{className:"h-2 w-2 mt-2 rounded-full bg-[var(--primary)] mr-3"}),f.jsx("p",{className:"text-gray-400",children:"Building scalable and reliable automation systems"})]}),f.jsxs("li",{className:"flex items-start",children:[f.jsx("div",{className:"h-2 w-2 mt-2 rounded-full bg-[var(--primary)] mr-3"}),f.jsx("p",{className:"text-gray-400",children:"Empowering teams with cutting-edge AI technology"})]})]})]})})]})}),b2=[{id:1,title:"How AI Agents Are Revolutionizing Software Development",excerpt:"Discover how AI-powered automation is transforming the way developers work and increasing productivity...",author:"Sarah Chen",readTime:"4 min read",image:"https://images.unsplash.com/photo-1555949963-aa79dcee981c?auto=format&fit=crop&q=80&w=800",category:"Development"},{id:2,title:"The Business Impact of AI Automation in 2025",excerpt:"An in-depth analysis of how AI automation is driving business transformation and ROI...",author:"Michael Roberts",readTime:"6 min read",image:"https://images.unsplash.com/photo-1551288049-bebda4e38f71?auto=format&fit=crop&q=80&w=800",category:"Business"},{id:3,title:"Building Smart AI Agents for Enterprise Solutions",excerpt:"Learn how to create and deploy intelligent AI agents that scale with your enterprise needs...",author:"Emily Watson",readTime:"5 min read",image:"https://images.unsplash.com/photo-1488229297570-58520851e868?auto=format&fit=crop&q=80&w=800",category:"Enterprise"}],H2=()=>f.jsx("main",{className:"py-24",children:f.jsxs("div",{className:"container",children:[f.jsxs(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center mb-16",children:[f.jsxs("h1",{className:"text-4xl md:text-6xl font-bold mb-6",children:["Discover How AI is Changing the",f.jsx("br",{}),f.jsx("span",{className:"gradient-text",children:"Future of Automation"})]}),f.jsx("p",{className:"text-gray-400 text-lg max-w-2xl mx-auto",children:"Explore the latest insights, trends, and success stories in AI automation"})]}),f.jsxs("div",{className:"flex flex-wrap gap-4 mb-12",children:[f.jsx("button",{className:"px-4 py-2 rounded-full bg-[var(--primary)] text-white",children:"All"}),f.jsx("button",{className:"px-4 py-2 rounded-full bg-[var(--card)] text-gray-400 hover:text-white",children:"Business"}),f.jsx("button",{className:"px-4 py-2 rounded-full bg-[var(--card)] text-gray-400 hover:text-white",children:"Automation"}),f.jsx("button",{className:"px-4 py-2 rounded-full bg-[var(--card)] text-gray-400 hover:text-white",children:"AI Agents"})]}),f.jsx("div",{className:"grid md:grid-cols-3 gap-8",children:b2.map((e,t)=>f.jsxs(F.article,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:t*.1},className:"card card-hover overflow-hidden",children:[f.jsx("img",{src:e.image,alt:e.title,className:"w-full h-48 object-cover"}),f.jsxs("div",{className:"p-6",children:[f.jsxs("div",{className:"flex items-center gap-2 mb-4",children:[f.jsx("span",{className:"text-sm text-[var(--primary)]",children:e.category}),f.jsx("span",{className:"text-gray-400",children:"•"}),f.jsx("span",{className:"text-sm text-gray-400",children:e.readTime})]}),f.jsx("h2",{className:"text-xl font-semibold mb-4",children:e.title}),f.jsx("p",{className:"text-gray-400 mb-6",children:e.excerpt}),f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsx("span",{className:"text-sm text-gray-400",children:e.author}),f.jsx("button",{className:"text-[var(--primary)] hover:text-[var(--secondary)]",children:"Read More →"})]})]})]},e.id))}),f.jsx("div",{className:"text-center mt-12",children:f.jsx("button",{className:"btn-primary",children:"Load More Articles"})})]})}),K2=[{name:"Basic",price:0,description:"Perfect for individuals and small teams",features:["5 AI Agents","1,000 API calls/month","Basic analytics","Email support","Community access"]},{name:"Professional",price:49,description:"Ideal for growing businesses",features:["20 AI Agents","10,000 API calls/month","Advanced analytics","Priority support","API access","Custom integrations","Team collaboration"],popular:!0},{name:"Enterprise",price:299,description:"For large organizations",features:["Unlimited AI Agents","Unlimited API calls","Enterprise analytics","24/7 dedicated support","Custom AI model training","SLA guarantee","Advanced security","Custom deployment"]}],G2=()=>f.jsx("main",{className:"py-24",children:f.jsxs("div",{className:"container",children:[f.jsxs(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center mb-16",children:[f.jsx("h1",{className:"text-4xl md:text-6xl font-bold mb-6",children:f.jsx("span",{className:"gradient-text",children:"Plans"})}),f.jsx("p",{className:"text-gray-400 text-lg max-w-2xl mx-auto",children:"Choose the perfect plan for your needs. All plans include our core features with different quotas and capabilities."})]}),f.jsx("div",{className:"grid md:grid-cols-3 gap-8 mb-16",children:K2.map((e,t)=>f.jsxs(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:t*.1},className:`card card-hover relative ${e.popular?"border-2 border-[var(--primary)]":""}`,children:[e.popular&&f.jsx("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:f.jsx("span",{className:"bg-[var(--primary)] text-white text-sm font-medium px-4 py-1 rounded-full",children:"Most Popular"})}),f.jsxs("div",{className:"p-6",children:[f.jsx("h3",{className:"text-2xl font-bold mb-2",children:e.name}),f.jsx("p",{className:"text-gray-400 mb-6",children:e.description}),e.name!=="Enterprise"&&f.jsxs("div",{className:"mb-6",children:[f.jsxs("span",{className:"text-4xl font-bold",children:["$",e.price]}),f.jsx("span",{className:"text-gray-400",children:"/month"})]}),e.name==="Enterprise"&&f.jsx("div",{className:"mb-6",children:f.jsx("span",{className:"text-4xl font-bold",children:"Contact Us"})}),f.jsx("ul",{className:"space-y-4 mb-8",children:e.features.map(n=>f.jsxs("li",{className:"flex items-center gap-3",children:[f.jsx(k2,{className:"w-5 h-5 text-[var(--primary)]"}),f.jsx("span",{className:"text-gray-300",children:n})]},n))}),f.jsx("button",{className:`w-full ${e.popular?"btn-primary":"btn-secondary"}`,children:"Get Started"})]})]},e.name))}),f.jsxs("div",{className:"max-w-3xl mx-auto",children:[f.jsx("h2",{className:"text-3xl font-bold text-center mb-8",children:"Frequently Asked Questions"}),f.jsxs("div",{className:"space-y-6",children:[f.jsxs("div",{className:"card",children:[f.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Can I switch plans later?"}),f.jsx("p",{className:"text-gray-400",children:"Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle."})]}),f.jsxs("div",{className:"card",children:[f.jsx("h3",{className:"text-xl font-semibold mb-2",children:"What payment methods do you accept?"}),f.jsx("p",{className:"text-gray-400",children:"We accept all major credit cards, PayPal, and wire transfers for enterprise customers."})]}),f.jsxs("div",{className:"card",children:[f.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Do you offer a free trial?"}),f.jsx("p",{className:"text-gray-400",children:"Yes, all plans come with a 14-day free trial. No credit card required."})]})]})]})]})}),Q2=[{icon:f.jsx(C2,{className:"w-8 h-8 text-[var(--primary)]"}),title:"AI-Driven Automation",description:"Boost efficiency with intelligent automation."},{icon:f.jsx(O2,{className:"w-8 h-8 text-[var(--secondary)]"}),title:"No Coding Required",description:"Build and deploy agents without writing code."},{icon:f.jsx(S2,{className:"w-8 h-8 text-purple-500"}),title:"Multi-LLM Support",description:"Leverage multiple AI models for diverse tasks."},{icon:f.jsx(hg,{className:"w-8 h-8 text-green-500"}),title:"Customizable & Scalable",description:"Tailor agents to your specific needs and scale effortlessly."},{icon:f.jsx(V2,{className:"w-8 h-8 text-blue-500"}),title:"Business-Focused",description:"Designed for real-world business applications."},{icon:f.jsx(P2,{className:"w-8 h-8 text-orange-500"}),title:"Enterprise-Ready Security",description:"Ensure data protection and compliance."}],Y2=()=>f.jsx("main",{className:"py-24",children:f.jsxs("div",{className:"container",children:[f.jsx(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center mb-16",children:f.jsxs("h1",{className:"text-4xl md:text-5xl font-bold mb-6",children:["Why Choose ",f.jsx("span",{className:"gradient-text",children:"siliconagent.ai?"})]})}),f.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:Q2.map((e,t)=>f.jsxs(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:t*.1},className:"card flex p-6 rounded-lg shadow-lg",children:[f.jsx("div",{className:"flex items-center justify-center w-1/4",children:e.icon}),f.jsxs("div",{className:"w-3/4 pl-4",children:[f.jsx("h3",{className:"text-xl font-semibold mb-2",children:e.title}),f.jsx("p",{className:"text-gray-400",children:e.description})]})]},t))})]})}),X2=()=>{const e=[{title:"No-Code & Low-Code Agent Creation",description:"Build AI agents with or without coding using our intuitive interface."},{title:"Agent Execution Engine",description:"Run agents on-demand or as part of automated workflows."},{title:"Multi-LLM Support",description:"Integrate different AI models like OpenAI, Ollama, and Gemini."},{title:"API Integration",description:"Seamlessly connect agents with external services and APIs."},{title:"Automated Workflows",description:"Implement AI-driven process automation for your business needs."},{title:"Real-time Monitoring & Analytics",description:"Track AI agent performance and gain valuable insights."},{title:"Security & RBAC",description:"Ensure secure access and control with role-based access control."}];return f.jsxs(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{duration:.5},className:"container mx-auto p-4 py-24",children:[f.jsx("h1",{className:"text-4xl md:text-6xl font-bold text-center mb-12",children:"Key Features"}),f.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.map((t,n)=>f.jsxs(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:n*.1},className:"card text-center p-6 rounded-lg shadow-lg",children:[f.jsx("h2",{className:"text-xl font-semibold mb-4",children:t.title}),f.jsx("p",{className:"text-gray-300",children:t.description})]},n))})]})},Z2=()=>f.jsxs(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{duration:.5},className:"container mx-auto p-4 py-24",children:[f.jsx("h1",{className:"text-4xl md:text-6xl font-bold text-center mb-12",children:"Contact Us"}),f.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[f.jsxs("div",{children:[f.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Get in Touch"}),f.jsxs("form",{className:"space-y-4",children:[f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium mb-1",children:"Name"}),f.jsx("input",{type:"text",className:"w-full p-2 rounded bg-gray-800 text-gray-300"})]}),f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium mb-1",children:"Email"}),f.jsx("input",{type:"email",className:"w-full p-2 rounded bg-gray-800 text-gray-300"})]}),f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium mb-1",children:"Message"}),f.jsx("textarea",{className:"w-full p-2 rounded bg-gray-800 text-gray-300",rows:4})]}),f.jsx("button",{type:"submit",className:"btn-primary",children:"Send Message"})]})]}),f.jsxs("div",{children:[f.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Business Inquiries"}),f.jsx("p",{className:"mb-4",children:"For partnership opportunities and business-related questions, please contact us at:"}),f.jsxs("div",{className:"flex items-center mb-4",children:[f.jsx(A2,{className:"w-6 h-6 text-[var(--primary)] mr-2"}),f.jsx("a",{href:"mailto:<EMAIL>",className:"text-[var(--primary)]",children:"<EMAIL>"})]}),f.jsxs("div",{className:"flex items-center mb-4",children:[f.jsx(R2,{className:"w-6 h-6 text-[var(--primary)] mr-2"}),f.jsx("span",{className:"text-[var(--primary)]",children:"+91 6301704900"})]}),f.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Follow Us"}),f.jsxs("div",{className:"flex space-x-4",children:[f.jsx("a",{href:"#",className:"text-[var(--primary)]",children:f.jsx(pg,{className:"w-6 h-6"})}),f.jsx("a",{href:"#",className:"text-[var(--primary)]",children:f.jsx(mg,{className:"w-6 h-6"})}),f.jsx("a",{href:"#",className:"text-[var(--primary)]",children:f.jsx(E2,{className:"w-6 h-6"})})]})]})]})]});function q2(){return f.jsx(W0,{children:f.jsxs("div",{className:"min-h-screen flex flex-col",children:[f.jsx(F2,{}),f.jsx(Z0,{mode:"wait",children:f.jsxs(I0,{children:[f.jsx(ot,{path:"/",element:f.jsx(B2,{})}),f.jsx(ot,{path:"/agents",element:f.jsx($2,{})}),f.jsx(ot,{path:"/about",element:f.jsx(W2,{})}),f.jsx(ot,{path:"/blog",element:f.jsx(H2,{})}),f.jsx(ot,{path:"/pricing",element:f.jsx(G2,{})}),f.jsx(ot,{path:"/advantages",element:f.jsx(Y2,{})}),f.jsx(ot,{path:"/features",element:f.jsx(X2,{})}),f.jsx(ot,{path:"/contact",element:f.jsx(Z2,{})})]})}),f.jsx(z2,{})]})})}jp(document.getElementById("root")).render(f.jsx(S.StrictMode,{children:f.jsx(q2,{})}));
