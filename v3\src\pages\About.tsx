import { motion } from 'framer-motion';

const About = () => {
  return (
    <main className="py-24">
      <div className="container">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-20"
        >
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-20">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <h2 className="text-2xl md:text-4xl gradient-text font-bold mb-6">
              Our Mission
            </h2>
            <p className="text-gray-400 text-lg max-w-2xl mx-auto">
              To empower businesses and individuals with the power of AI automation, making complex tasks simple and efficient.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <h2 className="gradient-text text-2xl md:text-4xl font-bold mb-6">
              Our Vision
            </h2>
            <p className="text-gray-400 text-lg max-w-2xl mx-auto">
              To be the leading platform for AI agent creation and execution, driving innovation and productivity worldwide.
            </p>
          </motion.div>
        </div>

        <div className="text-center items-center mb-20">
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="text-center"
          >
            <h2 className="gradient-text text-2xl md:text-4xl font-bold mb-6">
              Our Team
            </h2>
            <p className="text-gray-400 text-lg max-w-2xl mx-auto">
              We are a team of passionate AI experts, developers, and innovators dedicated to building the future of AI automation.
            </p>
          </motion.div>
        </div>

        <div className="text-center items-center mb-6">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
          >
            <h2 className="text-3xl font-bold mb-6">Transforming Businesses through <span className='gradient-text'>AI Innovation</span></h2>
            <p className="text-gray-400 mb-4">
              We believe in the power of AI to revolutionize how businesses operate, making complex tasks simpler and more efficient.
            </p>
            <ul className="flex flex-col mx-auto items-center text-center space-y-4">
              <li className="flex items-start">
                <div className="h-2 w-2 mt-2 rounded-full bg-[var(--primary)] mr-3"></div>
                <p className="text-gray-400">Pioneering AI solutions that solve real-world challenges</p>
              </li>
              <li className="flex items-start">
                <div className="h-2 w-2 mt-2 rounded-full bg-[var(--primary)] mr-3"></div>
                <p className="text-gray-400">Building scalable and reliable automation systems</p>
              </li>
              <li className="flex items-start">
                <div className="h-2 w-2 mt-2 rounded-full bg-[var(--primary)] mr-3"></div>
                <p className="text-gray-400">Empowering teams with cutting-edge AI technology</p>
              </li>
            </ul>
          </motion.div>
        </div>

        {/* Stats Section
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-20">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="text-center"
          >
            <h3 className="text-4xl font-bold gradient-text">500+</</h3>
            <p className="text-gray-400 mt-2">Clients Worldwide</p>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="text-center"
          >
            <h3 className="text-4xl font-bold gradient-text">50M+</h3>
            <p className="text-gray-400 mt-2">Tasks Automated</p>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="text-center"
          >
            <h3 className="text-4xl font-bold gradient-text">15+</h3>
            <p className="text-gray-400 mt-2">Countries</p>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="text-center"
          >
            <h3 className="text-4xl font-bold gradient-text">98%</h3>
            <p className="text-gray-400 mt-2">Client Satisfaction</p>
          </motion.div>
        </div> */}
      </div>
    </main>
  );
};

export default About;