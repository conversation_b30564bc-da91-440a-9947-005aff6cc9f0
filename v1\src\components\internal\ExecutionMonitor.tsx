import React from 'react';
    import { executionMonitorData } from './mockData/executionMonitorData';
    import { Check<PERSON><PERSON><PERSON>, XCircle, Loader2 } from 'lucide-react';

    const ExecutionMonitor: React.FC = () => {
      return (
        <div className="p-6 bg-gray-900 text-white rounded-lg shadow-md">
          <h1 className="text-3xl font-bold mb-6">AI Execution Monitor</h1>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-700">
              <thead className="bg-gray-800">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">ID</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Agent Chain</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Start Time</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Progress</th>
                </tr>
              </thead>
              <tbody className="bg-gray-900 divide-y divide-gray-700">
                {executionMonitorData.map((execution) => (
                  <tr key={execution.id} className="hover:bg-gray-800">
                    <td className="px-4 py-4 whitespace-nowrap">{execution.id}</td>
                    <td className="px-4 py-4 whitespace-nowrap">{execution.agentChain}</td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      {execution.status === 'running' && <Loader2 className="text-blue-500 inline-block mr-1 animate-spin" />}
                      {execution.status === 'completed' && <CheckCircle className="text-green-500 inline-block mr-1" />}
                      {execution.status === 'error' && <XCircle className="text-red-500 inline-block mr-1" />}
                      {execution.status}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">{execution.startTime}</td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="relative pt-1">
                        <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-gray-700">
                          <div style={{ width: `${execution.progress}%` }} className={`shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center ${execution.status === 'running' ? 'bg-blue-500' : execution.status === 'completed' ? 'bg-green-500' : 'bg-red-500'}`}></div>
                        </div>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      );
    };

    export default ExecutionMonitor;
