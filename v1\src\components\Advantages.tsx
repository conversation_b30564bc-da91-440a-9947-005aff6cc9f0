import React from 'react';
    import { Bolt, CheckCircle, Code, Cpu, Shield, TrendingUp } from 'lucide-react';

    const Advantages: React.FC = () => {
      return (
        <div className="bg-gray-900 text-white min-h-screen p-8">
          <div className="container mx-auto">
            <h1 className="text-4xl font-bold text-center mb-12">Why Choose siliconagent.ai?</h1>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Advantage 1 */}
              <div className="bg-gray-800 p-6 rounded-lg shadow-lg flex items-center">
                <Bolt className="mr-4 h-8 w-8 text-purple-500" />
                <div>
                  <h2 className="text-2xl font-bold mb-2">AI-Driven Automation</h2>
                  <p className="text-gray-300">Boost efficiency with intelligent automation.</p>
                </div>
              </div>

              {/* Advantage 2 */}
              <div className="bg-gray-800 p-6 rounded-lg shadow-lg flex items-center">
                <Code className="mr-4 h-8 w-8 text-purple-500" />
                <div>
                  <h2 className="text-2xl font-bold mb-2">No Coding Required</h2>
                  <p className="text-gray-300">Build and deploy agents without writing code.</p>
                </div>
              </div>

              {/* Advantage 3 */}
              <div className="bg-gray-800 p-6 rounded-lg shadow-lg flex items-center">
                <Cpu className="mr-4 h-8 w-8 text-purple-500" />
                <div>
                  <h2 className="text-2xl font-bold mb-2">Multi-LLM Support</h2>
                  <p className="text-gray-300">Leverage multiple AI models for diverse tasks.</p>
                </div>
              </div>

              {/* Advantage 4 */}
              <div className="bg-gray-800 p-6 rounded-lg shadow-lg flex items-center">
                <TrendingUp className="mr-4 h-8 w-8 text-purple-500" />
                <div>
                  <h2 className="text-2xl font-bold mb-2">Customizable & Scalable</h2>
                  <p className="text-gray-300">Tailor agents to your specific needs and scale effortlessly.</p>
                </div>
              </div>

              {/* Advantage 5 */}
              <div className="bg-gray-800 p-6 rounded-lg shadow-lg flex items-center">
                <CheckCircle className="mr-4 h-8 w-8 text-purple-500" />
                <div>
                  <h2 className="text-2xl font-bold mb-2">Business-Focused</h2>
                  <p className="text-gray-300">Designed for real-world business applications.</p>
                </div>
              </div>

              {/* Advantage 6 */}
              <div className="bg-gray-800 p-6 rounded-lg shadow-lg flex items-center">
                <Shield className="mr-4 h-8 w-8 text-purple-500" />
                <div>
                  <h2 className="text-2xl font-bold mb-2">Enterprise-Ready Security</h2>
                  <p className="text-gray-300">Ensure data protection and compliance.</p>
                </div>
              </div>
            </div>

            {/* Comparison (Placeholder) */}
            <div className="mt-16">
              <h2 className="text-3xl font-bold text-center mb-8">siliconagent.ai vs. Competitors</h2>
              <p className="text-lg text-gray-300 text-center">
                We offer faster, more flexible AI agent deployment compared to platforms like UiPath and OpenAI Assistants.
              </p>
            </div>
          </div>
        </div>
      );
    };

    export default Advantages;
