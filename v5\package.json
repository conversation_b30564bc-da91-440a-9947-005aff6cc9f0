{"private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix vite:build", "dev": "remix vite:dev", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "start": "remix-serve ./build/server/index.js", "typecheck": "tsc"}, "devDependencies": {"@remix-run/dev": "^2.16.2", "@remix-run/node": "^2.16.2", "@remix-run/react": "^2.16.2", "@remix-run/serve": "^2.16.2", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.5", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "eslint": "^8.57.1", "eslint-import-resolver-typescript": "^3.10.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^4.6.2", "isbot": "^5.1.25", "mysql2": "^3.14.0", "postcss": "^8.5.3", "react": "^18.3.0", "react-dom": "^18.3.0", "tailwindcss": "^3.4.17", "typescript": "^5.8.2", "vite": "^5.4.15", "vite-tsconfig-paths": "^4.3.2"}, "engines": {"node": ">=20.0.0"}, "dependencies": {"@radix-ui/react-tabs": "^1.1.3", "class-variance-authority": "^0.7.1", "framer-motion": "^11.11.17", "highlight.js": "^11.11.1", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.0.2"}}