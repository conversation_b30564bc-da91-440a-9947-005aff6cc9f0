import React from 'react';
    import { analyticsData } from './mockData/analyticsData';
    import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-react';

    const Analytics: React.FC = () => {
      return (
        <div className="p-6 bg-gray-900 text-white rounded-lg shadow-md">
          <h1 className="text-3xl font-bold mb-6 flex items-center"><BarChart className="mr-2" /> Analytics &amp; Insights</h1>

          <h2 className="text-xl font-semibold mb-4">Agent Performance</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {analyticsData.agentPerformance.map((agent, index) => (
              <div key={index} className="bg-gray-800 p-4 rounded-lg shadow">
                <h3 className="text-lg font-semibold">{agent.agent}</h3>
                <p className="text-gray-400 text-sm">Avg. Execution Time: {agent.averageExecutionTime}s</p>
                <p className="text-gray-400 text-sm">Success Rate: {agent.successRate}%</p>
              </div>
            ))}
          </div>

          <h2 className="text-xl font-semibold mb-4">Resource Usage</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {analyticsData.resourceUsage.map((resource, index) => (
              <div key={index} className="bg-gray-800 p-4 rounded-lg shadow flex items-center">
                <PieChart className="mr-4 text-purple-500" />
                <div>
                  <h3 className="text-lg font-semibold">{resource.resource}</h3>
                  <p className="text-gray-400 text-sm">Usage: {resource.usage}%</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      );
    };

    export default Analytics;
