import React from 'react';

    const Blog: React.FC = () => {
      // Placeholder data for blog posts
      const posts = [
        { id: 1, title: 'Introducing AI Agents', date: '2024-07-24', excerpt: 'Learn about the power of AI agents and how they can transform your business.' },
        { id: 2, title: 'How to Build Your First AI Agent', date: '2024-07-25', excerpt: 'A step-by-step guide to creating your first AI agent with siliconagent.ai.' },
        { id: 3, title: 'The Future of AI Automation', date: '2024-07-26', excerpt: 'Explore the latest trends and advancements in AI automation.' },
      ];

      return (
        <div className="bg-gray-900 text-white min-h-screen p-8">
          <div className="container mx-auto">
            <h1 className="text-4xl font-bold text-center mb-12">Blog & News</h1>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {posts.map((post) => (
                <div key={post.id} className="bg-gray-800 p-6 rounded-lg shadow-lg">
                  <h2 className="text-2xl font-bold mb-2">{post.title}</h2>
                  <p className="text-gray-400 mb-2">{post.date}</p>
                  <p className="text-gray-300 mb-4">{post.excerpt}</p>
                  <a href={`/blog/${post.id}`} className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                    Read More
                  </a>
                </div>
              ))}
            </div>
          </div>
        </div>
      );
    };

    export default Blog;
