import { useEffect, useState } from "react";
import { FiSun, FiMoon } from "react-icons/fi";

export function ThemeToggle() {
  const [isDark, setIsDark] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    // Check for saved theme preference or default to system preference
    const savedTheme = localStorage.getItem("theme");
    const systemPrefersDark = window.matchMedia(
      "(prefers-color-scheme: dark)"
    ).matches;

    const shouldBeDark =
      savedTheme === "dark" || (!savedTheme && systemPrefersDark);

    setIsDark(shouldBeDark);
    updateTheme(shouldBeDark);
  }, []);

  const updateTheme = (dark: boolean) => {
    const html = document.documentElement;

    if (dark) {
      html.classList.add("dark");
      html.style.colorScheme = "dark";
      localStorage.setItem("theme", "dark");
    } else {
      html.classList.remove("dark");
      html.style.colorScheme = "light";
      localStorage.setItem("theme", "light");
    }
  };

  const toggleTheme = () => {
    setIsTransitioning(true);
    const newTheme = !isDark;

    // Add transition class for smooth animation
    document.documentElement.classList.add("theme-transition");

    setIsDark(newTheme);
    updateTheme(newTheme);

    // Remove transition flag after animation completes
    setTimeout(() => {
      setIsTransitioning(false);
    }, 300);
  };

  return (
    <button
      onClick={toggleTheme}
      disabled={isTransitioning}
      className={`relative inline-flex h-8 w-16 items-center rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-black theme-transition ${
        isDark
          ? "bg-gradient-to-r from-blue-600 to-purple-600 shadow-lg shadow-blue-500/25"
          : "bg-gray-200 hover:bg-gray-300 shadow-md shadow-gray-300/25"
      } ${isTransitioning ? "cursor-wait" : "cursor-pointer"}`}
      aria-label="Toggle theme"
    >
      {/* Toggle Track Background */}
      <div
        className={`absolute inset-0 rounded-full theme-transition ${
          isDark
            ? "bg-gradient-to-r from-blue-600 to-purple-600"
            : "bg-gray-200"
        }`}
      />

      {/* Toggle Thumb */}
      <div
        className={`relative inline-block h-6 w-6 transform rounded-full bg-white shadow-lg theme-transition ${
          isDark ? "translate-x-9" : "translate-x-1"
        } ${
          isTransitioning ? "" : "transition-transform duration-300 ease-in-out"
        }`}
      >
        {/* Icon Container */}
        <div className="absolute inset-0 flex items-center justify-center">
          <FiSun
            className={`h-3 w-3 text-amber-500 theme-transition ${
              isDark
                ? "opacity-0 rotate-180 scale-75"
                : "opacity-100 rotate-0 scale-100"
            }`}
          />
          <FiMoon
            className={`absolute h-3 w-3 text-blue-600 theme-transition ${
              isDark
                ? "opacity-100 rotate-0 scale-100"
                : "opacity-0 -rotate-180 scale-75"
            }`}
          />
        </div>
      </div>

      {/* Enhanced Glow Effect */}
      <div
        className={`absolute inset-0 rounded-full theme-transition ${
          isDark
            ? "shadow-lg shadow-blue-500/25"
            : "shadow-md shadow-gray-300/25"
        }`}
      />

      {/* Background Icons */}
      <div className="absolute inset-0 flex items-center justify-between px-2 pointer-events-none">
        <FiSun
          className={`h-3 w-3 theme-transition ${
            isDark ? "text-blue-200/40" : "text-amber-400/60"
          }`}
        />
        <FiMoon
          className={`h-3 w-3 theme-transition ${
            isDark ? "text-blue-200/60" : "text-gray-400/40"
          }`}
        />
      </div>
    </button>
  );
}
