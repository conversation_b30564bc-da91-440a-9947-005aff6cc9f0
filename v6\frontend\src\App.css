/* App Layout */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  text-align: center;
}

.app-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
}

.app-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 1.1rem;
}

/* Navigation */
.app-nav {
  background: #f8f9fa;
  padding: 0;
  display: flex;
  border-bottom: 1px solid #dee2e6;
}

.app-nav button {
  background: none;
  border: none;
  padding: 1rem 2rem;
  cursor: pointer;
  font-size: 1rem;
  color: #495057;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.app-nav button:hover {
  background: #e9ecef;
  color: #212529;
}

.app-nav button.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: white;
}

/* Main Content */
.app-main {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.app-footer {
  background: #f8f9fa;
  padding: 1rem;
  text-align: center;
  color: #6c757d;
  border-top: 1px solid #dee2e6;
}

/* Dashboard Styles */
.dashboard h2 {
  color: #333;
  margin-bottom: 2rem;
}

.api-status-card {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.dot.green {
  background: #28a745;
}

.dot.red {
  background: #dc3545;
}

.api-info, .api-error {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  margin: 1rem 0;
}

.api-error {
  background: #f8d7da;
  color: #721c24;
}

.refresh-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
}

.quick-stats {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.guide-steps {
  display: grid;
  gap: 1.5rem;
  margin-top: 1rem;
}

.step {
  padding: 1.5rem;
  border: 1px solid #dee2e6;
  border-radius: 8px;
}

.step h4 {
  color: #667eea;
  margin-bottom: 0.5rem;
}

.step code {
  background: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
}

.step ul {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

/* Common Styles */
.loading {
  text-align: center;
  padding: 2rem;
  font-size: 1.2rem;
  color: #6c757d;
}

.error {
  background: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  border: 1px solid #f5c6cb;
}

.form-section {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.user-form, .task-form {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  align-items: end;
}

.user-form input, .task-form input, .task-form textarea, .task-form select {
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
}

.user-form input, .task-form input {
  flex: 1;
  min-width: 200px;
}

.task-form textarea {
  flex: 2;
  min-width: 300px;
  min-height: 80px;
  resize: vertical;
}

.task-form select {
  min-width: 150px;
}

.user-form button, .task-form button {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
}

.user-form button:hover, .task-form button:hover {
  background: #5a6fd8;
}

.task-form button[type="button"] {
  background: #6c757d;
}

.task-form button[type="button"]:hover {
  background: #5a6268;
}

/* Filters */
.filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.filters select {
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
}

/* Lists and Grids */
.users-list, .tasks-list {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.users-grid, .tasks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.user-card, .task-card {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1.5rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.user-card:hover, .task-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.task-card.completed {
  background: #d4edda;
  border-color: #c3e6cb;
}

.task-card.completed h4 {
  text-decoration: line-through;
  opacity: 0.7;
}

.user-card h4, .task-card h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.user-date, .task-date, .task-user {
  font-size: 0.9rem;
  color: #6c757d;
  margin: 0.5rem 0;
}

.user-actions, .task-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.user-actions button, .task-actions button {
  padding: 0.25rem 0.75rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.user-actions button:first-child, .task-actions button:first-child {
  background: #667eea;
  color: white;
}

.user-actions button:nth-child(2), .task-actions button:nth-child(2) {
  background: #28a745;
  color: white;
}

.delete-btn {
  background: #dc3545 !important;
  color: white !important;
}

.user-actions button:hover, .task-actions button:hover {
  opacity: 0.8;
}

/* Task form checkbox */
.task-form label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-nav {
    flex-direction: column;
  }

  .app-main {
    padding: 1rem;
  }

  .user-form, .task-form {
    flex-direction: column;
    align-items: stretch;
  }

  .users-grid, .tasks-grid {
    grid-template-columns: 1fr;
  }

  .filters {
    flex-direction: column;
  }
}
