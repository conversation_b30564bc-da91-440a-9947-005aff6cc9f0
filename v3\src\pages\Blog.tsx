// filepath: d:\Projects\SiliconAgent\website\v3\src\pages\Blog.tsx
import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom"; // Added for navigation

const Blog = () => {
  interface BlogPost {
    id: number;
    title: string;
    excerpt: string;
    author: string;
    readTime: string;
    image: string;
    category: string;
  }

  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<BlogPost[]>([]); // State for filtered posts
  const [categories, setCategories] = useState<string[]>([]); // State for top 6 categories
  const [selectedCategory, setSelectedCategory] = useState<string>("All"); // State for selected category
  const [showAll, setShowAll] = useState(false); // State to toggle between showing all and limited posts
  const navigate = useNavigate(); // Initialize navigation

  const truncate = (text: string, maxLength: number) => {
    return text.length > maxLength ? text.substring(0, maxLength) + "..." : text;
  };

  useEffect(() => {
    // Fetch blog posts
    fetch("http://localhost:5000/api/posts", {
      method: "GET",
      headers: { "Content-Type": "application/json" },
      mode: "cors", // Ensure CORS mode is set
    })
      .then((response) => response.json())
      .then((data) => {
        setBlogPosts(data);
        setFilteredPosts(data); // Initially, show all posts
      })
      .catch((error) => console.error("Error fetching blog posts:", error));

    // Fetch top 6 categories
    fetch("http://localhost:5000/api/categories", {
      method: "GET",
      headers: { "Content-Type": "application/json" },
      mode: "cors",
    })
      .then((response) => response.json())
      .then((data) => setCategories(["All", ...data])) // Add "All" to the list of categories
      .catch((error) => console.error("Error fetching categories:", error));
  }, []);

  const handleCategoryClick = (category: string) => {
    setSelectedCategory(category);
    if (category === "All") {
      setFilteredPosts(blogPosts); // Show all posts if "All" is selected
    } else {
      setFilteredPosts(blogPosts.filter((post) => post.category === category)); // Filter posts by category
    }
    setShowAll(false); // Reset to show limited posts when category changes
  };

  const displayedPosts = showAll ? filteredPosts : filteredPosts.slice(0, 3); // Show all or first 3 posts

  return (
    <main className="py-24">
      <div className="container">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Discover How AI is Changing the
            <br />
            <span className="gradient-text">Future of Automation</span>
          </h1>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            Explore the latest insights, trends, and success stories in AI
            automation
          </p>
        </motion.div>

        {/* Filter Section */}
        <div className="flex flex-wrap gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category}
              className={`px-4 py-2 rounded-full ${selectedCategory === category
                ? "bg-[var(--primary)] text-white"
                : "bg-[var(--card)] text-gray-400 hover:text-white"
                }`}
              onClick={() => handleCategoryClick(category)}
            >
              {category}
            </button>
          ))}
        </div>

        {filteredPosts.length > 0 ? (
          <div className="grid md:grid-cols-3 gap-8">
            {displayedPosts.map((post, index) => (
              <motion.article
                key={post.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="card card-hover overflow-hidden"
              >
                <img
                  src={post.image}
                  alt={post.title}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <span className="text-sm text-[var(--primary)]">
                      {post.category}
                    </span>
                    <span className="text-gray-400">•</span>
                    <span className="text-sm text-gray-400">{post.readTime}</span>
                  </div>
                  <h2 className="text-xl font-semibold mb-4">
                    {truncate(post.title, 40)} {/* Truncate title */}
                  </h2>
                  <p className="text-gray-400 mb-6">
                    {truncate(post.excerpt, 100)} {/* Truncate excerpt */}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-400">{post.author}</span>
                    <button
                      className="text-[var(--primary)] hover:text-[var(--secondary)]"
                      onClick={() => navigate(`/blog/${post.id}`)} // Navigate to blog details
                    >
                      Read More →
                    </button>
                  </div>
                </div>
              </motion.article>
            ))}
          </div>
        ) : (
          <div className="text-center text-gray-400 text-lg">
            No articles available
          </div>
        )}

        {/* Toggle Button */}
        {filteredPosts.length > 3 && (
          <div className="text-center mt-12">
            <button
              className="btn-primary"
              onClick={() => setShowAll(!showAll)} // Toggle between showing all and limited posts
            >
              {showAll ? "Show Less" : "Load More Articles"}
            </button>
          </div>
        )}
      </div>
    </main>
  );
};

export default Blog;
